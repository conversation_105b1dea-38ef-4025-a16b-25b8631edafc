<template>
  <div class="agent-plaza-container">
    <div class="plaza-content">
      <header class="plaza-header">
        <div class="header-left">
          <h1 class="plaza-title">AIGC工具坊</h1>
        </div>
        <div class="header-right">
          <div class="my-agents-btn" @click="handleToggleFavorites"
               :class="{ active: showFavoritesOnly }">
            我的收藏
          </div>
        </div>
      </header>

      <!-- 可滚动内容区域 -->
      <div class="scrollable-content">
        <!-- 搜索和筛选区域 -->
        <div class="search-filter-section">
          <div class="search-box">
            <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
            </svg>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索AI工具..."
              class="search-input"
              @input="handleSearch"
            />
          </div>

          <div class="category-filters">
            <div
              v-for="category in categories"
              :key="category.id"
              class="category-tag"
              :class="{ active: currentCategory === category.id }"
              @click="handleCategoryChange(category.id)"
            >
              {{ category.name }}
            </div>
          </div>
        </div>

        <!-- 工具网格 -->
        <div class="tools-grid" v-loading="loading">
          <div
            v-for="tool in filteredTools"
            :key="tool.id"
            class="tool-card"
            @click="openTool(tool)"
          >
            <div class="tool-header">
              <div class="tool-icon">
                <!-- 根据图标类型显示不同内容 -->
                <img
                  v-if="tool.icon_type === 'image' && tool.icon_image_url"
                  :src="getIconUrl(tool.id)"
                  :alt="tool.name"
                  class="tool-image"
                  @error="handleImageError(tool)" />
                <span v-else class="tool-emoji">{{ tool.icon || '🔧' }}</span>
              </div>
              <div class="tool-info">
                <h3 class="tool-name">{{ tool.name }}</h3>
                <p class="tool-description">{{ tool.description }}</p>
              </div>
              <div class="tool-actions">
                <button
                  class="favorite-btn"
                  :class="{ active: tool.is_favorite }"
                  @click.stop="toggleToolFavorite(tool)"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </button>
              </div>
            </div>

            <div class="tool-footer">
              <div class="tool-tags">
                <span
                  v-for="tag in tool.tags"
                  :key="tag"
                  class="tool-tag"
                >
                  {{ tag }}
                </span>
              </div>
              <div class="tool-stats">
                <span class="click-count">{{ tool.click_count || 0 }}次使用</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredTools.length === 0" class="empty-state">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor" opacity="0.3">
            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
          </svg>
          <p>{{ showFavoritesOnly ? '暂无收藏的工具' : '未找到相关工具' }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getCategories, getTools, toggleFavorite, recordClick } from '@/services/aigcService'

// 响应式数据
const searchQuery = ref('')
const currentCategory = ref('all')
const showFavoritesOnly = ref(false)
const loading = ref(false)

// 分类数据
const categories = ref([
  { id: 'all', name: '全部工具' }
])

// 工具数据
const tools = ref([])

// 计算属性：过滤后的工具列表
const filteredTools = computed(() => {
  let result = [...tools.value]

  // 收藏筛选
  if (showFavoritesOnly.value) {
    result = result.filter(tool => tool.is_favorite)
  }

  // 分类筛选
  if (currentCategory.value && currentCategory.value !== 'all') {
    result = result.filter(tool => tool.category_id == currentCategory.value)
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(tool =>
      tool.name.toLowerCase().includes(query) ||
      tool.description.toLowerCase().includes(query) ||
      (tool.tags && tool.tags.some(tag => tag.toLowerCase().includes(query)))
    )
  }

  return result
})

// 数据加载函数
const loadCategories = async () => {
  try {
    const data = await getCategories(true) // 只获取启用的分类
    categories.value = [
      { id: 'all', name: '全部工具' },
      ...data.map(cat => ({ id: cat.id, name: cat.name }))
    ]
  } catch (error) {
    console.error('加载分类失败:', error)
    ElMessage.error('加载分类失败')
  }
}

const loadTools = async () => {
  try {
    loading.value = true
    const options = {
      isActive: true,
      sortBy: 'sort_order'
    }

    if (currentCategory.value !== 'all') {
      options.categoryId = currentCategory.value
    }

    if (searchQuery.value) {
      options.search = searchQuery.value
    }

    const data = await getTools(options)
    tools.value = data.tools || []
  } catch (error) {
    console.error('加载工具失败:', error)
    ElMessage.error('加载工具失败')
  } finally {
    loading.value = false
  }
}

// 方法：打开工具
const openTool = async (tool) => {
  try {
    // 记录点击
    await recordClick(tool.id)
    tool.click_count = (tool.click_count || 0) + 1
  } catch (error) {
    console.error('记录点击失败:', error)
  }

  // 在新标签页打开工具链接
  window.open(tool.url, '_blank')

  ElMessage.success(`正在打开 ${tool.name}`)
}

// 方法：切换收藏状态
const toggleToolFavorite = async (tool) => {
  try {
    const result = await toggleFavorite(tool.id)
    tool.is_favorite = result.is_favorite
    tool.favorite_count = result.favorite_count
    ElMessage.success(result.message)
  } catch (error) {
    console.error('切换收藏失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 方法：处理分类切换
const handleCategoryChange = (categoryId) => {
  currentCategory.value = categoryId
  loadTools()
}

// 方法：处理搜索
const handleSearch = () => {
  loadTools()
}

// 方法：切换收藏筛选
const handleToggleFavorites = () => {
  showFavoritesOnly.value = !showFavoritesOnly.value
}

// 方法：获取图标URL
const getIconUrl = (toolId) => {
  if (!toolId) return ''
  // 使用AIGC工具的图标API端点
  return `/api/aigc-tools/icons/${toolId}`
}

// 方法：处理图片加载错误
const handleImageError = (tool) => {
  console.warn(`工具 ${tool.name} 的图标加载失败，回退到emoji显示`)
  // 将图标类型设置为emoji，这样下次渲染时会显示emoji
  tool.icon_type = 'emoji'
}

// 初始化数据
onMounted(async () => {
  await loadCategories()
  await loadTools()
})

// 生命周期
onMounted(() => {
  console.log('AIGC工具坊页面已加载')
})
</script>

<style scoped>
.agent-plaza-container {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.plaza-content {
  padding: 20px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 40px;
}

/* 页面头部 */
.plaza-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 28px;
  padding-bottom: 14px;
  position: relative;
}

.plaza-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #1677ff, #4096ff);
  border-radius: 3px;
}

.header-left {
  display: flex;
  align-items: center;
}

.plaza-title {
  font-size: 26px;
  font-weight: bold;
  color: #1a1a1a;
  margin: 0;
  position: relative;
  letter-spacing: -0.5px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.my-agents-btn {
  padding: 7px 16px;
  font-size: 14px;
  color: #1677ff;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s;
  border: 1px solid rgba(22, 119, 255, 0.3);
  background: rgba(22, 119, 255, 0.05);
  font-weight: 500;
}

.my-agents-btn:hover {
  background: rgba(22, 119, 255, 0.1);
  border-color: rgba(22, 119, 255, 0.5);
  transform: translateY(-1px);
}

.my-agents-btn.active {
  background: rgba(22, 119, 255, 0.15);
  border-color: rgba(22, 119, 255, 0.6);
}



/* 搜索和筛选区域 */
.search-filter-section {
  margin-bottom: 32px;
}

.search-box {
  position: relative;
  margin-bottom: 20px;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #a0aec0;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 48px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  background: white;
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.category-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.category-tag {
  padding: 8px 16px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  color: #4a5568;
}

.category-tag:hover {
  border-color: #cbd5e0;
  background: #f7fafc;
}

.category-tag.active {
  background: linear-gradient(135deg, #1677ff 0%, #4096ff 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.3);
}

/* 工具网格 */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.tool-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.02);
  border: 1px solid #f1f5f9;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.tool-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border-color: #e2e8f0;
}

.tool-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.tool-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7fafc;
  border-radius: 8px;
  flex-shrink: 0;
}

.tool-emoji {
  font-size: 20px;
}

.tool-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.tool-info {
  flex: 1;
  min-width: 0;
}

.tool-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 4px 0;
}

.tool-description {
  font-size: 13px;
  color: #718096;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.tool-actions {
  flex-shrink: 0;
}

.favorite-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f7fafc;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  color: #a0aec0;
}

.favorite-btn:hover {
  background: #edf2f7;
  color: #718096;
}

.favorite-btn.active {
  background: #fef5e7;
  color: #f6ad55;
}

.tool-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tool-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.tool-tag {
  padding: 2px 8px;
  background: #edf2f7;
  color: #4a5568;
  border-radius: 12px;
  font-size: 11px;
}

.tool-stats {
  font-size: 12px;
  color: #a0aec0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #a0aec0;
}

.empty-state p {
  margin-top: 16px;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .scrollable-content {
    -webkit-overflow-scrolling: touch;
    padding-bottom: 60px;
  }

  .plaza-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .tools-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }

  .category-filters {
    gap: 8px;
  }

  .category-tag {
    padding: 6px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .plaza-content {
    padding: 15px;
  }

  .scrollable-content {
    padding-bottom: 80px;
  }

  .tools-grid {
    grid-template-columns: 1fr;
  }

  .plaza-title {
    font-size: 24px;
  }
}
</style>
