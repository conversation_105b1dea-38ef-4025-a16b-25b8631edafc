"""
简化的认证路由
使用新的简化认证系统：外部验证密码 + 内部令牌管理
"""

import logging
from flask import request, jsonify
from flask_restx import Namespace, Resource, fields
from app.services.auth_service import auth_service
from app.utils.errors import AuthError, ValidationError
from app.utils.error_handlers import log_route_context
from app.core.jwt_manager import jwt_manager

logger = logging.getLogger(__name__)

# 创建命名空间
auth_ns = Namespace('auth', description='认证系统')

# 定义请求和响应模型
login_request_model = auth_ns.model('LoginRequest', {
    'username': fields.String(required=True, description='用户名', example='<EMAIL>'),
    'password': fields.String(required=True, description='密码', example='password123')
})

refresh_request_model = auth_ns.model('RefreshRequest', {
    'refresh_token': fields.String(required=True, description='刷新令牌', example='eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...')
})

token_response_model = auth_ns.model('TokenResponse', {
    'access_token': fields.String(description='访问令牌'),
    'refresh_token': fields.String(description='刷新令牌'),
    'token_type': fields.String(description='令牌类型', example='Bearer'),
    'expires_in': fields.Integer(description='过期时间（秒）', example=1800),
    'user': fields.Raw(description='用户信息')
})

error_response_model = auth_ns.model('ErrorResponse', {
    'error': fields.String(description='错误类型'),
    'error_description': fields.String(description='错误描述'),
    'message': fields.String(description='错误消息')
})


@auth_ns.route('/login')
class Login(Resource):
    @log_route_context
    @auth_ns.expect(login_request_model)
    @auth_ns.response(200, 'Success', token_response_model)
    @auth_ns.response(400, 'Bad Request', error_response_model)
    @auth_ns.response(401, 'Unauthorized', error_response_model)
    def post(self):
        """
        用户登录
        
        使用简化的认证流程：
        1. 外部系统验证用户名密码
        2. 内部系统生成JWT令牌
        3. 返回访问令牌和刷新令牌
        """
        try:
            # 获取请求数据 - 支持JSON和表单数据
            if request.is_json:
                # JSON格式请求
                data = request.get_json()
                if not data:
                    return {
                        'error': 'invalid_request',
                        'error_description': '请求体不能为空'
                    }, 400
                username = data.get('username')
                password = data.get('password')
            else:
                # 表单数据请求（OAuth2标准格式）
                username = request.form.get('username')
                password = request.form.get('password')
            
            # 参数验证
            if not username or not password:
                return {
                    'error': 'invalid_request',
                    'error_description': '用户名和密码不能为空'
                }, 400
            
            logger.info(f"用户登录请求: {username}")
            
            # 调用认证服务
            result = auth_service.login(username, password)
            
            logger.info(f"用户 {username} 登录成功")
            
            return result, 200
            
        except AuthError as e:
            logger.warning(f"认证失败: {e}")
            return {
                'error': 'invalid_grant',
                'error_description': str(e),
                'message': '用户名或密码错误'
            }, 401
            
        except ValidationError as e:
            logger.warning(f"参数验证失败: {e}")
            return {
                'error': 'invalid_request',
                'error_description': str(e),
                'message': '请求参数不正确'
            }, 400
            
        except Exception as e:
            logger.error(f"登录过程中发生错误: {e}")
            return {
                'error': 'server_error',
                'error_description': '服务器内部错误',
                'message': '登录失败，请稍后重试'
            }, 500


@auth_ns.route('/refresh')
class RefreshToken(Resource):
    @log_route_context
    @auth_ns.expect(refresh_request_model)
    @auth_ns.response(200, 'Success', token_response_model)
    @auth_ns.response(400, 'Bad Request', error_response_model)
    @auth_ns.response(401, 'Unauthorized', error_response_model)
    def post(self):
        """
        刷新访问令牌
        
        完全内部处理，无需外部系统参与：
        1. 验证刷新令牌的有效性
        2. 生成新的访问令牌
        3. 可选：轮转刷新令牌
        """
        try:
            # 获取请求数据
            data = request.get_json()
            if not data:
                return {
                    'error': 'invalid_request',
                    'error_description': '请求体不能为空'
                }, 400
            
            refresh_token = data.get('refresh_token')
            
            # 参数验证
            if not refresh_token:
                return {
                    'error': 'invalid_request',
                    'error_description': '刷新令牌不能为空'
                }, 400
            
            logger.debug("刷新令牌请求")
            
            # 调用认证服务刷新令牌
            result = auth_service.refresh_token(refresh_token)
            
            logger.debug("令牌刷新成功")
            
            return result, 200
            
        except AuthError as e:
            logger.warning(f"令牌刷新失败: {e}")
            return {
                'error': 'invalid_grant',
                'error_description': str(e),
                'message': '刷新令牌无效或已过期'
            }, 401
            
        except ValidationError as e:
            logger.warning(f"参数验证失败: {e}")
            return {
                'error': 'invalid_request',
                'error_description': str(e),
                'message': '请求参数不正确'
            }, 400
            
        except Exception as e:
            logger.error(f"刷新令牌过程中发生错误: {e}")
            return {
                'error': 'server_error',
                'error_description': '服务器内部错误',
                'message': '令牌刷新失败，请稍后重试'
            }, 500


@auth_ns.route('/logout')
class Logout(Resource):
    @log_route_context
    @auth_ns.expect(refresh_request_model)
    @auth_ns.response(200, 'Success')
    @auth_ns.response(400, 'Bad Request', error_response_model)
    def post(self):
        """
        用户登出
        
        清除服务器端的刷新令牌，使其失效
        """
        try:
            # 获取请求数据
            data = request.get_json()
            if not data:
                return {
                    'error': 'invalid_request',
                    'error_description': '请求体不能为空'
                }, 400

            refresh_token = data.get('refresh_token')

            # 参数验证
            if not refresh_token:
                return {
                    'error': 'invalid_request',
                    'error_description': '刷新令牌不能为空'
                }, 400

            logger.debug("用户登出请求")

            # 获取访问令牌（从Authorization头）
            access_token = None
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                access_token = auth_header[7:]  # 移除 "Bearer " 前缀

            # 撤销令牌（添加到黑名单）
            tokens_revoked = []

            # 撤销访问令牌
            if access_token:
                try:
                    if jwt_manager.revoke_token(access_token):
                        tokens_revoked.append('access_token')
                        logger.info("访问令牌已撤销")
                    else:
                        logger.warning("访问令牌撤销失败")
                except Exception as e:
                    logger.error(f"撤销访问令牌时发生错误: {e}")

            # 撤销刷新令牌
            try:
                if jwt_manager.revoke_token(refresh_token):
                    tokens_revoked.append('refresh_token')
                    logger.info("刷新令牌已撤销")
                else:
                    logger.warning("刷新令牌撤销失败")
            except Exception as e:
                logger.error(f"撤销刷新令牌时发生错误: {e}")

            # 调用认证服务登出（保持原有逻辑）
            success = auth_service.logout(refresh_token)

            if success:
                logger.debug("用户登出成功")
                # 构建响应消息
                message = '登出成功'
                if tokens_revoked:
                    message += f'，已撤销令牌: {", ".join(tokens_revoked)}'

                return {
                    'message': message
                }, 200
            else:
                logger.warning("用户登出失败")
                return {
                    'error': 'invalid_request',
                    'error_description': '登出失败',
                    'message': '无效的刷新令牌'
                }, 400
                
        except Exception as e:
            logger.error(f"登出过程中发生错误: {e}")
            return {
                'error': 'server_error',
                'error_description': '服务器内部错误',
                'message': '登出失败，请稍后重试'
            }, 500


@auth_ns.route('/verify')
class VerifyToken(Resource):
    @auth_ns.response(200, 'Success')
    @auth_ns.response(401, 'Unauthorized', error_response_model)
    def get(self):
        """
        验证访问令牌
        
        检查Authorization头中的Bearer令牌是否有效
        """
        try:
            # 获取Authorization头
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                return {
                    'error': 'invalid_token',
                    'error_description': '缺少或无效的Authorization头',
                    'message': '请提供有效的访问令牌'
                }, 401
            
            # 提取访问令牌
            access_token = auth_header[7:]  # 移除 "Bearer " 前缀
            
            # 验证访问令牌
            payload = auth_service.verify_access_token(access_token)

            if not payload:
                return {
                    'error': 'invalid_token',
                    'error_description': '访问令牌无效或已过期',
                    'message': '请重新登录或刷新令牌'
                }, 401

            # 获取用户信息
            user = auth_service.get_current_user(access_token)
            
            if not user:
                return {
                    'error': 'invalid_token',
                    'error_description': '用户不存在',
                    'message': '令牌对应的用户不存在'
                }, 401
            
            return {
                'valid': True,
                'user': user.to_dict(),
                'token_info': {
                    'user_id': payload.get('user_id'),
                    'username': payload.get('username'),
                    'role': payload.get('role'),
                    'exp': payload.get('exp'),
                    'iat': payload.get('iat')
                }
            }, 200
            
        except Exception as e:
            logger.error(f"验证令牌过程中发生错误: {e}")
            return {
                'error': 'server_error',
                'error_description': '服务器内部错误',
                'message': '令牌验证失败'
            }, 500
