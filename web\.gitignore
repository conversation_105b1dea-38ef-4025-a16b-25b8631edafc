# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
.DS_Store
dist
dist-ssr
coverage
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

*.tsbuildinfo

.cursor/

# Added by Task Master AI
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
.vscode
# OS specific

.cursor/
.taskmaster/
.env.example
.roomodes
.windsurfrules
.roo/
.promptx/