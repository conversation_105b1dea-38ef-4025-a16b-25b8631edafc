import { defineStore } from 'pinia'
import { ref } from 'vue'

/**
 * 智能体管理 Store
 * 负责管理智能体应用平台相关状态
 */
export const useAgentStore = defineStore('agent', () => {
  // 所有智能体列表
  const agents = ref([])
  
  // 推荐智能体列表
  const featuredAgents = ref([])
  
  // 加载状态
  const isLoading = ref(false)
  
  // 智能体分类列表 - 从后端获取
  const categories = ref([])
  
  /**
   * 设置所有智能体数据
   * @param {Array} data - 智能体数据列表
   */
  function setAgents(data) {
    agents.value = data
  }
  
  /**
   * 设置推荐智能体数据
   * @param {Array} data - 推荐智能体数据列表
   */
  function setFeaturedAgents(data) {
    featuredAgents.value = data
  }
  
  /**
   * 设置加载状态
   * @param {boolean} status - 是否正在加载
   */
  function setLoadingStatus(status) {
    isLoading.value = status
  }

  /**
   * 设置分类数据
   * @param {Array} data - 分类数据列表
   */
  function setCategories(data) {
    if (Array.isArray(data) && data.length > 0) {
      categories.value = data
    }
  }
  
  /**
   * 获取指定分类的智能体
   * @param {string} categoryId - 分类ID
   * @returns {Array} - 过滤后的智能体列表
   */
  function getAgentsByCategory(categoryId) {
    if (!categoryId) return agents.value
    return agents.value.filter(agent => agent.category === categoryId)
  }
  
  /**
   * 搜索智能体
   * @param {string} query - 搜索关键词
   * @returns {Array} - 搜索结果
   */
  function searchAgents(query) {
    if (!query) return agents.value
    query = query.toLowerCase()
    return agents.value.filter(
      agent => agent.name.toLowerCase().includes(query) || 
              agent.description.toLowerCase().includes(query)
    )
  }
  
  /**
   * 清空状态
   */
  function clearState() {
    agents.value = []
    featuredAgents.value = []
    isLoading.value = false
    categories.value = []
  }
  
  return {
    agents,
    featuredAgents,
    isLoading,
    categories,
    setAgents,
    setFeaturedAgents,
    setLoadingStatus,
    setCategories,
    getAgentsByCategory,
    searchAgents,
    clearState
  }
}) 