<template>
  <aside class="sidebar">
    <div class="sidebar-header">
      <div class="avatar-logo">
        <img src="../../assets/svg/logo.svg" alt="普米智能体" class="logo-img" draggable="false" />
      <div class="logo-text">
        <h1 class="logo-title">普米智能体</h1>
          <p class="logo-subtitle">让学习与工作更轻松</p>
        </div>
      </div>
      
      <!-- 使用SidebarMenu组件 -->
      <SidebarMenu 
        :active-menu="activeMenu"
        :has-user-sent-message="hasUserSentMessage"
        @create-new-chat="$emit('create-new-chat')"
        @menu-select="handleMenuSelect"
      />
    </div>
    
    <!-- 历史对话列表区域 -->
    <div class="sidebar-content">
      <!-- 使用HistoryList组件 -->
      <HistoryList 
        :chat-list="chatList"
        @select-chat="selectChat"
      />
    </div>
  </aside>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// 导入子组件
import SidebarMenu from '@/components/Sidebar/SidebarMenu.vue'
import HistoryList from '@/components/Sidebar/HistoryList.vue'

const router = useRouter()

// 定义props
const props = defineProps({
  chatList: {
    type: Array,
    default: () => []
  },
  hasUserSentMessage: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits(['create-new-chat', 'select-chat', 'menu-select'])

const activeMenu = ref('')  // 默认不选中任何菜单项

// 处理子组件的菜单选择事件
const handleMenuSelect = (index) => {
  activeMenu.value = index
  emit('menu-select', index)
  // 添加路由导航
  router.push('/' + index)
}

// 切换历史对话
const selectChat = (id) => {
  emit('select-chat', id)
}
</script>

<style scoped>
.sidebar {
  width: 280px;
  background: #fafbfc;
  border-right: 1px solid #ececec;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 8px rgba(0,0,0,0.03);
  height: 100vh; /* 确保侧边栏高度为整个视口高度 */
}

.sidebar-header {
  flex-shrink: 0; /* 不收缩 */
  display: flex;
  flex-direction: column;
}

.sidebar-content {
  flex: 1; /* 填充剩余空间 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 隐藏溢出内容 */
}

.avatar-logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 28px 15px 16px 31px;
  gap: 12px;
}

.logo-img {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.logo-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.logo-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  white-space: nowrap;
}

.logo-subtitle {
  font-size: 12px;
  color: #666;
  margin: 0;
  white-space: nowrap;
  font-weight: normal;
}
</style> 