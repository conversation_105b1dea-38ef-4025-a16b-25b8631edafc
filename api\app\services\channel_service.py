"""
渠道和模型服务
处理渠道和模型的业务逻辑
"""

from typing import List, Optional, Dict, Any
from sqlalchemy import and_
from app.models.channel import ChannelList, ModelList
from app import db


class ChannelService:
    """渠道服务类"""
    
    @staticmethod
    def get_channel_list() -> List[dict]:
        """获取所有渠道列表"""
        channels = ChannelList.query.all()
        return [
            {
                'channel_id': channel.channel_id,
                'channel_name': channel.channel_name,
                'channel_url': channel.channel_url,
                'channel_api_key': channel.channel_api_key,
                'channel_api_type': channel.channel_api_type,
                'channel_api_enabled': channel.channel_api_enabled
            }
            for channel in channels
        ]
    
    @staticmethod
    def get_channel_by_id(channel_id: int) -> Optional[ChannelList]:
        """根据ID获取渠道"""
        return ChannelList.query.filter_by(channel_id=channel_id).first()
    
    @staticmethod
    def create_channel(channel_name: str, channel_url: str, channel_api_key: str, channel_api_type: str) -> ChannelList:
        """创建新渠道"""
        channel = ChannelList(
            channel_name=channel_name,
            channel_url=channel_url,
            channel_api_key=channel_api_key,
            channel_api_type=channel_api_type,
            channel_api_enabled=True
        )
        db.session.add(channel)
        db.session.commit()
        return channel
    
    @staticmethod
    def delete_channel(channel_id: int) -> bool:
        """删除渠道及其所有模型"""
        channel = ChannelList.query.get(channel_id)
        if not channel:
            return False
        
        # 删除关联的模型
        ModelList.query.filter_by(channel_id=channel_id).delete()
        # 删除渠道
        db.session.delete(channel)
        db.session.commit()
        return True
    
    @staticmethod
    def change_channel_status(channel_id: int) -> bool:
        """切换渠道启用/禁用状态"""
        channel = ChannelList.query.filter_by(channel_id=channel_id).first()
        if not channel:
            return False
        
        channel.channel_api_enabled = not channel.channel_api_enabled
        db.session.commit()
        return True


class ModelService:
    """模型服务类"""
    
    @staticmethod
    def get_model_list() -> List[dict]:
        """获取所有启用的模型列表（只包括启用渠道的启用模型）"""
        models = ModelList.query.filter(
            and_(
                ModelList.model_enabled == True,
                ModelList.channel_id.in_(
                    db.session.query(ChannelList.channel_id).filter(
                        ChannelList.channel_api_enabled == True
                    )
                )
            )
        ).all()

        return [
            {
                'model_id': model.model_id,
                'channel_id': model.channel_id,
                'model_name': model.model_name,
                'model_enabled': model.model_enabled
            }
            for model in models
        ]
    
    @staticmethod
    def get_channel_model_list(channel_id: int) -> Optional[List[dict]]:
        """获取指定渠道的所有模型"""
        channel = ChannelList.query.filter_by(channel_id=channel_id).first()
        if not channel:
            return None

        models = ModelList.query.filter_by(channel_id=channel_id).all()
        return [
            {
                'model_id': model.model_id,
                'channel_id': model.channel_id,
                'model_name': model.model_name,
                'model_enabled': model.model_enabled
            }
            for model in models
        ]
    
    @staticmethod
    def add_models(channel_id: int, model_names: List[str]) -> List[dict]:
        """
        添加模型到指定渠道
        
        Args:
            channel_id: 渠道ID
            model_names: 模型名称列表
            
        Returns:
            创建的模型列表
            
        Raises:
            ValueError: 如果渠道不存在或模型已存在
        """
        # 检查渠道是否存在
        channel = ChannelService.get_channel_by_id(channel_id)
        if not channel:
            raise ValueError(f"渠道不存在: {channel_id}")
        
        # 检查模型是否已存在
        existing_models = ModelList.query.filter(
            ModelList.channel_id == channel_id,
            ModelList.model_name.in_(model_names)
        ).all()
        
        if existing_models:
            existing_names = [model.model_name for model in existing_models]
            raise ValueError(f"以下模型已存在: {', '.join(existing_names)}")
        
        # 创建模型
        models = []
        for model_name in model_names:
            model = ModelList(
                model_name=model_name,
                channel_id=channel_id,
                model_enabled=True
            )
            db.session.add(model)
            models.append(model)

        db.session.commit()

        # 返回字典格式
        return [
            {
                'model_id': model.model_id,
                'channel_id': model.channel_id,
                'model_name': model.model_name,
                'model_enabled': model.model_enabled
            }
            for model in models
        ]
    
    @staticmethod
    def delete_model(channel_id: int, model_name: str) -> bool:
        """删除指定模型"""
        model = ModelList.query.filter(
            and_(
                ModelList.channel_id == channel_id,
                ModelList.model_name == model_name
            )
        ).first()
        
        if not model:
            return False
        
        db.session.delete(model)
        db.session.commit()
        return True
    
    @staticmethod
    def change_model_status(channel_id: int, model_name: str) -> bool:
        """切换模型启用/禁用状态"""
        model = ModelList.query.filter(
            and_(
                ModelList.channel_id == channel_id,
                ModelList.model_name == model_name
            )
        ).first()
        
        if not model:
            return False
        
        model.model_enabled = not model.model_enabled
        db.session.commit()
        return True
    
    @staticmethod
    def check_model_enabled(channel_id: int, model_name: str) -> bool:
        """检查模型是否启用"""
        model = ModelList.query.filter(
            and_(
                ModelList.channel_id == channel_id,
                ModelList.model_name == model_name
            )
        ).first()
        
        if not model:
            return False
        
        return model.model_enabled 