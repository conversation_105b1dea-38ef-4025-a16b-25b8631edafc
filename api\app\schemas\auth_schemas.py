"""
认证API模型定义
定义登录、令牌响应、用户信息等API模型
"""

from flask_restx import fields, Namespace

# 创建一个临时命名空间用于定义模型
_temp_ns = Namespace('schemas')

# 登录请求模型
login_model = _temp_ns.model('LoginRequest', {
    'username': fields.String(required=True, description='用户名（用户编码）'),
    'password': fields.String(required=True, description='密码')
})

# 令牌响应模型
token_response_model = _temp_ns.model('TokenResponse', {
    'access_token': fields.String(description='访问令牌'),
    'refresh_token': fields.String(description='刷新令牌'),
    'token_type': fields.String(description='令牌类型'),
    'expires_in': fields.Integer(description='过期时间（秒）')
})

# 用户信息模型
user_info_model = _temp_ns.model('UserInfo', {
    'user_code': fields.String(description='用户编码'),
    'username': fields.String(description='用户名'),
    'role': fields.String(description='用户角色')
}) 