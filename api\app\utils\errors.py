"""
自定义错误类型定义
"""

class APIError(Exception):
    """API错误基类"""
    def __init__(self, message: str, status_code: int = 500):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

class InvalidCredentials(APIError):
    """无效的凭证"""
    def __init__(self, message: str = "用户名或密码错误"):
        super().__init__(message, 401)

class TokenError(APIError):
    """令牌错误"""
    def __init__(self, message: str = "无效的令牌"):
        super().__init__(message, 401)

class UserBanned(APIError):
    """用户被禁用"""
    def __init__(self, message: str = "用户已被禁用"):
        super().__init__(message, 403)

class AuthError(APIError):
    """认证服务错误"""
    def __init__(self, message: str = "认证服务错误"):
        super().__init__(message, 401)

class ValidationError(APIError):
    """参数验证错误"""
    def __init__(self, message: str = "参数验证失败"):
        super().__init__(message, 400)

class ServerError(APIError):
    """服务器内部错误"""
    def __init__(self, message: str = "服务器内部错误"):
        super().__init__(message, 500)

class PermissionDenied(APIError):
    """权限不足"""
    def __init__(self, message: str = "权限不足"):
        super().__init__(message, 403)

class ResourceNotFound(APIError):
    """资源未找到"""
    def __init__(self, resource: str = "资源", message: str = None):
        if message is None:
            message = f"{resource}不存在或已被删除"
        super().__init__(message, 404)

class ChannelConnectionError(APIError):
    """渠道连接错误"""
    def __init__(self, channel_type: str = None, details: str = None):
        message = "渠道连接失败"
        if channel_type:
            message = f"{channel_type}渠道连接失败"
        if details:
            message = f"{message}: {details}"
        super().__init__(message, 503)

class ChannelAuthError(APIError):
    """渠道认证错误"""
    def __init__(self, channel_type: str = None, details: str = None):
        message = "渠道认证失败"
        if channel_type:
            message = f"{channel_type}渠道认证失败"
        if details:
            message = f"{message}: {details}"
        super().__init__(message, 401)

class ChannelModelError(APIError):
    """渠道模型错误"""
    def __init__(self, channel_type: str = None, model_name: str = None):
        message = "模型不可用"
        if channel_type and model_name:
            message = f"{channel_type}渠道的{model_name}模型不可用"
        elif channel_type:
            message = f"{channel_type}渠道模型不可用"
        super().__init__(message, 400)

class ChannelRateLimitError(APIError):
    """渠道限流错误"""
    def __init__(self, channel_type: str = None, details: str = None):
        message = "请求过于频繁"
        if channel_type:
            message = f"{channel_type}渠道请求过于频繁"
        if details:
            message = f"{message}: {details}"
        super().__init__(message, 429)

class ChannelTimeoutError(APIError):
    """渠道超时错误"""
    def __init__(self, channel_type: str = None, timeout: int = None):
        message = "请求超时"
        if channel_type and timeout:
            message = f"{channel_type}渠道请求超时({timeout}秒)"
        elif channel_type:
            message = f"{channel_type}渠道请求超时"
        super().__init__(message, 504) 