/**
 * 统一存储工具 - 解决Pinia与localStorage双重存储矛盾
 * 基于马克思主义矛盾论：建立对立统一的存储机制
 */

class UnifiedStorage {
  constructor() {
    this.cache = new Map();
    this.listeners = new Map();
  }

  /**
   * 设置存储值
   * @param {string} key - 存储键
   * @param {*} value - 存储值（会被JSON序列化）
   * @param {Object} options - 配置选项
   * @param {boolean} options.notify - 是否通知监听器，默认true
   */
  set(key, value, options = { notify: true }) {
    try {
      // 更新内存缓存
      this.cache.set(key, value);

      // 更新持久化存储
      localStorage.setItem(key, JSON.stringify(value));

      // 通知监听器
      if (options.notify) {
        this.notify(key, value);
      }
    } catch (error) {
      console.error('统一存储设置失败:', error);
    }
  }

  /**
   * 获取存储值
   * @param {string} key - 存储键
   * @param {*} defaultValue - 默认值
   * @returns {*} 存储值或默认值
   */
  get(key, defaultValue = null) {
    try {
      // 优先从内存缓存获取
      if (this.cache.has(key)) {
        return this.cache.get(key);
      }

      // 从持久化存储恢复
      const stored = localStorage.getItem(key);
      if (stored !== null) {
        const value = JSON.parse(stored);
        this.cache.set(key, value);
        return value;
      }

      return defaultValue;
    } catch (error) {
      console.error('统一存储获取失败:', error);
      return defaultValue;
    }
  }

  /**
   * 删除存储值
   * @param {string} key - 存储键
   */
  remove(key) {
    this.cache.delete(key);
    localStorage.removeItem(key);
    this.notify(key, null);
  }

  /**
   * 检查键是否存在
   * @param {string} key - 存储键
   * @returns {boolean}
   */
  has(key) {
    return this.cache.has(key) || localStorage.getItem(key) !== null;
  }

  /**
   * 订阅存储变化
   * @param {string} key - 存储键
   * @param {Function} callback - 回调函数
   * @returns {Function} 取消订阅函数
   */
  subscribe(key, callback) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set());
    }
    this.listeners.get(key).add(callback);
    
    return () => {
      if (this.listeners.has(key)) {
        this.listeners.get(key).delete(callback);
        if (this.listeners.get(key).size === 0) {
          this.listeners.delete(key);
        }
      }
    };
  }

  /**
   * 通知监听器
   * @param {string} key - 存储键
   * @param {*} value - 新值
   */
  notify(key, value) {
    if (this.listeners.has(key)) {
      this.listeners.get(key).forEach(callback => {
        try {
          callback(value, key);
        } catch (error) {
          console.error('存储监听器执行失败:', error);
        }
      });
    }
  }

  /**
   * 清空所有存储
   */
  clear() {
    this.cache.clear();
    localStorage.clear();
    
    // 通知所有监听器
    this.listeners.forEach((callbacks, key) => {
      callbacks.forEach(callback => callback(null, key));
    });
  }

  /**
   * 获取所有存储键
   * @returns {string[]} 存储键数组
   */
  keys() {
    const keys = new Set([...Object.keys(localStorage), ...this.cache.keys()]);
    return Array.from(keys);
  }

  /**
   * 批量设置存储值
   * @param {Object} data - 键值对对象
   */
  setBatch(data) {
    Object.entries(data).forEach(([key, value]) => {
      this.set(key, value, { notify: false });
    });
    
    // 批量通知
    Object.entries(data).forEach(([key, value]) => {
      this.notify(key, value);
    });
  }
}

// 创建全局单例实例
const unifiedStorage = new UnifiedStorage();

// 导出实例和使用工具
export default unifiedStorage;

// 快捷工具函数
export const useStorage = (key, defaultValue = null) => {
  const value = ref(unifiedStorage.get(key, defaultValue));
  
  const updateValue = (newValue) => {
    unifiedStorage.set(key, newValue);
    value.value = newValue;
  };
  
  const removeValue = () => {
    unifiedStorage.remove(key);
    value.value = defaultValue;
  };
  
  // 订阅变化
  const unsubscribe = unifiedStorage.subscribe(key, (newValue) => {
    value.value = newValue ?? defaultValue;
  });
  
  // 组件卸载时取消订阅
  onUnmounted(unsubscribe);
  
  return {
    value: readonly(value),
    set: updateValue,
    remove: removeValue,
    subscribe: unifiedStorage.subscribe.bind(unifiedStorage)
  };
};

// 从vue导入需要的函数
import { ref, readonly, onUnmounted } from 'vue';