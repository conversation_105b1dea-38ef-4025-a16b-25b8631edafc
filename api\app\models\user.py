"""
用户模型
包含用户基本信息，支持学生/教师/管理员角色，使用外部认证系统
"""

from datetime import datetime, timezone
from app import db


class User(db.Model):
    """用户模型"""
    __tablename__ = 'User'
    
    user_id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键ID')
    
    user_code = db.Column(
        db.CHAR(7), 
        unique=True, 
        nullable=False, 
        index=True,
        comment='用户编码'
    )
    username = db.Column(
        db.String(50), 
        nullable=False, 
        index=True,
        comment='用户名'
    )
    role = db.Column(
        db.String(20),
        default='student',
        nullable=False,
        comment='用户角色'
    )
    external_user_id = db.Column(
        db.String(100),
        nullable=True,
        index=True,
        comment='外部系统用户ID'
    )
    external_token = db.Column(
        db.Text,
        nullable=True,
        comment='外部系统访问令牌'
    )
    internal_refresh_token = db.Column(
        db.Text,
        nullable=True,
        comment='内部刷新令牌，用于本地令牌刷新（已废弃）'
    )
    refresh_token_hash = db.Column(
        db.String(64),
        nullable=True,
        comment='刷新令牌哈希值，用于新的令牌管理系统'
    )
    token_expires_at = db.Column(
        db.DateTime,
        nullable=True,
        comment='令牌过期时间'
    )
    client_fp = db.Column(
        db.String(100),
        nullable=True,
        comment='客户端指纹'
    )
    session_id = db.Column(
        db.Text,
        nullable=True,
        comment='外部认证会话ID'
    )
    disabled = db.Column(
        db.Boolean, 
        default=False, 
        nullable=False,
        comment='是否禁用'
    )
    last_login_at = db.Column(
        db.DateTime,
        nullable=True,
        comment='最后登录时间'
    )
    created_at = db.Column(
        db.DateTime, 
        nullable=False, 
        default=lambda: datetime.now(timezone.utc),
        comment='创建时间'
    )
    
    # 关联关系
    conversations = db.relationship('Conversation', back_populates='user')
    files = db.relationship('File', back_populates='user')

    @property 
    def id(self):
        """为了兼容性，提供id属性"""
        return self.user_id

    @property
    def is_admin(self):
        """检查是否为管理员"""
        return self.role == 'admin'
    
    @property
    def is_teacher(self):
        """检查是否为教师"""
        return self.role == 'teacher'
    
    @property
    def is_student(self):
        """检查是否为学生"""
        return self.role == 'student'

    @property
    def is_active(self):
        """检查用户是否激活（与disabled相反）"""
        return not self.disabled

    def to_dict(self, include_sensitive=False):
        """转换为字典格式"""
        exclude_fields = ['external_token', 'internal_refresh_token', 'client_fp', 'session_id']
        
        result = {}
        for column in self.__table__.columns:
            field_name = column.name
            if not include_sensitive and field_name in exclude_fields:
                continue
                
            value = getattr(self, field_name)
            # 处理datetime类型
            if isinstance(value, datetime):
                value = value.isoformat() + 'Z'
            result[field_name] = value
        
        # 添加计算字段
        result['is_admin'] = self.is_admin
        result['is_teacher'] = self.is_teacher
        result['is_student'] = self.is_student
        result['is_active'] = self.is_active
        
        return result

    def __repr__(self):
        return f'<User {self.username}({self.user_code})>' 