"""文件解析服务，从不同类型的文件中提取文本内容"""
import os

class FileParser:
    """文件解析服务，用于从不同类型的文件中提取文本内容"""
    
    def __init__(self):
        """初始化文件解析服务"""
        pass
    
    def parse_file(self, file_path: str, file_type: str) -> str:
        """
        根据文件类型解析文件内容
        
        Args:
            file_path: 文件路径
            file_type: 文件类型
            
        Returns:
            提取的文本内容
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        file_type = file_type.lower()
        
        # 根据文件类型调用对应的解析方法
        if file_type == "txt":
            return self.parse_text_file(file_path)
        elif file_type == "pdf":
            return self.parse_pdf_file(file_path)
        elif file_type == "docx":
            return self.parse_docx_file(file_path)
        elif file_type == "md":
            return self.parse_text_file(file_path)  # Markdown本身就是文本格式
        else:
            # 不支持的文件类型返回简单消息
            return f"不支持解析的文件类型: {file_type}"
    
    def parse_text_file(self, file_path: str) -> str:
        """解析纯文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # 如果UTF-8解码失败，尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    return f.read()
            except Exception as e:
                raise ValueError(f"解析文本文件失败: {e}")
        except Exception as e:
            raise ValueError(f"解析文本文件失败: {e}")
    
    def parse_pdf_file(self, file_path: str) -> str:
        """解析PDF文件"""
        try:
            import fitz  # PyMuPDF
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            return text
        except ImportError:
            raise ImportError("未安装PyMuPDF，无法解析PDF文件")
        except Exception as e:
            raise ValueError(f"解析PDF文件失败: {e}")
    
    def parse_docx_file(self, file_path: str) -> str:
        """解析DOCX文件"""
        try:
            import mammoth
            from markdownify import markdownify as md
            with open(file_path, "rb") as docx_file:
                html = mammoth.convert_to_html(docx_file).value
                markdown = md(html)
            return markdown
        except ImportError:
            raise ImportError("未安装mammoth或markdownify，无法解析DOCX文件")
        except Exception as e:
            raise ValueError(f"解析DOCX文件失败: {e}") 