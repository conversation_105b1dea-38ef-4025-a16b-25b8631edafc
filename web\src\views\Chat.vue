<template>
  <div class="chat-container">
    <Sidebar></Sidebar>
    <div class="chat-content">
      <AIChatHeader></AIChatHeader>
      <div class="main-center">
        <RouterView></RouterView>
      </div>
    </div>
  </div>
</template>

<script setup>
import { RouterView } from 'vue-router'
import AIChatHeader from '@/components/AIChatHeader.vue';
import Sidebar from '@/components/Sidebar.vue';
</script>

<style scoped lang="scss">
.chat-container {
  display: flex;
  height: 100vh;
  background: #fff;

  .chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #fff;
    overflow: hidden;

    .main-center {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-top: 0;
      padding-bottom: 0;
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      overflow: auto;
    }
  }
}
</style>
