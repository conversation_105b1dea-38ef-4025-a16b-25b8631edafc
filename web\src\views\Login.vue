<template>
  <div class="login-container">
    <div class="login-wrapper">
      <div class="login-brand">
        <div class="brand-content">
          <img src="../assets/svg/logo.svg" alt="普米智能体 Logo" class="logo" draggable="false">
<h1 class="brand-title">普米智能体</h1>
          <p class="brand-tagline">智能对话，无限可能</p>
        </div>
        <div class="decoration-circles">
          <div class="circle circle-1"></div>
          <div class="circle circle-2"></div>
          <div class="circle circle-3"></div>
        </div>
      </div>

      <div class="login-form-container">
        <div class="login-form">
          <h2 class="form-title">欢迎回来</h2>
          <p class="form-subtitle">请登录您的账号</p>

          <el-form :model="form" :rules="rules" ref="loginFormRef" class="form-content">
            <el-form-item prop="username">
              <el-input v-model="form.username" placeholder="请输入用户名" size="large" prefix-icon="User"
                @keyup.enter="handleLogin" />
            </el-form-item>

            <el-form-item prop="password">
              <el-input v-model="form.password" type="password" placeholder="请输入密码" size="large" prefix-icon="Lock"
                show-password @keyup.enter="handleLogin" />
            </el-form-item>

            <el-form-item>
              <div class="form-options">
                <el-checkbox v-model="rememberMe">记住我</el-checkbox>
                <el-link type="primary" :underline="false">忘记密码？</el-link>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" size="large" class="login-button" :loading="loading" @click="handleLogin">
                {{ loading ? '登录中...' : '登录' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/authStore';
import { useRouter, useRoute } from 'vue-router'
import { useApiStore } from '@/stores/apiStore';
import * as tokenService from '@/services/tokenService';

// 登录表单获取的数据
const form = reactive({
  username: '',
  password: ''
})

// 用户名和密码最大输入长度
const rules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ]
})


const loginFormRef = ref()
const loading = ref(false)
const rememberMe = ref(false)
const authStore = useAuthStore()
const apiStore = useApiStore()
const router = useRouter()
const route = useRoute()

const handleLogin = async () => {
  try {
    await loginFormRef.value.validate()
  } catch {
    ElMessage.warning('请填写正确的用户名和密码')
    return
  }
  loading.value = true

  try {
    // 调用 apiStore 的 login 方法
    const response = await apiStore.login(form.username, form.password)
    ElMessage.success('登录成功')
    
    // 检查令牌是否存在
    if(tokenService.getAccessToken() && tokenService.getRefreshToken()){
      // 如果有重定向参数，导航到该位置
      const redirectPath = route.query.redirect || '/chat'
      router.push({
        path: redirectPath,
        replace: true
      })
    }
  } catch (err) {
    ElMessage.error(err.response?.data?.message || '登录失败,请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-wrapper {
  display: flex;
  width: 100%;
  max-width: 1200px;
  height: 600px;
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Left side - Branding */
.login-brand {
  flex: 1;
  background: linear-gradient(135deg, #2A82E4 0%, #1e6bc8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.brand-content {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;
}

.logo {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.brand-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 10px 0;
  letter-spacing: -1px;
}

.brand-tagline {
  font-size: 18px;
  opacity: 0.9;
  font-weight: 300;
}

/* Decorative circles */
.decoration-circles {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -100px;
}

.circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -50px;
}

.circle-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  right: 10%;
}

/* Right side - Login form */
.login-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form {
  width: 100%;
  max-width: 400px;
}

.form-title {
  font-size: 32px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px 0;
}

.form-subtitle {
  font-size: 16px;
  color: #909399;
  margin: 0 0 40px 0;
}

.form-content {
  width: 100%;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(42, 130, 228, 0.3);
}

.login-footer {
  text-align: center;
  margin-top: 30px;
  color: #909399;
}

.login-footer span {
  margin-right: 5px;
}

/* Element Plus customizations */
:deep(.el-input__wrapper) {
  border-radius: 8px;
  height: 48px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #2A82E4;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(42, 130, 228, 0.2);
}

:deep(.el-input__prefix) {
  font-size: 18px;
  color: #909399;
}

:deep(.el-checkbox__label) {
  color: #606266;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
}

/* Responsive design */
@media (max-width: 968px) {
  .login-wrapper {
    flex-direction: column;
    height: auto;
    max-width: 500px;
  }

  .login-brand {
    padding: 60px 40px;
  }

  .brand-title {
    font-size: 36px;
  }

  .logo {
    width: 80px;
    height: 80px;
  }

  .login-form-container {
    padding: 40px;
  }
}

@media (max-width: 480px) {
  .login-form-container {
    padding: 30px 20px;
  }

  .form-title {
    font-size: 24px;
  }

  .form-subtitle {
    font-size: 14px;
  }
}
</style>