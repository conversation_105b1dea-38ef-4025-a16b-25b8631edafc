/**
 * 渠道管理相关的API服务
 */
import axios from 'axios'
import { useApiConfigStore } from '@/stores/apiConfig'

const apiConfig = useApiConfigStore()

/**
 * 获取渠道列表
 * @returns {Promise} 渠道列表
 */
export const getChannelList = async () => {
  try {
    const response = await axios.get(apiConfig.getNestUrl('channels', 'get_channel_list'))
    return response.data
  } catch (error) {
    console.error('获取渠道列表失败:', error)
    throw error
  }
}

/**
 * 创建新渠道
 * @param {Object} channelData 渠道数据
 * @returns {Promise} 创建结果
 */
export const createChannel = async (channelData) => {
  try {
    const response = await axios.post(apiConfig.getNestUrl('channels', 'create'), {
      channel_name: channelData.channel_name,
      channel_url: channelData.channel_url,
      channel_api_key: channelData.channel_api_key,
      channel_api_type: channelData.channel_api_type
    })
    return response.data
  } catch (error) {
    console.error('创建渠道失败:', error)
    throw error
  }
}

/**
 * 删除渠道
 * @param {number} channelId 渠道ID
 * @returns {Promise} 删除结果
 */
export const deleteChannel = async (channelId) => {
  try {
    const response = await axios.post(apiConfig.getNestUrl('channels', 'delete_channel'), {
      channel_id: channelId
    })
    return response.data
  } catch (error) {
    console.error('删除渠道失败:', error)
    throw error
  }
}

/**
 * 切换渠道启用/禁用状态
 * @param {number} channelId 渠道ID
 * @returns {Promise} 操作结果
 */
export const changeChannelStatus = async (channelId) => {
  try {
    const response = await axios.post(apiConfig.getNestUrl('channels', 'change_channel_status'), {
      channel_id: channelId
    })
    return response.data
  } catch (error) {
    console.error('切换渠道状态失败:', error)
    throw error
  }
}

/**
 * 获取渠道统计信息
 * @returns {Promise} 统计信息
 */
export const getChannelStats = async () => {
  try {
    const channels = await getChannelList()
    
    // 计算统计信息
    const stats = {
      totalChannels: channels.length,
      enabledChannels: channels.filter(c => c.channel_api_enabled).length,
      disabledChannels: channels.filter(c => !c.channel_api_enabled).length,
      channelTypes: new Set(channels.map(c => c.channel_api_type)).size
    }
    
    return stats
  } catch (error) {
    console.error('获取渠道统计信息失败:', error)
    throw error
  }
}
