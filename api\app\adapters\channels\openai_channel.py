from typing import AsyncGenerator, List, Dict, Optional, Any
import json
import asyncio
from httpx import get, post, AsyncClient, ConnectTimeout, ReadTimeout
from .base import BaseChannel
from app.utils.errors import (
    ChannelConnectionError,
    ChannelAuthError,
    ChannelModelError,
    ChannelRateLimitError,
    ChannelTimeoutError
)

class OpenAIChannel(BaseChannel):
    """
    OpenAI渠道实现
    适用于与OpenAI API兼容的服务，如官方OpenAI API、Azure OpenAI、通义千问兼容接口等
    """
    
    def __init__(self, base_url: str = None, api_key: str = None, **kwargs):
        """
        初始化OpenAI渠道
        
        Args:
            base_url: OpenAI API的基础URL
            api_key: OpenAI API密钥
            **kwargs: 其他参数
        """
        super().__init__(base_url, api_key, **kwargs)
    
    async def check_connection(self) -> bool:
        """
        检查OpenAI API连接是否正常
        
        Returns:
            如果API连接正常返回True，否则返回False
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            response = get(f"{self.base_url}/models", headers=headers, timeout=5)
            return response.status_code == 200
        except Exception:
            return False

    async def get_available_models(self) -> List[Dict[str, Any]]:
        """
        获取可用的OpenAI模型列表

        Returns:
            可用模型列表，如果获取失败则返回默认模型列表
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            response = get(f"{self.base_url}/models", headers=headers)
            if response.status_code == 200:
                models_data = response.json().get("data", [])
                models = []
                for model in models_data:
                    model_id = model.get("id", "")
                    if model_id:
                        models.append({
                            "model_name": model_id,
                            "model_id": model_id,
                            "channel_id": 0,  # 这会在服务层被正确设置
                            "channel_name": "OpenAI",
                            "channel_type": "openai"
                        })
                if models:
                    return models
        except Exception:
            pass
            
        # 如果API调用失败，返回默认模型列表
        return [
            {
                "model_name": "gpt-3.5-turbo",
                "model_id": "gpt-3.5-turbo",
                "channel_id": 0,
                "channel_name": "OpenAI",
                "channel_type": "openai"
            },
            {
                "model_name": "gpt-4",
                "model_id": "gpt-4",
                "channel_id": 0,
                "channel_name": "OpenAI",
                "channel_type": "openai"
            }
        ]

    async def chat(
        self,
        model_name: str,
        message: str,
        history: List[Dict[str, str]] = None,
        stream: bool = True,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        使用OpenAI API发送聊天请求并获取响应

        Args:
            model_name: 模型名称
            message: 用户消息
            history: 历史消息列表，格式为[{"role": "user/assistant", "content": "消息内容"}]
            stream: 是否使用流式响应
            **kwargs: 额外参数

        Returns:
            生成器，产生响应文本
        """
        # 构建消息列表
        messages = []
        if history:
            messages.extend(history)
        messages.append({"role": "user", "content": message})

        # 提取特殊参数
        enable_thinking = kwargs.get("enable_thinking", False)

        async with AsyncClient() as client:
            url = f"{self.base_url}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": model_name,
                "messages": messages,
                "stream": stream
            }

            # 对于通义千问API，使用正确的参数格式设置思考过程
            if "dashscope" in self.base_url.lower() or "qwen" in model_name.lower():
                # 使用chat_template_kwargs包装enable_thinking参数
                payload["chat_template_kwargs"] = {
                    "enable_thinking": enable_thinking
                }

                if stream:
                    # 添加stream_options参数
                    payload["stream_options"] = {
                        "include_usage": True
                    }

            # 对于其他支持enable_thinking的API，直接设置
            elif enable_thinking:
                payload["enable_thinking"] = enable_thinking
                
            # 添加额外参数，如temperature等
            for key, value in kwargs.items():
                if key not in payload and key != "enable_thinking" and value is not None:
                    payload[key] = value

            try:
                if stream:
                    try:
                        async with client.stream("POST", url, json=payload, headers=headers, timeout=60.0) as response:
                            if response.status_code != 200:
                                if response.status_code == 401:
                                    raise ChannelAuthError("openai")
                                elif response.status_code == 404:
                                    raise ChannelModelError("openai", model_name)
                                elif response.status_code == 429:
                                    raise ChannelRateLimitError("openai")
                                elif response.status_code == 500:
                                    raise ChannelConnectionError("openai", "服务器内部错误，可能是模型加载失败")
                                elif response.status_code == 408:
                                    raise ChannelTimeoutError("openai")
                                elif response.status_code == 403:
                                    raise ChannelAuthError("openai")
                                else:
                                    raise ChannelConnectionError("openai", f"服务器返回非200状态码: {response.status_code}")

                            async for line in response.aiter_lines():
                                if line.strip():
                                    if line.startswith("data: "):
                                        line = line[6:]  # 移除 "data: " 前缀
                                    if line == "[DONE]":
                                        continue
                                    try:
                                        data = json.loads(line)
                                        if "choices" in data and len(data["choices"]) > 0:
                                            delta = data["choices"][0].get("delta", {})
                                            if "content" in delta:
                                                content = delta["content"]
                                                yield content
                                            elif "error" in data:
                                                raise ChannelConnectionError("openai", data["error"])
                                    except json.JSONDecodeError:
                                        raise ChannelConnectionError("openai", f"无法解析JSON响应: {line[:100]}")
                                    except Exception as e:
                                        raise ChannelConnectionError("openai", f"处理流式响应出错: {str(e)}")
                    except ConnectTimeout:
                        raise ChannelTimeoutError("openai", 60)
                    except ReadTimeout:
                        raise ChannelTimeoutError("openai", 60)
                    except Exception as e:
                        error_message = str(e)
                        if "connection" in error_message.lower():
                            raise ChannelConnectionError("openai", f"连接服务器失败: {error_message}")
                        elif "timeout" in error_message.lower():
                            raise ChannelTimeoutError("openai")
                        elif "api key" in error_message.lower() or "apikey" in error_message.lower() or "authorization" in error_message.lower():
                            raise ChannelAuthError("openai")
                        else:
                            raise ChannelConnectionError("openai", error_message)
                else:
                    try:
                        response = await client.post(url, json=payload, headers=headers, timeout=300.0)
                        if response.status_code == 200:
                            data = response.json()
                            if "choices" in data and len(data["choices"]) > 0:
                                content = data["choices"][0]["message"]["content"]
                                yield content
                            else:
                                raise ChannelConnectionError("openai", "响应格式异常，服务返回的数据结构不符合预期")
                        else:
                            if response.status_code == 401:
                                raise ChannelAuthError("openai")
                            elif response.status_code == 404:
                                raise ChannelModelError("openai", model_name)
                            elif response.status_code == 429:
                                raise ChannelRateLimitError("openai")
                            elif response.status_code == 500:
                                raise ChannelConnectionError("openai", "服务器内部错误，可能是模型加载失败")
                            elif response.status_code == 408:
                                raise ChannelTimeoutError("openai")
                            elif response.status_code == 403:
                                raise ChannelAuthError("openai")
                            else:
                                raise ChannelConnectionError("openai", f"服务器返回非200状态码: {response.status_code}")
                    except ConnectTimeout:
                        raise ChannelTimeoutError("openai", 60)
                    except ReadTimeout:
                        raise ChannelTimeoutError("openai", 60)
                    except Exception as e:
                        error_message = str(e)
                        if "connection" in error_message.lower():
                            raise ChannelConnectionError("openai", f"请求失败: {error_message}")
                        elif "timeout" in error_message.lower():
                            raise ChannelTimeoutError("openai")
                        elif "api key" in error_message.lower() or "apikey" in error_message.lower() or "authorization" in error_message.lower():
                            raise ChannelAuthError("openai")
                        else:
                            raise ChannelConnectionError("openai", error_message)
            except Exception as e:
                error_message = str(e)
                if "connection" in error_message.lower():
                    raise ChannelConnectionError("openai", f"无法连接到服务器: {error_message}")
                elif "timeout" in error_message.lower():
                    raise ChannelTimeoutError("openai")
                elif "api key" in error_message.lower() or "apikey" in error_message.lower() or "authorization" in error_message.lower():
                    raise ChannelAuthError("openai")
                else:
                    raise ChannelConnectionError("openai", error_message)

    async def generate_title(self, model_name: str, message: str) -> str:
        """
        根据用户的第一条消息生成对话标题

        Args:
            model_name: 模型名称
            message: 用户的第一条消息

        Returns:
            生成的标题，如果生成失败则返回截断的用户消息作为标题
        """
        prompt = f"""请根据以下用户消息生成一个简短、有意义的对话标题：
要求：
1. 不超过12个字
2. 不要使用任何标点符号
3. 只返回标题，不要其他任何内容

用户消息：
{message}"""

        try:
            full_response = ""
            async for chunk in self.chat(
                model_name=model_name,
                message=prompt,
                stream=False
            ):
                full_response += chunk

            # 处理并返回标题
            if full_response:
                # 移除所有标点符号并限制长度
                title = full_response.strip()
                title = ''.join(char for char in title if not char in '，。！？、；：""''（）【】《》')
                return title[:8]
        except Exception:
            pass

        # 如果生成失败，则使用用户消息的前几个字作为标题
        return message[:8]
