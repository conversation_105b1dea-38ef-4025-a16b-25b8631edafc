"""update_user_role_enum_add_group_leader_dev_operator

Revision ID: 829e2f029216
Revises: 0ed522bc8d4c
Create Date: 2025-07-22 14:14:36.374437

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '829e2f029216'
down_revision = '0ed522bc8d4c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('User', schema=None) as batch_op:
        batch_op.alter_column('role',
               existing_type=mysql.ENUM('student', 'teacher', 'admin', collation='utf8mb4_general_ci'),
               type_=sa.String(length=20),
               existing_comment='用户角色',
               existing_nullable=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('User', schema=None) as batch_op:
        batch_op.alter_column('role',
               existing_type=sa.String(length=20),
               type_=mysql.ENUM('student', 'teacher', 'admin', collation='utf8mb4_general_ci'),
               existing_comment='用户角色',
               existing_nullable=False)

    # ### end Alembic commands ###
