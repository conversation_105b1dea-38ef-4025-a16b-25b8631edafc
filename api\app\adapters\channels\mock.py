"""
模拟渠道类，用于开发和测试
"""
import asyncio
from typing import List, Dict, Any, AsyncGenerator, Optional
from .base import BaseChannel


class MockChannel(BaseChannel):
    """模拟渠道，返回预定义的响应"""
    
    def __init__(self, base_url: str = None, api_key: str = None, **kwargs):
        """初始化模拟渠道"""
        super().__init__(base_url, api_key, **kwargs)
        
    async def chat(
        self,
        model_name: str,
        message: str,
        history: List[Dict[str, str]] = None,
        stream: bool = True,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        模拟聊天响应
        
        Args:
            model_name: 模型名称
            message: 用户消息
            history: 历史消息
            stream: 是否使用流式响应
            **kwargs: 其他参数
            
        Returns:
            模拟的响应生成器
        """
        # 模拟思考时间
        await asyncio.sleep(0.5)
        
        # 构建模拟响应
        response = f"这是来自模拟渠道的响应。\n您选择的模型是: {model_name}\n您的消息是: {message}"
        
        if "enable_thinking" in kwargs and kwargs["enable_thinking"]:
            response = f"思考过程: 分析用户问题...\n\n{response}"
        
        if stream:
            # 模拟流式响应
            words = response.split()
            for word in words:
                await asyncio.sleep(0.1)  # 模拟延迟
                yield word + " "
        else:
            # 非流式响应
            yield response
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """
        获取模拟的可用模型列表
        
        Returns:
            模拟的模型列表
        """
        return [
            {
                "model_name": "mock-gpt-3.5",
                "model_id": 1,
                "channel_id": 999,
                "channel_name": "模拟渠道",
                "channel_type": "mock"
            },
            {
                "model_name": "mock-gpt-4",
                "model_id": 2,
                "channel_id": 999,
                "channel_name": "模拟渠道",
                "channel_type": "mock"
            }
        ]
    
    async def check_connection(self) -> bool:
        """
        检查连接状态
        
        Returns:
            始终返回True
        """
        return True
        
    async def generate_title(self, model_name: str, message: str) -> str:
        """
        根据用户的第一条消息生成对话标题
        
        Args:
            model_name: 模型名称
            message: 用户的第一条消息
            
        Returns:
            生成的标题
        """
        # 简单地截取消息前几个字符作为标题
        if len(message) <= 8:
            return message
            
        # 尝试在句子边界截断
        for boundary in ['.', '。', '!', '！', '?', '？', '\n']:
            first_sentence = message.split(boundary)[0]
            if 3 <= len(first_sentence) <= 8:
                return first_sentence
                
        # 返回前8个字符
        return message[:8] 