<template>
  <div class="model-management">
    <!-- 统计卡片 -->
    <div class="stats-row">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><Cpu /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ totalModels }}</div>
            <div class="stat-label">总模型数</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon enabled">
            <el-icon><Check /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ enabledModels }}</div>
            <div class="stat-label">已启用</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon disabled">
            <el-icon><Close /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ disabledModels }}</div>
            <div class="stat-label">已禁用</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><Connection /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ channelCount }}</div>
            <div class="stat-label">关联渠道</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 模型列表表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>模型列表</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索模型名称"
              style="width: 250px"
              size="small"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>

            <el-select v-model="filterChannel" placeholder="选择渠道" clearable size="small" style="width: 150px">
              <el-option label="全部渠道" value="" />
              <el-option
                v-for="channel in channels"
                :key="channel.channel_id"
                :label="channel.channel_name"
                :value="channel.channel_id"
              />
            </el-select>

            <el-select v-model="filterStatus" placeholder="选择状态" clearable size="small" style="width: 150px">
              <el-option label="全部状态" value="" />
              <el-option label="已启用" value="enabled" />
              <el-option label="已禁用" value="disabled" />
            </el-select>

            <el-button type="primary" :icon="Plus" @click="showAddModelDialog = true" size="small">
              添加模型
            </el-button>

            <el-button :icon="Refresh" @click="refreshData" :loading="loading" size="small">
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="filteredModels" 
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="{ background: '#fafbfc', color: '#606266' }"
      >
        <el-table-column prop="model_name" label="模型名称" min-width="200">
          <template #default="{ row }">
            <div class="model-name">
              <el-icon class="model-icon"><Cpu /></el-icon>
              {{ row.model_name }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="channel_name" label="所属渠道" min-width="150">
          <template #default="{ row }">
            <el-tag :type="getChannelTagType(row.channel_type)" size="small">
              {{ row.channel_name }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="channel_type" label="渠道类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.channel_type)" size="small">
              {{ getTypeDisplayName(row.channel_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="model_enabled" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.model_enabled"
              @change="toggleModelStatus(row)"
              :loading="row.switching"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="danger" 
              size="small" 
              text
              @click="deleteModel(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredModels.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加模型对话框 -->
    <el-dialog v-model="showAddModelDialog" title="添加模型" width="500px">
      <el-form :model="addModelForm" label-width="100px">
        <el-form-item label="选择渠道" required>
          <el-select v-model="addModelForm.channel_id" placeholder="请选择渠道" style="width: 100%">
            <el-option 
              v-for="channel in channels" 
              :key="channel.channel_id"
              :label="`${channel.channel_name} (${channel.channel_type})`" 
              :value="channel.channel_id" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="模型名称" required>
          <el-input
            v-model="addModelForm.model_names"
            type="textarea"
            :rows="4"
            placeholder="请输入模型名称，多个模型用换行分隔&#10;例如：&#10;gpt-3.5-turbo&#10;gpt-4&#10;claude-3-sonnet"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddModelDialog = false">取消</el-button>
          <el-button type="primary" @click="addModels" :loading="addingModel">添加</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Refresh,
  Cpu,
  Check,
  Close,
  Connection
} from '@element-plus/icons-vue'
import * as modelService from '@/services/modelService'

// 定义事件
const emit = defineEmits(['data-updated'])

// 响应式数据
const loading = ref(false)
const models = ref([])
const channels = ref([])
const showAddModelDialog = ref(false)
const addingModel = ref(false)

// 筛选条件
const filterChannel = ref('')
const filterStatus = ref('')
const searchKeyword = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 添加模型表单
const addModelForm = ref({
  channel_id: '',
  model_names: ''
})

// 计算属性
const totalModels = computed(() => models.value.length)
const enabledModels = computed(() => models.value.filter(m => m.model_enabled).length)
const disabledModels = computed(() => models.value.filter(m => !m.model_enabled).length)
const channelCount = computed(() => {
  const channelIds = new Set(models.value.map(m => m.channel_id))
  return channelIds.size
})

// 筛选后的模型列表
const filteredModels = computed(() => {
  let filtered = models.value

  // 按关键词筛选
  if (searchKeyword.value) {
    filtered = filtered.filter(model => 
      model.model_name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 按渠道筛选
  if (filterChannel.value) {
    filtered = filtered.filter(model => model.channel_id === filterChannel.value)
  }

  // 按状态筛选
  if (filterStatus.value) {
    if (filterStatus.value === 'enabled') {
      filtered = filtered.filter(model => model.model_enabled)
    } else if (filterStatus.value === 'disabled') {
      filtered = filtered.filter(model => !model.model_enabled)
    }
  }

  return filtered
})

// 监听统计数据变化，向父组件发送更新事件
watch([totalModels, enabledModels, disabledModels, channelCount], () => {
  emit('data-updated', {
    totalModels: totalModels.value,
    enabledModels: enabledModels.value,
    disabledModels: disabledModels.value,
    channelCount: channelCount.value
  })
}, { immediate: true })

// 获取渠道标签类型
const getChannelTagType = (type) => {
  const typeMap = {
    'openai': 'success',
    'ollama': 'info', 
    'zhipu': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取类型标签类型
const getTypeTagType = (type) => {
  const typeMap = {
    'openai': 'success',
    'ollama': 'info', 
    'zhipu': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取类型显示名称
const getTypeDisplayName = (type) => {
  const nameMap = {
    'openai': 'OpenAI',
    'ollama': 'Ollama',
    'zhipu': '智谱AI'
  }
  return nameMap[type] || type
}

// 刷新数据
const refreshData = async (showMessage = true) => {
  loading.value = true
  try {
    await Promise.all([
      loadModels(),
      loadChannels()
    ])
    if (showMessage) {
      ElMessage.success('模型数据刷新成功')
    }
  } catch (error) {
    console.error('数据刷新失败:', error)
    ElMessage.error(`数据刷新失败: ${error.message || '未知错误'}`)
  } finally {
    loading.value = false
  }
}

// 加载模型数据
const loadModels = async () => {
  try {
    const fullModelData = await modelService.getFullModelData()
    models.value = fullModelData
  } catch (error) {
    console.error('加载模型数据失败:', error)
    throw error
  }
}

// 加载渠道数据
const loadChannels = async () => {
  try {
    const channelData = await modelService.getChannelList()
    channels.value = channelData
  } catch (error) {
    console.error('加载渠道数据失败:', error)
    throw error
  }
}

// 切换模型状态
const toggleModelStatus = async (model) => {
  model.switching = true
  const originalStatus = model.model_enabled

  try {
    await modelService.changeModelStatus(model.channel_id, model.model_name)
    ElMessage.success(`模型 ${model.model_name} 状态已更新`)
  } catch (error) {
    // 恢复原状态
    model.model_enabled = originalStatus
    console.error('切换模型状态失败:', error)
    ElMessage.error(`状态更新失败: ${error.response?.data?.message || error.message || '未知错误'}`)
  } finally {
    model.switching = false
  }
}

// 删除模型
const deleteModel = async (model) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模型 "${model.model_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 这里需要实现删除模型的API
    ElMessage.warning('删除模型功能暂未实现')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模型失败:', error)
      ElMessage.error(`删除失败: ${error.message || '未知错误'}`)
    }
  }
}

// 添加模型
const addModels = async () => {
  if (!addModelForm.value.channel_id) {
    ElMessage.warning('请选择渠道')
    return
  }

  if (!addModelForm.value.model_names.trim()) {
    ElMessage.warning('请输入模型名称')
    return
  }

  addingModel.value = true
  try {
    const modelNames = addModelForm.value.model_names
      .split('\n')
      .map(name => name.trim())
      .filter(name => name)

    if (modelNames.length === 0) {
      ElMessage.warning('请输入至少一个模型名称')
      return
    }

    await modelService.addModels(addModelForm.value.channel_id, modelNames)

    ElMessage.success(`成功添加 ${modelNames.length} 个模型`)
    showAddModelDialog.value = false
    addModelForm.value = { channel_id: '', model_names: '' }
    await refreshData()
  } catch (error) {
    console.error('添加模型失败:', error)
    ElMessage.error(`添加失败: ${error.response?.data?.message || error.message || '未知错误'}`)
  } finally {
    addingModel.value = false
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 暴露方法给父组件
defineExpose({
  refreshData
})

// 组件挂载
onMounted(() => {
  refreshData(false) // 初始加载时不显示成功提示
})
</script>

<style scoped>
/* 复用渠道管理的样式 */
.model-management {
  width: 100%;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 20px;
}

.stat-icon.enabled {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.disabled {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
}

.table-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.model-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.model-icon {
  color: #409eff;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}
</style>
