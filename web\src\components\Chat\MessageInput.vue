<template>
  <div class="input-card" :class="{ 'input-disabled': disabled }">
    <!-- 文件上传区域 -->
    <div class="uploaded-files" v-if="uploadedFiles.length > 0">
      <div v-for="(file, index) in uploadedFiles" :key="index" class="file-item">
        <div class="file-icon">
          <!-- PDF 图标 -->
          <img src="../../assets/img/Pdf.png" alt="pdf" class="file-type-icon" v-if="file.type === 'application/pdf'" />
          <!-- DOCX 图标 -->
          <img src="../../assets/img/Docx.png" alt="docx" class="file-type-icon"
            v-else-if="file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'" />
          <!-- 图片文件图标 -->
          <el-icon class="file-type-icon image-icon" v-else-if="file.type.startsWith('image/')">
            <Picture />
          </el-icon>
          <!-- 视频文件图标 -->
          <el-icon class="file-type-icon video-icon" v-else-if="file.type.startsWith('video/')">
            <VideoPlay />
          </el-icon>
          <!-- 音频文件图标 -->
          <el-icon class="file-type-icon audio-icon" v-else-if="file.type.startsWith('audio/')">
            <Headset />
          </el-icon>
          <!-- 通用文件图标 -->
          <el-icon class="file-type-icon generic-icon" v-else>
            <Document />
          </el-icon>
        </div>
        <div class="file-info">
          <div class="file-name" :title="file.name">
            {{ getDisplayFileName(file.name) }}
          </div>
          <div class="file-size">
            {{ getFileTypeText(file.type) }} · {{ formatFileSize(file.size) }}
          </div>
          <div class="file-status">
            <span v-if="file.status === 'uploading'" class="upload-status">上传中...</span>
            <span v-else-if="file.status === 'success'" class="upload-success">已上传</span>
          </div>
        </div>
        <div class="file-close" @click="removeFile(index)">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
            <path fill="currentColor"
              d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41z">
            </path>
          </svg>
        </div>
      </div>
    </div>

    <!-- 输入框区域 -->
    <div class="input-row">
      <el-input v-model="userInput" type="textarea" :rows="1" :autosize="{ minRows: 1, maxRows: 6 }"
        :placeholder="placeholder || '发消息、选择文件上传'" resize="none" class="message-input" @keydown.enter="handleEnterKey"
        :input-style="nativeCaretStyle" @focus="handleFocus" @blur="handleBlur" :disabled="disabled" />
      <span class="custom-cursor" v-if="!userInput && !isInputFocused"></span>
    </div>

    <!-- 底部工具栏 -->
    <div class="bottom-row">
      <div class="left-tools">
        <el-upload
          v-if="showFileUpload"
          class="upload-btn"
          action="#"
          :auto-upload="false"
          :show-file-list="false"
          @change="handleFileChange"
          :disabled="disabled"
        >
          <el-button link :disabled="disabled">
            <img :src="upIcon" alt="upload" class="upload-icon" />
          </el-button>
        </el-upload>

        <!-- 深度思考按钮 -->
        <div class="thinking-btn" @click="toggleThinking" v-if="!agentMode">
          <el-button link :disabled="disabled" :class="{ 'thinking-active': enableThinking }">
            深度思考
          </el-button>
        </div>

        <!-- 模型选择组件 -->
        <ModelSelector v-if="!agentMode" @update:model="handleModelUpdate" :disabled="disabled" />
      </div>

      <div class="right-tools">
        <!-- 表单状态标签 -->
        <div v-if="formStatus?.hasForm" class="form-status-tag">
          <el-button
            v-if="!formStatus.completed"
            type="primary"
            size="small"
            @click.stop="handleOpenForm"
            class="form-tag-btn incomplete"
          >
            <el-icon><EditPen /></el-icon>
            填写表单
          </el-button>
          <el-button
            v-else
            type="success"
            size="small"
            @click.stop="handleOpenForm"
            class="form-tag-btn completed"
          >
            <el-icon><CircleCheckFilled /></el-icon>
            已完成
          </el-button>
        </div>

        <!-- 发送按钮 -->
        <el-button class="send-btn" @click="sendMessage" :class="{ 'send-btn-active': canSend }"
          :disabled="!canSend || disabled">
          <span>发送</span>
          <svg class="enter-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="16" height="16">
            <path d="M900.181333 385.792v-192a39.978667 39.978667 0 1 0-80 0v192a221.098667 221.098667 0 0 1-65.194666 157.44 221.184 221.184 0 0 1-157.397334 65.152H258.218667l112.384-112.298667a40.106667 40.106667 0 0 0-28.288-68.266666 39.808 39.808 0 0 0-28.330667 11.690666l-178.474667 178.474667a40.021333 40.021333 0 0 0 0 56.618667l183.68 183.808a39.978667 39.978667 0 1 0 56.618667-56.618667L262.4 688.384h335.189333a300.586667 300.586667 0 0 0 214.016-88.576 300.16 300.16 0 0 0 88.576-214.016z" fill="currentColor" p-id="4286"></path>
          </svg>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { EditPen, CircleCheckFilled, Picture, VideoPlay, Headset, Document } from '@element-plus/icons-vue'
import ModelSelector from '../Common/ModelSelector.vue'
import { useModelStore } from '@/stores/modelStore'
import { useApiStore } from '@/stores/apiStore'
import { useChatStore } from '@/stores/chatStore'
import { useRouter } from 'vue-router'
import upIcon from '@/assets/svg/upload.svg'

// 向父组件发送的事件
const emit = defineEmits(['send', 'update:files', 'openForm'])
const router = useRouter()

// 接收的属性
const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  agentMode: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: ''
  },
  formStatus: {
    type: Object,
    default: null
  },
  fileUploadConfig: {
    type: Object,
    default: null
  }
})

// 使用模型存储
const modelStore = useModelStore()
const apiStore = useApiStore()
const chatStore = useChatStore()

// 用户输入文本
const userInput = ref('')
// 聚焦状态
const isInputFocused = ref(false)
// 上传的文件列表
const uploadedFiles = ref([])
// 深度思考开关
const enableThinking = ref(false)

// 切换深度思考状态
const toggleThinking = () => {
  enableThinking.value = !enableThinking.value
  if (enableThinking.value) {
    ElMessage.success('已开启深度思考模式')
  } else {
    ElMessage.info('已关闭深度思考模式')
  }
}

// 格式化剪贴板的内容，移除多余空格和换行，优化排版
const formatClipboardContent = (content) => {
  // 将连续的换行符减少到最多两个
  let formatted = content.replace(/\n{3,}/g, '\n\n');
  
  // 去除每行开头的空格和缩进
  formatted = formatted.split('\n').map(line => {
    return line.trim();
  }).join('\n');
  
  return formatted;
};

// 处理粘贴事件
const handlePaste = () => {
  // 在下一个事件循环中处理，确保内容已更新
  setTimeout(() => {
    // 格式化输入内容
    const formatted = formatClipboardContent(userInput.value);
    userInput.value = formatted;
  }, 0);
};

// 发送消息
const sendMessage = async () => {
  if ((!userInput.value.trim() && !uploadedFiles.value.length) || props.isLoading || props.disabled) return

  const fileIds = uploadedFiles.value
    .filter(file => file.file_id && file.status === 'success')
    .map(file => file.file_id)

  // 如果是智能体聊天模式，仅触发事件，不检查模型
  if (props.agentMode) {
    emit('send', userInput.value, fileIds);
    userInput.value = ''; // 清空输入框
    uploadedFiles.value = []; // 清空文件列表
    return;
  }

  // 普通聊天模式下检查模型
  const selectedModel = modelStore.selectedModel
  if (!selectedModel) {
    ElMessage.warning('请先选择一个模型')
    return
  }

  // 发送事件给父组件，用于自定义处理消息发送逻辑
  emit('send', userInput.value, fileIds);

  // 以下是普通聊天模式的逻辑
  // 1. 创建并添加用户的消息
  const userMessage = {
    id: 'user-' + Date.now(),
    role: 'user',
    content: userInput.value,
    // 如果需要，还可以添加文件信息
  };
  chatStore.addMessage(userMessage);
  
  // 立即清空输入框和文件列表，不等待API响应
  const messageText = userInput.value; // 保存消息内容以便发送请求
  userInput.value = '';
  uploadedFiles.value = [];

  // 2. 新建一条用于接收AI回复的临时消息
  const tempMsg = {
    id: 'ai-' + Date.now(),
    role: 'assistant',
    content: '',
    streaming: true
  }
  chatStore.addMessage(tempMsg)

  try {
    await apiStore.sendMessage({
      query: messageText, // 使用保存的消息内容
      response_mode: 'streaming',
      conversation_id: chatStore.currentConversationId || '',
      model_name: selectedModel.model_name || '',
      enable_thinking: enableThinking.value, // 使用深度思考状态
      file_ids: fileIds
    }, (data) => {
      // 处理错误情况
      if (data && data.error) {
        tempMsg.content = `❌ ${data.error}`
        tempMsg.streaming = false
        tempMsg.error = true

        // 强制触发响应式
        const idx = chatStore.currentHistory.findIndex(m => m.id === tempMsg.id)
        if (idx !== -1) {
          chatStore.currentHistory[idx] = { ...tempMsg }
        }

        // 显示错误提示
        ElMessage.error(data.error || '消息发送失败')
        return
      }

      // 处理正常内容
      if (data && data.content !== undefined) {
        tempMsg.content += data.content
        // 强制触发响应式
        const idx = chatStore.currentHistory.findIndex(m => m.id === tempMsg.id)
        if (idx !== -1) {
          chatStore.currentHistory[idx] = { ...tempMsg }
        }
      }

      // 处理完成状态，获取会话 ID 并更新 URL
      if (data && data.status === 'completed' && data.conversation_id) {
        // 对话完成，更新ID
        tempMsg.streaming = false

        // 如果这是一个新的对话（没有当前对话ID），则更新URL
        if (!chatStore.currentConversationId) {
          router.push({ path: `/chat/${data.conversation_id}` });
          // 更新当前对话ID
          chatStore.setCurrentConversationId(data.conversation_id, false);
        }
      }
    })
    // 发送成功后刷新对话列表
    await apiStore.getConversations()
  } catch (e) {
    ElMessage.error('发送消息失败')
  }
}

// 处理模型选择更新
const handleModelUpdate = (model) => {
  // 直接使用modelStore更新选定模型
  modelStore.setSelectedModel(model)
}

// 计算发送按钮状态
const canSend = computed(() => {
  // 检查是否有文本输入
  const hasText = userInput.value.trim().length > 0

  // 检查是否有成功上传的文件
  const hasUploadedFiles = uploadedFiles.value.some(file => file.status === 'success')

  // 检查是否有正在上传的文件
  const hasUploadingFiles = uploadedFiles.value.some(file => file.status === 'uploading')

  // 如果有正在上传的文件，不允许发送
  if (hasUploadingFiles) {
    return false
  }

  // 允许发送的条件：有文本输入或有成功上传的文件
  return hasText || hasUploadedFiles
})

// 处理聚焦事件
const handleFocus = () => {
  isInputFocused.value = true
}

// 处理失焦事件
const handleBlur = () => {
  isInputFocused.value = false
}

// 计算属性，根据聚焦状态控制原生光标颜色
const nativeCaretStyle = computed(() => {
  if (isInputFocused.value) {
    return { 'caret-color': '#409eff' } // 聚焦时显示蓝色原生光标
  } else {
    return { 'caret-color': 'transparent' } // 未聚焦时隐藏原生光标
  }
})

// 处理回车键事件
const handleEnterKey = (event) => {
  // 如果按下Shift键+Enter，允许默认的换行行为
  if (event.shiftKey) {
    return
  }
  // 否则阻止默认换行行为并发送消息
  event.preventDefault()
  sendMessage()
}

// 修改文件变化处理函数
const handleFileChange = async (file) => {
  // 使用动态文件类型验证
  if (!validateFileType(file.raw, props.fileUploadConfig)) {
    return // 验证失败，validateFileType函数已经显示了错误信息
  }

  try {
    // 生成唯一ID用于标识文件
    const fileId = Date.now().toString()

    // 显示上传中状态
    const tempFileObj = {
      id: fileId,
      name: file.raw.name,
      size: file.raw.size,
      type: file.raw.type,
      status: 'uploading'
    }

    // 先添加到列表中显示上传中状态
    uploadedFiles.value.push(tempFileObj)

    console.log('开始上传文件:', file.raw.name)

    // 调用 apiStore 的上传方法
    const response = await apiStore.uploadFile(file.raw, '从聊天界面上传')

    console.log('文件上传响应:', response)

    // 上传成功，更新文件信息 - 新的响应格式处理
    if (response && response.success && response.file && response.file.file_id) {
      // 更新临时对象为服务器返回的数据
      const index = uploadedFiles.value.findIndex(f => f.id === fileId)
      if (index !== -1) {
        console.log('更新文件状态为成功, 索引:', index)

        // 使用解构和重新赋值确保响应式更新
        const updatedFiles = [...uploadedFiles.value]
        updatedFiles[index] = {
          ...updatedFiles[index],
          file_id: response.file.file_id, // 从新格式中获取file_id
          status: 'success',
          // 保存额外的文件信息供显示使用
          filename: response.file.filename,
          file_type: response.file.file_type,
          file_size: response.file.file_size,
          description: response.file.description
        }
        uploadedFiles.value = updatedFiles

        // 通知父组件文件列表已更新
        emit('update:files', uploadedFiles.value)

        // 上传成功提示
        ElMessage.success(response.message || '文件上传成功')

        console.log('更新后的文件状态:', uploadedFiles.value[index].status)
      } else {
        console.warn('找不到要更新的文件对象')
      }
    } else {
      // 处理上传成功但响应格式不符合预期的情况
      throw new Error(response.message || '上传响应格式错误')
    }
  } catch (error) {
    console.error('上传文件失败:', error)
    ElMessage.error(error.message || '上传文件失败，请重试')

    // 从列表中移除上传失败的文件
    const index = uploadedFiles.value.findIndex(f =>
      f.name === file.raw.name && f.size === file.raw.size && f.status === 'uploading'
    )

    if (index !== -1) {
      uploadedFiles.value.splice(index, 1)
    }
  }
}

// 修改移除文件函数
const removeFile = async (index) => {
  const fileToRemove = uploadedFiles.value[index]

  if (fileToRemove.file_id) {
    try {
      // 调用 apiStore 的删除文件方法
      await apiStore.deleteFile(fileToRemove.file_id)

      // 从本地列表中移除
      uploadedFiles.value.splice(index, 1)

      // 显示删除成功提示
      ElMessage.success('文件已删除')
    } catch (error) {
      console.error('删除文件失败:', error)
      ElMessage.error('删除文件失败，请重试')
    }
  } else {
    // 如果没有file_id，可能是上传失败的文件，直接从本地移除
    uploadedFiles.value.splice(index, 1)
  }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (size < 1024) {
    return size + 'B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(0) + 'KB'
  } else {
    return (size / (1024 * 1024)).toFixed(0) + 'MB'
  }
}

// 获取文件类型显示文本
const getFileTypeText = (fileType) => {
  // PDF 文件
  if (fileType === 'application/pdf') {
    return 'PDF';
  }
  // Word 文档
  else if (fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    return 'DOCX';
  }
  else if (fileType === 'application/msword') {
    return 'DOC';
  }
  // 图片文件
  else if (fileType.startsWith('image/')) {
    const imageType = fileType.split('/')[1].toUpperCase();
    return imageType === 'JPEG' ? 'JPG' : imageType;
  }
  // 视频文件
  else if (fileType.startsWith('video/')) {
    const videoType = fileType.split('/')[1].toUpperCase();
    return videoType;
  }
  // 音频文件
  else if (fileType.startsWith('audio/')) {
    const audioType = fileType.split('/')[1].toUpperCase();
    return audioType === 'MPEG' ? 'MP3' : audioType;
  }
  // 文本文件
  else if (fileType === 'text/plain') {
    return 'TXT';
  }
  else if (fileType === 'text/markdown') {
    return 'MD';
  }
  // Excel 文件
  else if (fileType === 'application/vnd.ms-excel' ||
           fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
    return 'EXCEL';
  }
  // PowerPoint 文件
  else if (fileType === 'application/vnd.ms-powerpoint' ||
           fileType === 'application/vnd.openxmlformats-officedocument.presentationml.presentation') {
    return 'PPT';
  }
  // JSON 文件
  else if (fileType === 'application/json') {
    return 'JSON';
  }
  // 默认返回通用文件类型
  return '文件';
}

// 分离文件名和后缀名
const splitFileName = (fileName) => {
  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1) {
    return { name: fileName, ext: '' };
  }
  return {
    name: fileName.substring(0, lastDotIndex),
    ext: fileName.substring(lastDotIndex) // 保留后缀名的点号
  };
}

// 修改文件名显示逻辑
const getDisplayFileName = (fileName) => {
  const maxLength = 15; // 最大显示长度

  // 如果整个文件名（包括扩展名）不超过最大长度，则完整显示
  if (fileName.length <= maxLength) {
    return fileName;
  }

  const split = splitFileName(fileName);

  // 如果文件名部分已经很长，则截断文件名并添加省略号
  if (split.name.length > maxLength - 3) { // 为省略号预留3个字符
    return split.name.substring(0, maxLength - 3) + '...';
  }

  // 否则显示尽可能多的文件名，加上省略号
  return split.name + '...';
}

// 处理打开表单事件
const handleOpenForm = () => {
  emit('openForm');
}

// 计算是否显示文件上传按钮
const showFileUpload = computed(() => {
  // 如果是智能体模式，检查文件上传配置
  if (props.agentMode && props.fileUploadConfig) {
    return props.fileUploadConfig.enabled === true;
  }

  // 普通聊天模式默认显示上传按钮
  return !props.agentMode;
})

// 文件类型检查辅助函数
const getAcceptTypes = (extensions, types) => {
  const acceptList = []

  // 添加文件扩展名
  if (extensions && extensions.length > 0) {
    acceptList.push(...extensions)
  }

  // 添加文件类型（如 image/*）
  if (types && types.length > 0) {
    const typeMap = {
      'image': 'image/*',
      'video': 'video/*',
      'audio': 'audio/*',
      'document': '.pdf,.doc,.docx,.txt,.rtf',
      'spreadsheet': '.xls,.xlsx,.csv',
      'presentation': '.ppt,.pptx'
    }

    types.forEach(type => {
      if (typeMap[type]) {
        acceptList.push(typeMap[type])
      }
    })
  }

  return acceptList.join(',')
}

// 获取要显示的文件类型（过滤掉 custom）
const getDisplayFileTypes = (fileTypes) => {
  if (!fileTypes) return []
  return fileTypes.filter(type => type !== 'custom')
}

// 文件类型验证函数
const validateFileType = (file, config) => {
  // 如果没有配置，默认支持PDF和DOCX（向后兼容）
  if (!config || (!config.allowed_file_types && !config.allowed_file_extensions)) {
    const fileType = file.type
    const isValid = fileType === 'application/pdf' ||
                   fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'

    if (!isValid) {
      ElMessage.error('不支持的文件类型，请上传 PDF, DOCX 类型的文件')
    }

    return isValid
  }

  // 检查文件扩展名
  if (config.allowed_file_extensions && config.allowed_file_extensions.length > 0) {
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
    if (!config.allowed_file_extensions.includes(fileExtension)) {
      ElMessage.error(`不支持的文件格式，请上传 ${config.allowed_file_extensions.join(', ')} 格式的文件`)
      return false
    }
  }

  // 检查文件类型（忽略 custom 类型）
  if (config.allowed_file_types && config.allowed_file_types.length > 0) {
    const validTypes = config.allowed_file_types.filter(type => type !== 'custom')

    if (validTypes.length > 0) {
      const fileType = file.type
      let isValidType = false

      validTypes.forEach(type => {
        switch (type) {
          case 'image':
            if (fileType.startsWith('image/')) isValidType = true
            break
          case 'video':
            if (fileType.startsWith('video/')) isValidType = true
            break
          case 'audio':
            if (fileType.startsWith('audio/')) isValidType = true
            break
          case 'document':
            if (fileType.includes('pdf') || fileType.includes('document') || fileType.includes('text')) isValidType = true
            break
        }
      })

      if (!isValidType) {
        // 构建错误提示信息
        const supportedTypes = []

        // 添加文件扩展名信息
        if (config.allowed_file_extensions && config.allowed_file_extensions.length > 0) {
          supportedTypes.push(`格式：${config.allowed_file_extensions.join(', ')}`)
        }

        // 添加文件类型信息
        if (validTypes.length > 0) {
          supportedTypes.push(`类型：${validTypes.join(', ')}`)
        }

        const errorMessage = supportedTypes.length > 0
          ? `不支持的文件类型，请上传 ${supportedTypes.join('，')} 的文件`
          : '不支持的文件类型'

        ElMessage.error(errorMessage)
        return false
      }
    }
  }

  return true
}
</script>

<style scoped>
.input-card {
  width: 750px;
  background: #fff;
  border-radius: 18px;
  padding: 12px 20px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  border: 1px solid #e4e7ed;
  box-shadow: 0px 100px 20px 20px rgba(255, 255, 255);
  position: relative;
  transition: all 0.3s ease;
}

/* 禁用状态样式 - 更优雅的设计 */
.input-disabled {
  pointer-events: none;
}

.input-disabled .message-input :deep(.el-textarea__inner) {
  color: #9ca3af !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.input-disabled .message-input :deep(.el-input) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.input-disabled .message-input :deep(.el-input__wrapper) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.input-disabled .message-input :deep(.el-textarea) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.input-disabled .send-btn,
.input-disabled .upload-btn :deep(.el-button),
.input-disabled .thinking-btn :deep(.el-button) {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

/* 表单按钮在禁用状态下仍然可用 */
.input-disabled .form-tag-btn {
  opacity: 1 !important;
  cursor: pointer !important;
  pointer-events: auto !important;
}

.input-disabled .upload-icon {
  opacity: 0.5 !important;
}

.input-row {
  display: flex;
  flex-direction: column;
  position: relative;
  /* 为自定义光标添加相对定位 */
}

.message-input {
  flex: 1;
  min-height: 36px;
  max-height: 180px;
  font-size: 16px;
  background: transparent;
  border: none;
  resize: none;
  width: 100%;
  caret-color: #409eff;
  /* 确保原生光标为蓝色 */
}

/* 光标闪烁效果 */
@keyframes blink {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

/* 针对 Element Plus 输入框根元素 */
.message-input :deep(.el-input) {
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
  transition: none !important;
  background-color: transparent !important;
}

.message-input :deep(.el-input__wrapper) {
  box-shadow: none !important;
  border: none !important;
  background-color: transparent !important;
  transition: none !important;
  /* 禁用所有过渡动画 */
}

.message-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
  /* 确保移除任何聚焦轮廓 */
  transition: none !important;
  /* 禁用所有过渡动画 */
  background-color: transparent !important;
  /* 确保聚焦时背景透明 */
}

.message-input :deep(.el-textarea__inner) {
  border: none;
  background: transparent;
  font-size: 16px;
  color: #222;
  padding: 6px 0 6px 8px;
  /* 调整左侧内边距以留出光标位置 */
  box-shadow: none;
  resize: none;
}

.message-input :deep(.el-textarea__inner:focus) {
  box-shadow: none !important;
}

/* 自定义光标 */
.custom-cursor {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 1.2em;
  background-color: #409eff;
  animation: blink 0.8s step-end infinite;
  pointer-events: none;
  z-index: 1;
  transition: opacity 0.1s ease;
  /* 添加过渡效果 */
}

.bottom-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 24px;
  margin-top: 16px;
}

.left-tools {
  display: flex;
  align-items: center;
}

.right-tools {
  display: flex;
  align-items: center;
  gap: 12px;
}

.upload-btn {
  margin-left: -2px;
}

.upload-btn :deep(.el-button) {
  padding: 8px;
  font-size: 20px;
  color: #333;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-btn :deep(.el-button:hover) {
  background: rgba(0, 0, 0, 0.06) !important;
  border-color: #e4e7ed;
  color: #333;
}

.upload-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0);
  /* 将SVG图标变为黑色 */
}

.send-btn {
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
  height: 36px;
  background: transparent;
  transition: all 0.3s ease;
}

.send-btn :deep(.el-button__content) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.send-btn span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  text-align: center;
}

.send-btn-active {
  background: #0057FF !important;
  border-color: #0057FF !important;
  color: white !important;
}

.send-btn-active:hover {
  background: #0047cc !important;
  border-color: #0047cc !important;
}

.send-btn:hover {
  background: rgba(0, 0, 0, 0.06);
}

.send-btn:hover .enter-icon {
  color: currentColor;
}

.send-btn-active .send-icon {
  filter: brightness(0) invert(1);
}

.send-btn-active span {
  color: white;
}

/* 添加发送按钮中图标的样式 */
.send-btn .enter-icon {
  margin-left: 4px;
  color: currentColor;
}

.send-btn-active .enter-icon {
  color: white;
}

/* 上传文件显示样式 */
.uploaded-files {
  width: 100%;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 6px 10px;
  margin-bottom: 8px;
  width: 195px;
  min-height: 52px;
  box-sizing: border-box;
}

.file-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
}

/* 文件图标通用样式 */
.file-type-icon {
  width: 32px;
  height: 32px;
}

/* 图片文件图标样式 */
.image-icon {
  color: #67c23a;
}

/* 视频文件图标样式 */
.video-icon {
  color: #e6a23c;
}

/* 音频文件图标样式 */
.audio-icon {
  color: #f56c6c;
}

/* 通用文件图标样式 */
.generic-icon {
  color: #909399;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  max-width: 120px;
  gap: 1px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  max-width: 120px;
}

.file-size {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
}

.file-status {
  font-size: 12px;
  margin-top: 2px;
}

.file-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  cursor: pointer;
  color: #909399;
  border-radius: 50%;
}

.file-close:hover {
  background: rgba(0, 0, 0, 0.06);
  color: #333;
}

/* 上传状态样式 */
.upload-status {
  color: #409eff;
  font-size: 12px;
  animation: blink 1.5s infinite;
  white-space: nowrap;
}

.upload-success {
  color: #67c23a;
  font-size: 12px;
  white-space: nowrap;
}

/* 添加深度思考按钮的样式 */
.thinking-btn {
  margin-left: 8px; /* 调整为与模型选择按钮相同的间距 */
}

.thinking-btn :deep(.el-button) {
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thinking-btn :deep(.el-button:hover) {
  background: rgba(0, 0, 0, 0.06) !important;
  border-color: #e4e7ed;
  color: #333;
}

.thinking-active {
  background-color: rgba(42, 130, 228, 0.1) !important;
  border-color: #2A82E4 !important;
  color: #2A82E4 !important;
}

/* 表单状态标签样式 */
.form-status-tag {
  display: flex;
  align-items: center;
}

.form-tag-btn {
  height: 32px !important;
  padding: 0 12px !important;
  font-size: 13px !important;
  border-radius: 16px !important;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.form-tag-btn.incomplete {
  background: #4f7cff !important;
  border-color: #4f7cff !important;
  color: white !important;
}

.form-tag-btn.incomplete:hover {
  background: #3b6bff !important;
  border-color: #3b6bff !important;
}

.form-tag-btn.completed {
  background: rgba(103, 194, 58, 0.1) !important;
  border-color: #67c23a !important;
  color: #67c23a !important;
}

.form-tag-btn.completed:hover {
  background: rgba(103, 194, 58, 0.2) !important;
}
</style>