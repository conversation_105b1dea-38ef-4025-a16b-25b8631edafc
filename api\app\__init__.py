import os

from flask import Flask, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
# Flask-JWT-Extended已移除，使用自定义JWT管理器
from flask_restx import Api
from flask_migrate import Migrate

from app.core.config import get_config
from app.core.jwt_manager import init_jwt_manager
from app.core.logging_config import setup_logging, setup_request_logging
from app.utils.errors import APIError, InvalidCredentials, TokenError, UserBanned, PermissionDenied
from app.utils.json_utils import CustomJSONEncoder

# 初始化扩展（但不绑定到应用）
db = SQLAlchemy()
migrate = Migrate()

def create_app(config_name=None):
    """
    应用工厂函数
    
    Args:
        config_name: 配置环境名称 ('development', 'production', 'testing')
                    如果不指定，将从ENV环境变量读取
    
    Returns:
        Flask应用实例
    """
    # 创建Flask应用实例
    app = Flask(__name__)
    
    # 创建API实例
    api = Api(
        app,
        doc='/docs/',
        title='Pumi-Agent API',
        version='1.0.0',
        description='Pumi-Agent API文档',
        authorizations={
            'Login': {
                'type': 'oauth2',
                'flow': 'password',
                'tokenUrl': '/auth/login',
                'description': '使用用户名和密码登录获取JWT令牌'
            }
        },
        security='Login'
    )

    # 加载配置
    config = get_config(config_name)
    app.config.from_object(config)
    app.config['MAX_CONTENT_LENGTH'] = 1024 * 1024 * 1024
    # 添加JWT专用配置
    configure_jwt(app)
    
    # 注册自定义JSON编码器 - 支持新旧版本的Flask
    try:
        # 较新版本Flask的方式
        app.json.encoder = CustomJSONEncoder
    except AttributeError:
        # 旧版本Flask的方式
        app.json_encoder = CustomJSONEncoder
    
    # 初始化扩展
    init_extensions(app)

    # 设置日志系统
    setup_logging(app)
    setup_request_logging(app)

    # 注册蓝图
    register_blueprints(app, api)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # JWT错误处理已集成到自定义JWT管理器中
    
    # 注册自定义错误处理器
    register_custom_error_handlers(app)
    
    # 注册Shell上下文
    register_shell_context(app)
    
    # 应用上下文处理器
    register_context_processors(app)
    
    return app


def configure_jwt(app):
    """配置JWT设置"""
    # JWT管理器设置
    if 'JWT_ACCESS_TOKEN_EXPIRES' not in app.config:
        app.config['JWT_ACCESS_TOKEN_EXPIRES'] = 30  # 30分钟
    if 'JWT_REFRESH_TOKEN_EXPIRES' not in app.config:
        app.config['JWT_REFRESH_TOKEN_EXPIRES'] = 7   # 7天


def init_extensions(app):
    """初始化Flask扩展"""
    # 初始化数据库
    db.init_app(app)

    # 初始化数据库迁移
    migrate.init_app(app, db)

    # 初始化JWT管理器
    init_jwt_manager(app)

    # 初始化CORS
    CORS(app, origins=app.config.get('CORS_ORIGINS', ['*']))


def register_blueprints(app, api):
    """注册蓝图"""
    # 导入并注册命名空间
    from app.routes.auth import auth_ns
    from app.routes.tools import tools_ns
    from app.routes.channels import channels_ns
    from app.routes.chat import chat_ns
    from app.routes.file import file_ns
    from app.routes.agent import agent_ns
    from app.routes.aigc_tools import ns as aigc_tools_ns
    from app.routes.admin import admin_ns

    # 添加命名空间到API
    api.add_namespace(auth_ns, path='/auth')

    api.add_namespace(tools_ns, path='/online_tools')
    api.add_namespace(channels_ns, path='/channels')
    api.add_namespace(chat_ns, path='/chat')
    api.add_namespace(file_ns, path='/file')
    api.add_namespace(agent_ns, path='/agent')
    api.add_namespace(aigc_tools_ns, path='/aigc-tools')
    api.add_namespace(admin_ns, path='/admin')

    # 注册一个基础的健康检查端点
    @app.route('/health')
    def health_check():
        return {'status': 'ok', 'message': '应用运行正常'}
    
    # 注册根路径
    @app.route('/')
    def index():
        return {
            'message': 'Welcome to AIWorkshop-API',
            'version': app.config.get('API_VERSION', '1.0.0'),
            'environment': app.config.get('ENV', 'development'),
            'docs': '/docs/'
        }


def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(400)
    def bad_request(error):
        return {'error': '请求参数错误', 'message': str(error)}, 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        return {'error': '未授权访问', 'message': '请提供有效的认证信息'}, 401
    
    @app.errorhandler(403)
    def forbidden(error):
        return {'error': '禁止访问', 'message': '您没有权限访问此资源'}, 403
    
    @app.errorhandler(404)
    def not_found(error):
        return {'error': '资源未找到', 'message': '请求的资源不存在'}, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return {'error': '服务器内部错误', 'message': '请稍后重试'}, 500
    
    @app.errorhandler(ValueError)
    def handle_value_error(error):
        return {'error': '配置错误', 'message': str(error)}, 500





def register_custom_error_handlers(app):
    """注册自定义错误处理器"""
    
    @app.errorhandler(APIError)
    def handle_api_error(error):
        response = jsonify({
            'error': error.message
        })
        response.status_code = error.status_code
        return response
    
    @app.errorhandler(InvalidCredentials)
    def handle_invalid_credentials(error):
        response = jsonify({
            'error': error.message
        })
        response.status_code = error.status_code
        return response
    
    @app.errorhandler(TokenError)
    def handle_token_error(error):
        response = jsonify({
            'error': error.message
        })
        response.status_code = error.status_code
        return response
    
    @app.errorhandler(UserBanned)
    def handle_user_banned(error):
        response = jsonify({
            'error': error.message
        })
        response.status_code = error.status_code
        return response
    
    @app.errorhandler(PermissionDenied)
    def handle_permission_denied(error):
        response = jsonify({
            'error': error.message
        })
        response.status_code = error.status_code
        return response
    
    @app.errorhandler(Exception)
    def handle_generic_error(error):
        import logging
        logger = logging.getLogger(__name__)

        # 记录完整的异常信息用于调试，包含堆栈跟踪
        logger.error(f"未处理的异常: {str(error)}", exc_info=True)

        # 根据环境区分返回信息
        if app.config.get('DEBUG'):
            # 开发环境返回详细错误信息
            response = jsonify({
                'error': str(error)
            })
        else:
            # 生产环境返回通用错误信息，不泄露敏感信息
            response = jsonify({
                'error': '服务器内部错误，请稍后重试'
            })

        response.status_code = 500
        return response


def register_shell_context(app):
    """注册Shell上下文"""
    @app.shell_context_processor
    def make_shell_context():
        return {
            'db': db,
            'app': app,
            'config': app.config
        }


def register_context_processors(app):
    """注册上下文处理器"""
    @app.before_request
    def before_request():
        """请求前处理"""
        pass
    
    @app.after_request
    def after_request(response):
        """请求后处理"""
        # 添加安全头
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        if app.config.get('ENV') == 'production':
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        return response
    
    @app.teardown_appcontext
    def teardown_db(exception):
        """应用上下文结束时的清理"""
        db.session.remove()
