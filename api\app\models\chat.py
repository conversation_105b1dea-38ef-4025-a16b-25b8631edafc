"""
聊天相关的数据库模型
包含对话和消息等信息
"""

from datetime import datetime, timezone
import uuid
from app import db

# 添加对话-文件关联表
conversation_files = db.Table(
    'conversation_files',
    db.<PERSON>umn('conversation_id', db.String(36), db.<PERSON>('conversations.conversation_id'), primary_key=True),
    db.Column('file_id', db.String(36), db.<PERSON>ey('files.file_id'), primary_key=True)
)

class Conversation(db.Model):
    """对话表"""
    __tablename__ = 'conversations'
    
    conversation_id = db.Column(db.String(36), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.Integer, db.ForeignKey('User.user_id'), nullable=False, comment='用户ID')
    title = db.Column(db.String(100), nullable=False, comment='对话标题')
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), comment='创建时间')
    updated_at = db.Column(
        db.DateTime, 
        default=lambda: datetime.now(timezone.utc), 
        onupdate=lambda: datetime.now(timezone.utc),
        comment='更新时间'
    )
    
    # 关联关系
    user = db.relationship('User', back_populates='conversations')
    messages = db.relationship('ChatMessage', back_populates='conversation')
    # 添加与文件的多对多关系
    files = db.relationship('File', secondary=conversation_files, backref='conversations')

    def __repr__(self):
        return f'<Conversation {self.conversation_id}>'

class ChatMessage(db.Model):
    """聊天消息表"""
    __tablename__ = 'ChatMessage'
    
    id = db.Column(db.Integer, primary_key=True, index=True, autoincrement=True, comment='主键ID')
    conversation_id = db.Column(
        db.String(36), 
        db.ForeignKey('conversations.conversation_id'), 
        nullable=False,
        index=True,
        comment='对话ID'
    )
    channel_id = db.Column(
        db.Integer, 
        db.ForeignKey('channellist.channel_id'), 
        nullable=False,
        comment='渠道ID'
    )
    role = db.Column(db.String(20), nullable=False, comment='角色(user/assistant)')
    content = db.Column(db.Text, nullable=False, comment='消息内容')
    model_name = db.Column(db.String(50), nullable=False, comment='使用的模型名称')
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), comment='创建时间')
    
    # 关联关系
    conversation = db.relationship('Conversation', back_populates='messages')

    def __repr__(self):
        return f'<ChatMessage {self.id}>' 