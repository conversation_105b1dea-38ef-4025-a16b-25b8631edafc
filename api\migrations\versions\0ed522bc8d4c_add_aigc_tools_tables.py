"""Add AIGC tools tables

Revision ID: 0ed522bc8d4c
Revises: e32946ecb329
Create Date: 2025-07-21 22:37:16.415238

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0ed522bc8d4c'
down_revision = 'e32946ecb329'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('aigc_categories',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键ID'),
    sa.Column('name', sa.String(length=50), nullable=False, comment='分类名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='分类描述'),
    sa.Column('icon', sa.String(length=10), nullable=True, comment='分类图标(emoji)'),
    sa.Column('color', sa.String(length=7), nullable=True, comment='主题色'),
    sa.Column('sort_order', sa.Integer(), nullable=False, comment='排序权重，数值越大排序越靠前'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否启用'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('aigc_categories', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_aigc_categories_name'), ['name'], unique=False)

    op.create_table('aigc_tools',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键ID'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='工具名称'),
    sa.Column('description', sa.Text(), nullable=False, comment='工具描述'),
    sa.Column('url', sa.String(length=500), nullable=False, comment='工具链接'),
    sa.Column('icon', sa.String(length=10), nullable=True, comment='工具图标(emoji)'),
    sa.Column('category_id', sa.Integer(), nullable=False, comment='分类ID'),
    sa.Column('tags', sa.JSON(), nullable=True, comment='标签列表'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否启用'),
    sa.Column('is_hot', sa.Boolean(), nullable=False, comment='是否热门'),
    sa.Column('click_count', sa.Integer(), nullable=False, comment='点击次数'),
    sa.Column('favorite_count', sa.Integer(), nullable=False, comment='收藏次数'),
    sa.Column('sort_order', sa.Integer(), nullable=False, comment='排序权重'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['category_id'], ['aigc_categories.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('aigc_tools', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_aigc_tools_category_id'), ['category_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_aigc_tools_name'), ['name'], unique=False)

    op.create_table('aigc_click_logs',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键ID'),
    sa.Column('user_id', sa.Integer(), nullable=True, comment='用户ID（可为空，支持匿名点击）'),
    sa.Column('tool_id', sa.Integer(), nullable=False, comment='工具ID'),
    sa.Column('ip_address', sa.String(length=45), nullable=True, comment='IP地址'),
    sa.Column('user_agent', sa.Text(), nullable=True, comment='用户代理'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='点击时间'),
    sa.ForeignKeyConstraint(['tool_id'], ['aigc_tools.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['User.user_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('aigc_click_logs', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_aigc_click_logs_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_aigc_click_logs_tool_id'), ['tool_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_aigc_click_logs_user_id'), ['user_id'], unique=False)

    op.create_table('aigc_user_favorites',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键ID'),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('tool_id', sa.Integer(), nullable=False, comment='工具ID'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='收藏时间'),
    sa.ForeignKeyConstraint(['tool_id'], ['aigc_tools.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['User.user_id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'tool_id', name='uk_user_tool_favorite')
    )
    with op.batch_alter_table('aigc_user_favorites', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_aigc_user_favorites_tool_id'), ['tool_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_aigc_user_favorites_user_id'), ['user_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('aigc_user_favorites', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_aigc_user_favorites_user_id'))
        batch_op.drop_index(batch_op.f('ix_aigc_user_favorites_tool_id'))

    op.drop_table('aigc_user_favorites')
    with op.batch_alter_table('aigc_click_logs', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_aigc_click_logs_user_id'))
        batch_op.drop_index(batch_op.f('ix_aigc_click_logs_tool_id'))
        batch_op.drop_index(batch_op.f('ix_aigc_click_logs_created_at'))

    op.drop_table('aigc_click_logs')
    with op.batch_alter_table('aigc_tools', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_aigc_tools_name'))
        batch_op.drop_index(batch_op.f('ix_aigc_tools_category_id'))

    op.drop_table('aigc_tools')
    with op.batch_alter_table('aigc_categories', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_aigc_categories_name'))

    op.drop_table('aigc_categories')
    # ### end Alembic commands ###
