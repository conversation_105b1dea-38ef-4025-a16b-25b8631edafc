name: Deploy to Production

on:
  push:
    branches: [ master ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        
    - name: Install dependencies
      run: |
        cd web
        rm -rf node_modules package-lock.json
        npm install --force
        
    - name: Build frontend
      run: |
        cd web
        npm run build
        
    - name: Deploy to server
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        script: |
          # 部署后端API
          echo "=== 部署后端API ==="
          cd /opt/pumi-agent/api
          git pull origin master

          # 更新数据库
          echo "=== 更新数据库 ==="
          source .venv/bin/activate
          flask db upgrade

          # 重启后端服务
          echo "=== 重启后端服务 ==="
          sudo systemctl restart pumi-agent-api
          sudo systemctl status pumi-agent-api --no-pager
          
          # 清理前端目录
          echo "=== 清理前端目录 ==="
          rm -rf /opt/1panel/apps/openresty/openresty/www/sites/Ai-Workshop/index/*
          
    - name: Upload frontend files
      uses: appleboy/scp-action@v0.1.7
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        source: "web/dist/*"
        target: "/opt/1panel/apps/openresty/openresty/www/sites/Ai-Workshop/index/"
        strip_components: 2
        
    - name: Verify deployment
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        port: ${{ secrets.PORT || 22 }}
        script: |
          # 检查前端文件
          echo "=== 检查前端文件 ==="
          ls -la /opt/1panel/apps/openresty/openresty/www/sites/Ai-Workshop/index/
          
          # 检查后端服务状态
          echo "=== 检查后端服务状态 ==="
          sudo systemctl is-active pumi-agent-api
          
          # 可选：检查API健康状态
          echo "=== 检查API健康状态 ==="
          curl -f http://localhost:5555/health || echo "API健康检查失败"
          
          echo "=== 部署完成 ==="
