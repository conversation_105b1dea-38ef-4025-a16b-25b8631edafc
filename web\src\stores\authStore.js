import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as tokenService from '@/services/tokenService'

export const useAuthStore = defineStore('auth', () => {
  // 使用简单的ref存储状态，通过action方法管理
  const access_token = ref('')
  const refresh_token = ref('')
  const username = ref('')

  // 初始化状态：从tokenService加载当前令牌
  const initializeAuth = () => {
    access_token.value = tokenService.getAccessToken() || ''
    refresh_token.value = tokenService.getRefreshToken() || ''
    username.value = tokenService.getUsername() || ''
  }

  // 设置访问令牌
  const setAccessToken = (token) => {
    access_token.value = token || ''
    if (token) {
      tokenService.setTokens(token, refresh_token.value)
    }
  }

  // 设置刷新令牌
  const setRefreshToken = (token) => {
    refresh_token.value = token || ''
    if (token) {
      tokenService.setTokens(access_token.value, token)
    }
  }

  // 设置用户名
  const setUsername = (name) => {
    username.value = name || ''
    tokenService.setUsername(name)
  }

  // 登录状态计算属性（保持响应式）
  const isAuthenticated = computed(() => {
    return access_token.value && !tokenService.isTokenExpired(access_token.value)
  })

  // 统一设置认证数据
  const setAuthData = (newAccessToken, newRefreshToken) => {
    access_token.value = newAccessToken || ''
    refresh_token.value = newRefreshToken || ''
    tokenService.setTokens(newAccessToken, newRefreshToken)
  }

  // 设置用户信息
  const setUserInfo = (user) => {
    setUsername(user)
  }

  // 统一清理认证数据
  const clearAuthData = () => {
    access_token.value = ''
    refresh_token.value = ''
    username.value = ''
    tokenService.clearTokens()
  }

  // 刷新状态：从tokenService重新加载
  const refreshState = () => {
    initializeAuth()
  }

  // 初始化状态
  initializeAuth()

  return {
    // 状态
    access_token,
    refresh_token,
    username,
    isAuthenticated,

    // 动作方法
    setAccessToken,
    setRefreshToken,
    setUsername,
    setAuthData,
    setUserInfo,
    clearAuthData,
    refreshState,
    initializeAuth
  }
}, {
  // 移除持久化配置，避免双重存储
  persist: false
})