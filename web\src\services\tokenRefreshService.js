/**
 * 令牌状态监控服务
 * 提供令牌状态查询功能，令牌刷新已统一到axiosConfig.js中处理
 */
import * as tokenService from './tokenService';

class TokenStatusService {
  constructor() {
    // 移除了后台刷新功能，令牌刷新已统一到axiosConfig.js中处理
    console.log('TokenStatusService: 令牌刷新功能已统一到axiosConfig.js中处理');
  }

  /**
   * 启动服务（保持兼容性，但不执行刷新）
   * @deprecated 令牌刷新已统一到axiosConfig.js中处理
   */
  start() {
    console.log('TokenStatusService: 令牌刷新已统一到axiosConfig.js中处理，无需启动后台服务');
  }

  /**
   * 停止服务（保持兼容性）
   * @deprecated 令牌刷新已统一到axiosConfig.js中处理
   */
  stop() {
    console.log('TokenStatusService: 令牌刷新已统一到axiosConfig.js中处理，无需停止后台服务');
  }

  /**
   * 获取令牌状态信息
   */
  getTokenStatus() {
    const accessToken = tokenService.getAccessToken();
    if (!accessToken) {
      return {
        hasToken: false,
        isExpired: true,
        remainingTime: 0
      };
    }

    const isExpired = tokenService.isTokenExpired(accessToken);
    const remainingTime = tokenService.getTokenRemainingTime(accessToken);
    const isNearExpiry = tokenService.isTokenNearExpiry(accessToken);

    return {
      hasToken: true,
      isExpired,
      remainingTime,
      isNearExpiry,
      expiresIn: Math.floor(remainingTime / 1000)
    };
  }
}

// 创建单例实例（重命名以反映新功能）
const tokenStatusService = new TokenStatusService();

export default tokenStatusService;