<template>
  <div class="admin-layout">
    <!-- 侧边栏 -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <div class="avatar-logo">
          <img src="../assets/svg/logo.svg" alt="普米智能体" class="logo-img" draggable="false" />
          <div class="logo-text">
            <h1 class="logo-title">普米智能体</h1>
            <p class="logo-subtitle">管理后台</p>
          </div>
        </div>
      </div>

      <el-menu
        :default-active="activeMenu"
        class="sidebar-menu"
        router
        @select="handleMenuSelect"
      >
        <template v-for="item in menuItems" :key="item.key">
          <el-menu-item v-if="!item.children" :index="item.key">
            <el-icon>
              <component :is="item.icon" />
            </el-icon>
            <template #title>{{ item.title }}</template>
          </el-menu-item>

          <el-sub-menu v-else :index="item.key">
            <template #title>
              <el-icon>
                <component :is="item.icon" />
              </el-icon>
              <span>{{ item.title }}</span>
            </template>
            
            <el-menu-item 
              v-for="child in item.children" 
              :key="child.key"
              :index="child.key"
            >
              {{ child.title }}
            </el-menu-item>
          </el-sub-menu>
        </template>
      </el-menu>

      <!-- 返回聊天按钮 -->
      <div class="sidebar-footer">
        <div class="back-to-chat-item" @click="goToChat">
          <el-icon><ChatDotRound /></el-icon>
          <span>返回聊天</span>
        </div>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <div class="main-container">
      <!-- 顶部导航 -->
      <header class="header">
        <div class="header-left">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/admin' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>{{ breadcrumbTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 通知中心 -->
          <el-badge :value="3" class="notification-badge">
            <el-button type="text" size="small">
              <el-icon><Bell /></el-icon>
            </el-button>
          </el-badge>


          <!-- 用户菜单 -->
          <el-dropdown trigger="click">
            <span class="user-dropdown">
              <el-avatar :size="32" :src="userAvatar" />
              <span class="username">管理员</span>
              <el-icon><CaretBottom /></el-icon>
            </span>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-icon><Setting /></el-icon>
                  账号设置
                </el-dropdown-item>
                <el-dropdown-item divided>
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="content">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Monitor,
  User,
  Cpu,
  Operation,
  ChatDotRound,
  TrendCharts,
  Setting,
  Bell,
  CaretBottom,
  SwitchButton,
  Grid
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const userAvatar = ref('https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png')

const menuItems = [
  {
    key: '/admin/dashboard',
    title: '仪表盘',
    icon: Monitor
  },
  {
    key: '/admin/users',
    title: '用户管理',
    icon: User
  },
  {
    key: '/admin/agents',
    title: '智能体管理',
    icon: Cpu
  },
  {
    key: '/admin/ai-resources',
    title: '模型管理',
    icon: Operation
  },
  {
    key: 'aigc-tools',
    title: 'AIGC工具坊',
    icon: Setting,
    children: [
      {
        key: '/admin/aigc-tools/tools',
        title: '工具管理'
      },
      {
        key: '/admin/aigc-tools/categories',
        title: '分类管理'
      }
    ]
  },
  {
    key: '/admin/conversations',
    title: '对话记录',
    icon: ChatDotRound
  },
  {
    key: '/admin/analytics',
    title: '数据分析',
    icon: TrendCharts
  },
  {
    key: '/admin/settings',
    title: '系统设置',
    icon: Setting
  }
]

const activeMenu = computed(() => route.path)
const breadcrumbTitle = computed(() => {
  const current = menuItems.find(item => item.key === route.path)
  return current?.title || '管理后台'
})

const handleMenuSelect = (index) => {
  router.push(index)
}

const goToChat = () => {
  router.push('/chat')
}

</script>

<style scoped>
.admin-layout {
  height: 100vh;
  display: flex;
  background: #F5F8FB;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.sidebar {
  width: 280px;
  background: #fafbfc;
  border-right: 1px solid #ececec;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.03);
  height: 100vh;
}



.sidebar-header {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.avatar-logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 28px 15px 16px 31px;
  gap: 12px;
}

.logo-img {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.logo-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.logo-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  white-space: nowrap;
}

.logo-subtitle {
  font-size: 12px;
  color: #666;
  margin: 0;
  white-space: nowrap;
  font-weight: normal;
}

.sidebar-menu {
  flex: 1;
  border-right: none;
  background: transparent;
}

.sidebar-menu .el-menu-item {
  margin: 0 16px 4px 16px;
  border-radius: 10px;
  height: 44px;
  line-height: 44px;
  color: #666;
  font-size: 14px;
  font-weight: 400;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.sidebar-menu .el-menu-item:hover {
  background: rgba(0, 0, 0, 0.06);
  color: #409eff;
}

.sidebar-menu .el-menu-item.is-active {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.sidebar-menu .el-menu-item.is-active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.2) 0%, transparent 50%);
  pointer-events: none;
}

.sidebar-menu .el-sub-menu .el-sub-menu__title {
  margin: 0 16px 4px 16px;
  border-radius: 10px;
  height: 44px;
  line-height: 44px;
  color: #666;
  font-size: 14px;
  font-weight: 400;
  transition: all 0.2s ease;
}

.sidebar-menu .el-sub-menu .el-sub-menu__title:hover {
  background: rgba(0, 0, 0, 0.06);
  color: #409eff;
}

.sidebar-menu .el-sub-menu.is-active .el-sub-menu__title {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.sidebar-menu .el-sub-menu .el-menu-item {
  margin: 0 32px 2px 32px;
  border-radius: 8px;
  height: 36px;
  line-height: 36px;
  font-size: 13px;
}

.sidebar-menu .el-sub-menu .el-menu-item.is-active {
  background: rgba(64, 158, 255, 0.15);
  color: #409eff;
  font-weight: 500;
  box-shadow: none;
}

/* 图标在选中状态下的样式 */
.sidebar-menu .el-menu-item.is-active .el-icon {
  color: #ffffff;
  transform: scale(1.1);
}

.sidebar-menu .el-sub-menu.is-active .el-sub-menu__title .el-icon {
  color: #ffffff;
  transform: scale(1.1);
}

/* 添加微妙的动画效果 */
.sidebar-menu .el-menu-item .el-icon {
  transition: all 0.2s ease;
}

.sidebar-menu .el-sub-menu .el-sub-menu__title .el-icon {
  transition: all 0.2s ease;
}

/* 侧边栏底部样式 */
.sidebar-footer {
  margin-top: auto;
  padding: 16px;
}

.back-to-chat-item {
  margin: 0 0 4px 0;
  border-radius: 10px;
  height: 44px;
  line-height: 44px;
  color: #666;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 0 16px;
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.back-to-chat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.2) 0%, transparent 50%);
  pointer-events: none;
}

.back-to-chat-item:hover {
  background: rgba(0, 0, 0, 0.06);
  color: #409eff;
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  color: #ffffff;
  transform: translateX(2px);
}

.back-to-chat-item .el-icon {
  color: #ffffff;
  transform: scale(1.1);
  margin-right: 12px;
  font-size: 16px;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  height: 64px;
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10px);
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}



.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.user-dropdown:hover {
  background-color: #f1f5f9;
  transform: translateY(-1px);
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: #334155;
}

.notification-badge {
  margin-right: 8px;
}

.content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: #F5F8FB;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -280px;
    z-index: 1000;
    height: 100vh;
    transition: left 0.3s ease;
  }

  .sidebar.mobile-open {
    left: 0;
  }

  .header-left {
    display: none;
  }
}
</style>

<style>
/* 全局样式覆盖 */
.admin-layout .el-menu {
  border-right: none;
  background: transparent;
}

.admin-layout .el-menu-item,
.admin-layout .el-sub-menu__title {
  height: 44px;
  line-height: 44px;
  margin: 0 16px 4px 16px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 400;
  color: #666;
  transition: all 0.2s ease;
}

.admin-layout .el-menu-item:hover,
.admin-layout .el-sub-menu__title:hover {
  background: rgba(0, 0, 0, 0.06);
  color: #409eff;
}

.admin-layout .el-menu-item.is-active {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.admin-layout .el-sub-menu.is-active .el-sub-menu__title {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.admin-layout .el-menu-item.is-active .el-icon,
.admin-layout .el-sub-menu.is-active .el-sub-menu__title .el-icon {
  color: #ffffff;
  transform: scale(1.1);
}
</style>