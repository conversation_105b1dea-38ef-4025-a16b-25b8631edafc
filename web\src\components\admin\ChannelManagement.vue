<template>
  <div class="channel-management">
    <!-- 统计卡片 -->
    <div class="stats-row">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><Connection /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ totalChannels }}</div>
            <div class="stat-label">总渠道数</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon enabled">
            <el-icon><Check /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ enabledChannels }}</div>
            <div class="stat-label">已启用</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon disabled">
            <el-icon><Close /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ disabledChannels }}</div>
            <div class="stat-label">已禁用</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><Setting /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ channelTypes }}</div>
            <div class="stat-label">渠道类型</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 渠道列表表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>渠道列表</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索渠道名称"
              style="width: 250px"
              size="small"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>

            <el-select v-model="filterType" placeholder="选择类型" clearable size="small" style="width: 150px">
              <el-option label="全部类型" value="" />
              <el-option label="OpenAI" value="openai" />
              <el-option label="Ollama" value="ollama" />
              <el-option label="智谱AI" value="zhipu" />
            </el-select>

            <el-select v-model="filterStatus" placeholder="选择状态" clearable size="small" style="width: 150px">
              <el-option label="全部状态" value="" />
              <el-option label="已启用" value="enabled" />
              <el-option label="已禁用" value="disabled" />
            </el-select>

            <el-button type="primary" :icon="Plus" @click="showAddChannelDialog = true" size="small">
              添加渠道
            </el-button>

            <el-button :icon="Refresh" @click="refreshData" :loading="loading" size="small">
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="filteredChannels" 
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="{ background: '#fafbfc', color: '#606266' }"
      >
        <el-table-column prop="channel_name" label="渠道名称" min-width="200">
          <template #default="{ row }">
            <div class="channel-name">
              <el-icon class="channel-icon"><Connection /></el-icon>
              {{ row.channel_name }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="channel_api_type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.channel_api_type)" size="small">
              {{ getTypeDisplayName(row.channel_api_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="channel_url" label="API地址" min-width="250">
          <template #default="{ row }">
            <span class="api-url">{{ row.channel_url }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="channel_api_enabled" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.channel_api_enabled"
              @change="toggleChannelStatus(row)"
              :loading="row.switching"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              text
              @click="editChannel(row)"
            >
              编辑
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              text
              @click="deleteChannel(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredChannels.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑渠道对话框 -->
    <el-dialog 
      v-model="showAddChannelDialog" 
      :title="isEditing ? '编辑渠道' : '添加渠道'" 
      width="500px"
      @close="resetForm"
    >
      <el-form 
        ref="channelFormRef" 
        :model="channelForm" 
        :rules="channelRules" 
        label-width="100px"
      >
        <el-form-item label="渠道名称" prop="channel_name">
          <el-input v-model="channelForm.channel_name" placeholder="请输入渠道名称" />
        </el-form-item>
        
        <el-form-item label="渠道类型" prop="channel_api_type">
          <el-select v-model="channelForm.channel_api_type" placeholder="请选择渠道类型" style="width: 100%">
            <el-option label="OpenAI" value="openai" />
            <el-option label="Ollama" value="ollama" />
            <el-option label="智谱AI" value="zhipu" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="API地址" prop="channel_url">
          <el-input v-model="channelForm.channel_url" placeholder="请输入API地址" />
        </el-form-item>
        
        <el-form-item label="API密钥" prop="channel_api_key">
          <el-input 
            v-model="channelForm.channel_api_key" 
            type="password" 
            placeholder="请输入API密钥"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddChannelDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="saveChannel" 
            :loading="savingChannel"
          >
            {{ isEditing ? '更新' : '添加' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Search, 
  Refresh, 
  Connection, 
  Check, 
  Close, 
  Setting 
} from '@element-plus/icons-vue'
import * as channelService from '@/services/channelService'

// 定义事件
const emit = defineEmits(['data-updated'])

// 响应式数据
const loading = ref(false)
const channels = ref([])
const showAddChannelDialog = ref(false)
const savingChannel = ref(false)
const isEditing = ref(false)
const channelFormRef = ref()

// 筛选条件
const searchKeyword = ref('')
const filterType = ref('')
const filterStatus = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 渠道表单
const channelForm = ref({
  channel_name: '',
  channel_api_type: '',
  channel_url: '',
  channel_api_key: ''
})

// 表单验证规则
const channelRules = {
  channel_name: [
    { required: true, message: '请输入渠道名称', trigger: 'blur' }
  ],
  channel_api_type: [
    { required: true, message: '请选择渠道类型', trigger: 'change' }
  ],
  channel_url: [
    { required: true, message: '请输入API地址', trigger: 'blur' }
  ],
  channel_api_key: [
    { required: true, message: '请输入API密钥', trigger: 'blur' }
  ]
}

// 计算属性
const totalChannels = computed(() => channels.value.length)
const enabledChannels = computed(() => channels.value.filter(c => c.channel_api_enabled).length)
const disabledChannels = computed(() => channels.value.filter(c => !c.channel_api_enabled).length)
const channelTypes = computed(() => {
  const types = new Set(channels.value.map(c => c.channel_api_type))
  return types.size
})

// 筛选后的渠道列表
const filteredChannels = computed(() => {
  let filtered = channels.value

  // 按关键词筛选
  if (searchKeyword.value) {
    filtered = filtered.filter(channel => 
      channel.channel_name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 按类型筛选
  if (filterType.value) {
    filtered = filtered.filter(channel => channel.channel_api_type === filterType.value)
  }

  // 按状态筛选
  if (filterStatus.value) {
    if (filterStatus.value === 'enabled') {
      filtered = filtered.filter(channel => channel.channel_api_enabled)
    } else if (filterStatus.value === 'disabled') {
      filtered = filtered.filter(channel => !channel.channel_api_enabled)
    }
  }

  return filtered
})

// 监听统计数据变化，向父组件发送更新事件
watch([totalChannels, enabledChannels, disabledChannels, channelTypes], () => {
  emit('data-updated', {
    totalChannels: totalChannels.value,
    enabledChannels: enabledChannels.value,
    disabledChannels: disabledChannels.value,
    channelTypes: channelTypes.value
  })
}, { immediate: true })

// 获取类型标签类型
const getTypeTagType = (type) => {
  const typeMap = {
    'openai': 'success',
    'ollama': 'info', 
    'zhipu': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取类型显示名称
const getTypeDisplayName = (type) => {
  const nameMap = {
    'openai': 'OpenAI',
    'ollama': 'Ollama',
    'zhipu': '智谱AI'
  }
  return nameMap[type] || type
}

// 刷新数据
const refreshData = async (showMessage = true) => {
  loading.value = true
  try {
    await loadChannels()
    if (showMessage) {
      ElMessage.success('渠道数据刷新成功')
    }
  } catch (error) {
    console.error('数据刷新失败:', error)
    ElMessage.error(`数据刷新失败: ${error.message || '未知错误'}`)
  } finally {
    loading.value = false
  }
}

// 加载渠道数据
const loadChannels = async () => {
  try {
    const channelData = await channelService.getChannelList()
    channels.value = channelData.map(channel => ({
      ...channel,
      switching: false
    }))
  } catch (error) {
    console.error('加载渠道数据失败:', error)
    throw error
  }
}

// 切换渠道状态
const toggleChannelStatus = async (channel) => {
  channel.switching = true
  const originalStatus = channel.channel_api_enabled

  try {
    await channelService.changeChannelStatus(channel.channel_id)
    ElMessage.success(`渠道 ${channel.channel_name} 状态已更新`)
  } catch (error) {
    // 恢复原状态
    channel.channel_api_enabled = originalStatus
    console.error('切换渠道状态失败:', error)
    ElMessage.error(`状态更新失败: ${error.response?.data?.message || error.message || '未知错误'}`)
  } finally {
    channel.switching = false
  }
}

// 编辑渠道
const editChannel = (channel) => {
  isEditing.value = true
  channelForm.value = { ...channel }
  showAddChannelDialog.value = true
}

// 删除渠道
const deleteChannel = async (channel) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除渠道 "${channel.channel_name}" 吗？此操作将同时删除该渠道下的所有模型。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await channelService.deleteChannel(channel.channel_id)
    ElMessage.success('渠道删除成功')
    await refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除渠道失败:', error)
      ElMessage.error(`删除失败: ${error.response?.data?.message || error.message || '未知错误'}`)
    }
  }
}

// 保存渠道
const saveChannel = async () => {
  if (!channelFormRef.value) return

  try {
    await channelFormRef.value.validate()
  } catch (error) {
    return
  }

  savingChannel.value = true
  try {
    if (isEditing.value) {
      // 编辑功能暂未实现
      ElMessage.warning('编辑功能暂未实现')
    } else {
      await channelService.createChannel(channelForm.value)
      ElMessage.success('渠道添加成功')
      showAddChannelDialog.value = false
      resetForm()
      await refreshData()
    }
  } catch (error) {
    console.error('保存渠道失败:', error)
    ElMessage.error(`保存失败: ${error.response?.data?.message || error.message || '未知错误'}`)
  } finally {
    savingChannel.value = false
  }
}

// 重置表单
const resetForm = () => {
  isEditing.value = false
  channelForm.value = {
    channel_name: '',
    channel_api_type: '',
    channel_url: '',
    channel_api_key: ''
  }
  if (channelFormRef.value) {
    channelFormRef.value.resetFields()
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 暴露方法给父组件
defineExpose({
  refreshData
})

// 组件挂载
onMounted(() => {
  refreshData(false) // 初始加载时不显示成功提示
})
</script>

<style scoped>
/* 复用原有的样式，这里只列出关键样式 */
.channel-management {
  width: 100%;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 20px;
}

.stat-icon.enabled {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.disabled {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
}

.table-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.channel-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.channel-icon {
  color: #409eff;
}

.api-url {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #666;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}
</style>
