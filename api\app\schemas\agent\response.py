"""智能体API响应模型"""
from flask_restx import fields, Namespace

# 创建一个临时命名空间用于定义模型
_temp_ns = Namespace('agent_schemas')

# 文件上传数据模型
file_upload_data_model = _temp_ns.model('FileUploadData', {
    'id': fields.String(description='文件ID'),
    'name': fields.String(description='文件名'),
    'size': fields.Integer(description='文件大小(字节)'),
    'extension': fields.String(description='文件扩展名'),
    'mime_type': fields.String(description='MIME类型'),
    'created_by': fields.String(description='创建者'),
    'created_at': fields.Integer(description='创建时间(Unix时间戳)')
})

# 文件上传响应模型
file_upload_response_model = _temp_ns.model('FileUploadResponse', {
    'code': fields.Integer(description='状态码'),
    'message': fields.String(description='消息'),
    'data': fields.Nested(file_upload_data_model)
})

# 用户输入表单项 - 文本输入控件
text_input_control_model = _temp_ns.model('TextInputControl', {
    'label': fields.String(description='变量显示标签名'),
    'variable': fields.String(description='变量ID'),
    'required': fields.Boolean(description='是否必填'),
    'default': fields.String(description='默认值')
})

# 用户输入表单项 - 段落输入控件
paragraph_control_model = _temp_ns.model('ParagraphControl', {
    'label': fields.String(description='变量显示标签名'),
    'variable': fields.String(description='变量ID'),
    'required': fields.Boolean(description='是否必填'),
    'default': fields.String(description='默认值')
})

# 用户输入表单项 - 选择控件
select_control_model = _temp_ns.model('SelectControl', {
    'label': fields.String(description='变量显示标签名'),
    'variable': fields.String(description='变量ID'),
    'required': fields.Boolean(description='是否必填'),
    'default': fields.String(description='默认值'),
    'options': fields.List(fields.String(description='选项值'))
})

# 文件上传配置 - 图片设置
image_upload_config_model = _temp_ns.model('ImageUploadConfig', {
    'enabled': fields.Boolean(description='是否启用图片上传'),
    'number_limits': fields.Integer(description='图片数量限制，默认为3'),
    'detail': fields.String(description='图片处理的详细级别(例如"high")'),
    'transfer_methods': fields.List(fields.String(description='传输方法列表'))
})

# 文件上传配置 - 文件上传详细配置
file_upload_detail_config_model = _temp_ns.model('FileUploadDetailConfig', {
    'file_size_limit': fields.Integer(description='文档上传大小限制(MB)'),
    'batch_count_limit': fields.Integer(description='批量上传数量限制'),
    'image_file_size_limit': fields.Integer(description='图片文件上传大小限制(MB)'),
    'video_file_size_limit': fields.Integer(description='视频文件上传大小限制(MB)'),
    'audio_file_size_limit': fields.Integer(description='音频文件上传大小限制(MB)'),
    'workflow_file_upload_limit': fields.Integer(description='工作流文件上传限制')
})

# 文件上传配置
file_upload_config_model = _temp_ns.model('FileUploadConfig', {
    'image': fields.Nested(image_upload_config_model),
    'enabled': fields.Boolean(description='是否启用文件上传'),
    'allowed_file_types': fields.List(fields.String(description='允许的文件类型')),
    'allowed_file_extensions': fields.List(fields.String(description='允许的文件扩展名')),
    'allowed_file_upload_methods': fields.List(fields.String(description='允许的文件上传方法')),
    'number_limits': fields.Integer(description='文件数量限制'),
    'fileUploadConfig': fields.Nested(file_upload_detail_config_model)
})

# 文本转语音设置
text_to_speech_model = _temp_ns.model('TextToSpeech', {
    'enabled': fields.Boolean(description='是否启用文本转语音'),
    'language': fields.String(description='语言'),
    'voice': fields.String(description='语音')
})

# 更多类似内容设置
more_like_this_model = _temp_ns.model('MoreLikeThis', {
    'enabled': fields.Boolean(description='是否启用更多类似内容')
})

# 敏感词回避设置
sensitive_word_avoidance_model = _temp_ns.model('SensitiveWordAvoidance', {
    'enabled': fields.Boolean(description='是否启用敏感词回避')
})

# 系统参数
system_parameters_model = _temp_ns.model('SystemParameters', {
    'file_size_limit': fields.Integer(description='文档上传大小限制(MB)'),
    'image_file_size_limit': fields.Integer(description='图片文件上传大小限制(MB)'),
    'audio_file_size_limit': fields.Integer(description='音频文件上传大小限制(MB)'),
    'video_file_size_limit': fields.Integer(description='视频文件上传大小限制(MB)'),
    'workflow_file_upload_limit': fields.Integer(description='工作流文件上传限制')
})

# 智能体参数数据模型
agent_parameters_data_model = _temp_ns.model('AgentParametersData', {
    'opening_statement': fields.String(description='开场白'),
    'suggested_questions': fields.List(fields.String(description='建议问题列表')),
    'suggested_questions_after_answer': fields.Raw(description='回答后的建议问题设置'),
    'speech_to_text': fields.Raw(description='语音转文本设置'),
    'text_to_speech': fields.Nested(text_to_speech_model, description='文本转语音设置'),
    'retriever_resource': fields.Raw(description='检索资源设置'),
    'annotation_reply': fields.Raw(description='注释回复设置'),
    'more_like_this': fields.Nested(more_like_this_model, description='更多类似内容设置'),
    'sensitive_word_avoidance': fields.Nested(sensitive_word_avoidance_model, description='敏感词回避设置'),
    'user_input_form': fields.Raw(description='用户输入表单配置'),
    'file_upload': fields.Raw(description='文件上传配置'),
    'system_parameters': fields.Raw(description='系统参数')
})

# 智能体参数响应模型
agent_parameters_response_model = _temp_ns.model('AgentParametersResponse', {
    'code': fields.Integer(description='状态码'),
    'message': fields.String(description='消息'),
    'data': fields.Nested(agent_parameters_data_model)
})

# 智能体项目模型（用于列表项）
agent_item_in_list_model = _temp_ns.model('AgentItemInList', {
    'id': fields.String(description='智能体ID'),
    'name': fields.String(description='智能体名称'),
    'description': fields.String(description='智能体描述'),
    'mode': fields.String(description='智能体模式'),
    'icon_type': fields.String(description='图标类型'),
    'icon': fields.String(description='图标'),
    'icon_background': fields.String(description='图标背景'),
    'icon_url': fields.String(description='图标URL'),
    'opening_statement': fields.String(description='开场白'),
    'tags': fields.List(fields.String(description='标签')),
    'created_at': fields.String(description='创建时间'),
    'updated_at': fields.String(description='更新时间')
})

# 智能体列表数据模型
agent_list_data_model = _temp_ns.model('AgentListData', {
    'total': fields.Integer(description='总记录数'),
    'page': fields.Integer(description='当前页码'),
    'limit': fields.Integer(description='每页记录数'),
    'items': fields.List(fields.Nested(agent_item_in_list_model))
})

# 智能体列表响应模型
agent_list_response_model = _temp_ns.model('AgentListResponse', {
    'code': fields.Integer(description='状态码'),
    'message': fields.String(description='消息'),
    'data': fields.Nested(agent_list_data_model)
})

# 智能体项目模型
agent_item_model = _temp_ns.model('AgentItem', {
    'id': fields.String(description='智能体ID'),
    'name': fields.String(description='智能体名称'),
    'description': fields.String(description='智能体描述'),
    'mode': fields.String(description='智能体模式'),
    'icon_type': fields.String(description='图标类型'),
    'icon': fields.String(description='图标'),
    'icon_background': fields.String(description='图标背景'),
    'icon_url': fields.String(description='图标URL'),
    'opening_statement': fields.String(description='开场白'),
    'tags': fields.List(fields.String(description='标签')),
    'created_at': fields.String(description='创建时间'),
    'updated_at': fields.String(description='更新时间')
})

# 智能体详情响应模型
agent_detail_response_model = _temp_ns.model('AgentDetailResponse', {
    'code': fields.Integer(description='状态码'),
    'message': fields.String(description='消息'),
    'data': fields.Nested(agent_item_model)
})

# 智能体标签分类响应模型
agent_tags_response_model = _temp_ns.model('AgentTagsResponse', {
    'code': fields.Integer(description='状态码'),
    'message': fields.String(description='消息'),
    'data': fields.List(fields.String(description='标签'))
})

# 智能体聊天数据模型
agent_chat_data_model = _temp_ns.model('AgentChatData', {
    'content': fields.String(description='回复内容'),
    'conversation_id': fields.String(description='对话ID')
})

# 智能体对话响应模型
agent_chat_response_model = _temp_ns.model('AgentChatResponse', {
    'code': fields.Integer(description='状态码'),
    'message': fields.String(description='消息'),
    'data': fields.Nested(agent_chat_data_model)
})

# 对话项模型
agent_conversation_item_model = _temp_ns.model('AgentConversationItem', {
    'conversation_id': fields.String(description='对话ID'),
    'title': fields.String(description='对话标题'),
    'created_at': fields.String(description='创建时间'),
    'updated_at': fields.String(description='更新时间')
})

# 对话列表响应模型
agent_conversation_list_response_model = _temp_ns.model('AgentConversationListResponse', {
    'code': fields.Integer(description='状态码'),
    'message': fields.String(description='消息'),
    'data': fields.List(fields.Nested(agent_conversation_item_model))
}) 