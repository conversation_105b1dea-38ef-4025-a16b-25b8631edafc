"""
日志配置模块
提供统一的日志配置和管理功能
"""

import os
import logging
import logging.handlers
from datetime import datetime
from flask import request, g
import uuid


class RequestFormatter(logging.Formatter):
    """自定义日志格式化器，包含请求信息"""
    
    def format(self, record):
        # 安全地添加请求ID
        try:
            if hasattr(g, 'request_id'):
                record.request_id = g.request_id
            else:
                record.request_id = 'N/A'
        except RuntimeError:
            record.request_id = 'N/A'

        # 安全地添加用户ID
        try:
            if hasattr(g, 'current_user_id'):
                record.user_id = g.current_user_id
            else:
                record.user_id = 'N/A'
        except RuntimeError:
            record.user_id = 'N/A'

        # 安全地添加请求信息
        try:
            if request:
                record.method = request.method
                record.url = request.url
                record.remote_addr = request.remote_addr
            else:
                record.method = 'N/A'
                record.url = 'N/A'
                record.remote_addr = 'N/A'
        except RuntimeError:
            record.method = 'N/A'
            record.url = 'N/A'
            record.remote_addr = 'N/A'

        return super().format(record)


def setup_logging(app):
    """
    设置应用日志配置

    Args:
        app: Flask应用实例
    """
    # 检查是否已经初始化过，避免重复初始化
    if hasattr(app, '_logging_initialized'):
        return

    # 创建logs目录
    log_dir = os.path.join(os.path.dirname(app.root_path), 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 根据环境设置日志级别
    if app.config.get('ENV') == 'production':
        log_level = logging.INFO
    else:
        log_level = logging.DEBUG

    try:
        # 设置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)

        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 创建格式化器
        detailed_formatter = RequestFormatter(
            fmt='%(asctime)s [%(levelname)s] [%(request_id)s] [User:%(user_id)s] '
                '[%(method)s %(url)s] [%(remote_addr)s] %(name)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        simple_formatter = logging.Formatter(
            fmt='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 1. 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(simple_formatter)
        root_logger.addHandler(console_handler)

        # 2. 应用日志文件处理器（按大小轮转，避免Windows文件锁定问题）
        app_log_file = os.path.join(log_dir, 'app.log')
        app_handler = logging.handlers.RotatingFileHandler(
            app_log_file,
            maxBytes=50*1024*1024,  # 50MB
            backupCount=10,
            encoding='utf-8'
        )
        app_handler.setLevel(log_level)
        app_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(app_handler)

        # 3. 错误日志文件处理器（按大小轮转，避免Windows文件锁定问题）
        error_log_file = os.path.join(log_dir, 'error.log')
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=50*1024*1024,  # 50MB
            backupCount=10,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(error_handler)

        # 4. 访问日志处理器（使用RotatingFileHandler避免Windows文件锁定问题）
        access_log_file = os.path.join(log_dir, 'access.log')
        access_handler = logging.handlers.RotatingFileHandler(
            access_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        access_handler.setLevel(logging.INFO)
        access_formatter = RequestFormatter(
            fmt='%(asctime)s [%(request_id)s] [User:%(user_id)s] '
                '%(method)s %(url)s %(remote_addr)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        access_handler.setFormatter(access_formatter)

        # 创建访问日志器
        access_logger = logging.getLogger('access')
        access_logger.setLevel(logging.INFO)
        access_logger.addHandler(access_handler)
        access_logger.propagate = False  # 不传播到根日志器

        # 5. 安全日志处理器（按大小轮转，避免Windows文件锁定问题）
        security_log_file = os.path.join(log_dir, 'security.log')
        security_handler = logging.handlers.RotatingFileHandler(
            security_log_file,
            maxBytes=50*1024*1024,  # 50MB
            backupCount=30,  # 安全日志保留更多备份以满足合规要求
            encoding='utf-8'
        )
        security_handler.setLevel(logging.INFO)
        security_handler.setFormatter(detailed_formatter)

        # 创建安全日志器
        security_logger = logging.getLogger('security')
        security_logger.setLevel(logging.INFO)
        security_logger.addHandler(security_handler)
        security_logger.propagate = False

        # 设置第三方库日志级别
        logging.getLogger('werkzeug').setLevel(logging.WARNING)
        logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
        logging.getLogger('urllib3').setLevel(logging.WARNING)

        # 标记已初始化，避免重复初始化
        app._logging_initialized = True
        app.logger.info(f"日志系统初始化完成 - 日志目录: {log_dir}")

    except Exception as e:
        # 日志配置失败时的fallback机制
        print(f"警告: 日志系统配置失败 - {str(e)}")
        print("使用基本控制台日志作为fallback")

        # 确保至少有控制台日志可用
        try:
            root_logger = logging.getLogger()
            root_logger.setLevel(logging.INFO)

            # 清除可能存在的处理器
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)

            # 添加基本的控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            formatter = logging.Formatter(
                fmt='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)

            # 标记已初始化（即使是fallback模式）
            app._logging_initialized = True
            app.logger.warning(f"日志系统使用fallback模式 - 原因: {str(e)}")

        except Exception as fallback_error:
            print(f"严重错误: 连fallback日志配置也失败 - {str(fallback_error)}")
            # 即使fallback失败，也要标记为已初始化，避免重复尝试
            app._logging_initialized = True


def setup_request_logging(app):
    """
    设置请求级别的日志功能

    Args:
        app: Flask应用实例
    """
    # 检查是否已经初始化过请求日志
    if hasattr(app, '_request_logging_initialized'):
        return

    @app.before_request
    def before_request_logging():
        """请求开始前的日志处理"""
        # 生成请求ID
        g.request_id = str(uuid.uuid4())[:8]

        # 记录请求开始
        access_logger = logging.getLogger('access')
        access_logger.info(f"Request started")
    
    @app.after_request
    def after_request_logging(response):
        """请求结束后的日志处理"""
        access_logger = logging.getLogger('access')
        access_logger.info(f"Request completed - Status: {response.status_code}")
        return response
    
    @app.teardown_request
    def teardown_request_logging(exception):
        """请求清理时的日志处理"""
        if exception:
            app.logger.error(f"Request failed with exception: {exception}", exc_info=True)

    # 标记请求日志已初始化
    app._request_logging_initialized = True


def get_logger(name):
    """
    获取指定名称的日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 日志器实例
    """
    return logging.getLogger(name)


def log_security_event(event_type, message, user_id=None, extra_data=None):
    """
    记录安全事件
    
    Args:
        event_type: 事件类型（如：login_failed, token_refresh, permission_denied等）
        message: 事件描述
        user_id: 用户ID（可选）
        extra_data: 额外数据（可选）
    """
    security_logger = logging.getLogger('security')
    
    if user_id:
        g.current_user_id = user_id
    
    log_message = f"[{event_type}] {message}"
    if extra_data:
        log_message += f" - Extra: {extra_data}"
    
    security_logger.warning(log_message)


def log_api_call(endpoint, method, user_id=None, duration=None, status_code=None):
    """
    记录API调用
    
    Args:
        endpoint: API端点
        method: HTTP方法
        user_id: 用户ID（可选）
        duration: 请求耗时（可选）
        status_code: 响应状态码（可选）
    """
    access_logger = logging.getLogger('access')
    
    if user_id:
        g.current_user_id = user_id
    
    log_message = f"API Call: {method} {endpoint}"
    if duration:
        log_message += f" - Duration: {duration:.3f}s"
    if status_code:
        log_message += f" - Status: {status_code}"
    
    access_logger.info(log_message)
