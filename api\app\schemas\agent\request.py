"""智能体API请求模型"""
from flask_restx import fields, Namespace

# 创建一个临时命名空间用于定义模型
_temp_ns = Namespace('agent_schemas')

# 聊天文件项模型
chat_file_item_model = _temp_ns.model('ChatFileItem', {
    'type': fields.String(description='文件类型(image, document等)'),
    'transfer_method': fields.String(description='传输方式(remote_url, local_file等)'),
    'url': fields.String(description='远程URL(transfer_method为remote_url时)'),
    'content': fields.String(description='Base64编码内容(transfer_method为base64时)')
})

# 智能体聊天请求模型
agent_chat_request_model = _temp_ns.model('AgentChatRequest', {
    'agent_id': fields.String(required=True, description='智能体ID'),
    'query': fields.String(required=True, description='用户问题'),
    'response_mode': fields.String(
        description='响应模式(streaming或blocking)', 
        default='streaming',
        enum=['streaming', 'blocking']
    ),
    'conversation_id': fields.String(description='对话ID，为空表示创建新对话'),
    'inputs': fields.Raw(description='额外输入参数'),
    'files': fields.List(fields.Nested(chat_file_item_model))
})

# 获取对话历史列表请求模型
agent_conversation_list_params_model = _temp_ns.model('AgentConversationListParams', {
    'last_id': fields.String(description='（选填）当前页最后面一条记录的ID'),
    'limit': fields.Integer(description='（选填）一次请求返回多少条记录，默认20条，最大100条，最小1条', min=1, max=100, default=20),
    'sort_by': fields.String(description='（选填）排序字段，默认-updated_at', 
                            enum=['created_at', '-created_at', 'updated_at', '-updated_at'],
                            default='-updated_at')
}) 