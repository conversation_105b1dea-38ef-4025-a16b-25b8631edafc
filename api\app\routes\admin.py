"""
管理员接口路由
提供管理员仪表盘所需的各种API接口
"""

import logging
from datetime import datetime, timezone
from flask import request, jsonify
from flask_restx import Namespace, Resource
from app.core.auth_decorators import admin_required
from app.services.system_monitor_service import system_monitor_service
from app.services.admin_service import admin_service
from app.utils.cache_utils import admin_cache, CACHE_TIME_SYSTEM, CACHE_TIME_STATS, CACHE_TIME_CHARTS
from app.schemas.admin_schemas import (
    admin_ns,
    system_monitor_model,
    system_health_model,
    dashboard_overview_model,
    charts_data_model,
    success_response_model,
    error_response_model
)

logger = logging.getLogger(__name__)

# 创建命名空间
admin_ns = Namespace('admin', description='管理员接口')


def create_success_response(data, message='success'):
    """创建成功响应"""
    return {
        'code': 200,
        'message': message,
        'data': data,
        'timestamp': datetime.now(timezone.utc).isoformat()
    }


@admin_ns.route('/system/monitor')
class SystemMonitor(Resource):
    @admin_required()
    @admin_ns.response(200, 'Success')
    @admin_ns.response(403, 'Forbidden')
    @admin_ns.response(500, 'Internal Server Error')
    def get(self):
        """
        获取系统监控数据
        包括CPU、内存、磁盘、网络使用情况
        """
        try:
            # 使用缓存获取系统监控数据
            monitor_data = admin_cache.get(
                'system_monitor',
                system_monitor_service.get_all_monitor_data,
                CACHE_TIME_SYSTEM
            )
            
            logger.info(f"管理员获取系统监控数据成功 - 用户ID: {getattr(request, 'current_user_id', 'unknown')}")
            return create_success_response(monitor_data)
            
        except Exception as e:
            logger.error(f"获取系统监控数据失败: {e}")
            return jsonify({
                'error': 'server_error',
                'error_description': '获取系统监控数据失败',
                'message': '服务器内部错误'
            }), 500


@admin_ns.route('/system/health')
class SystemHealth(Resource):
    @admin_required()
    @admin_ns.response(200, 'Success')
    @admin_ns.response(403, 'Forbidden')
    @admin_ns.response(500, 'Internal Server Error')
    def get(self):
        """
        获取系统健康状态
        包括系统状态和各服务健康状况
        """
        try:
            # 使用缓存获取系统健康数据
            health_data = admin_cache.get(
                'system_health',
                system_monitor_service.get_system_health,
                CACHE_TIME_SYSTEM
            )
            
            logger.info(f"管理员获取系统健康状态成功 - 用户ID: {getattr(request, 'current_user_id', 'unknown')}")
            return create_success_response(health_data)
            
        except Exception as e:
            logger.error(f"获取系统健康状态失败: {e}")
            return jsonify({
                'error': 'server_error',
                'error_description': '获取系统健康状态失败',
                'message': '服务器内部错误'
            }), 500


@admin_ns.route('/dashboard/overview')
class DashboardOverview(Resource):
    @admin_required()
    @admin_ns.response(200, 'Success')
    @admin_ns.response(403, 'Forbidden')
    @admin_ns.response(500, 'Internal Server Error')
    def get(self):
        """
        获取仪表盘概览数据
        包括用户、智能体、对话、文件统计
        """
        try:
            # 使用缓存获取概览数据
            overview_data = admin_cache.get(
                'dashboard_overview',
                admin_service.get_dashboard_overview,
                CACHE_TIME_STATS
            )
            
            logger.info(f"管理员获取仪表盘概览成功 - 用户ID: {getattr(request, 'current_user_id', 'unknown')}")
            return create_success_response(overview_data)
            
        except Exception as e:
            logger.error(f"获取仪表盘概览失败: {e}")
            return jsonify({
                'error': 'server_error',
                'error_description': '获取仪表盘概览失败',
                'message': '服务器内部错误'
            }), 500


@admin_ns.route('/dashboard/charts')
class DashboardCharts(Resource):
    @admin_required()
    @admin_ns.response(200, 'Success')
    @admin_ns.response(403, 'Forbidden')
    @admin_ns.response(500, 'Internal Server Error')
    def get(self):
        """
        获取图表数据
        包括智能体使用分布和对话活跃度
        """
        try:
            # 获取查询参数
            time_range = request.args.get('time_range', 'today')
            
            # 使用缓存获取图表数据
            cache_key = f'charts_data_{time_range}'
            
            def get_charts_data():
                return {
                    'agent_usage': admin_service.get_agent_usage_distribution(),
                    'conversation_activity': admin_service.get_conversation_activity(time_range)
                }
            
            charts_data = admin_cache.get(
                cache_key,
                get_charts_data,
                CACHE_TIME_CHARTS
            )
            
            logger.info(f"管理员获取图表数据成功 - 用户ID: {getattr(request, 'current_user_id', 'unknown')}, 时间范围: {time_range}")
            return create_success_response(charts_data)
            
        except Exception as e:
            logger.error(f"获取图表数据失败: {e}")
            return jsonify({
                'error': 'server_error',
                'error_description': '获取图表数据失败',
                'message': '服务器内部错误'
            }), 500


@admin_ns.route('/cache/info')
class CacheInfo(Resource):
    @admin_required()
    @admin_ns.response(200, 'Success')
    @admin_ns.response(403, 'Forbidden')
    def get(self):
        """
        获取缓存信息（调试用）
        """
        try:
            cache_info = admin_cache.get_cache_info()
            
            logger.info(f"管理员获取缓存信息 - 用户ID: {getattr(request, 'current_user_id', 'unknown')}")
            return create_success_response(cache_info)
            
        except Exception as e:
            logger.error(f"获取缓存信息失败: {e}")
            return jsonify({
                'error': 'server_error',
                'error_description': '获取缓存信息失败',
                'message': '服务器内部错误'
            }), 500


@admin_ns.route('/cache/clear')
class CacheClear(Resource):
    @admin_required()
    @admin_ns.response(200, 'Success')
    @admin_ns.response(403, 'Forbidden')
    def post(self):
        """
        清空缓存（调试用）
        """
        try:
            admin_cache.clear()
            
            logger.info(f"管理员清空缓存 - 用户ID: {getattr(request, 'current_user_id', 'unknown')}")
            return create_success_response({'message': '缓存已清空'})
            
        except Exception as e:
            logger.error(f"清空缓存失败: {e}")
            return jsonify({
                'error': 'server_error',
                'error_description': '清空缓存失败',
                'message': '服务器内部错误'
            }), 500
