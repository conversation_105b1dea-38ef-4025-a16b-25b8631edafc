"""
模型路由器
负责根据用户请求选择合适的模型和渠道
"""

from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy.orm import Session
from app.models.channel import ChannelList, ModelList


class ModelRouter:
    """模型路由器，负责选择合适的渠道和模型"""
    
    def __init__(self, db: Session):
        """
        初始化路由器
        
        Args:
            db: 数据库会话
        """
        self.db = db
    
    def get_all_available_models(self) -> List[Dict[str, Any]]:
        """
        获取所有可用的模型
        
        Returns:
            模型信息列表
        """
        # 查询所有启用的渠道和模型
        channels = self.db.query(ChannelList).filter(
            ChannelList.channel_api_enabled == True
        ).all()
        
        models = []
        for channel in channels:
            channel_models = self.db.query(ModelList).filter(
                ModelList.channel_id == channel.channel_id,
                ModelList.model_enabled == True
            ).all()
            
            for model in channel_models:
                models.append({
                    'model_id': model.model_id,
                    'model_name': model.model_name,
                    'channel_id': channel.channel_id,
                    'channel_name': channel.channel_name,
                    'channel_type': channel.channel_api_type
                })
                
        return models
        
    def get_best_channel_for_model(self, model_name: str) -> Optional[Tuple[int, ChannelList]]:
        """
        为指定模型名称找到最佳渠道
        
        Args:
            model_name: 模型名称
            
        Returns:
            (channel_id, channel) 元组，如果没有找到则返回None
        """
        # 查找所有包含该模型并启用的渠道
        channels_with_model = self.db.query(
            ChannelList, ModelList
        ).join(
            ModelList, ChannelList.channel_id == ModelList.channel_id
        ).filter(
            ModelList.model_name == model_name,
            ModelList.model_enabled == True,
            ChannelList.channel_api_enabled == True
        ).all()
        
        if not channels_with_model:
            return None
            
        # 简单起见，选择第一个渠道
        # 未来可以实现负载均衡或基于速度/成本的路由策略
        channel, _ = channels_with_model[0]
        
        return channel.channel_id, channel 