[project]
name = "pumi-agent-api"
version = "1.0.0"
description = "PUMI Agent 后端API服务 - 企业级AI智能体平台"
authors = [
    {name = "PUMI Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8.1"
keywords = ["flask", "api", "ai", "agent", "chatbot"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Framework :: Flask",
    "Topic :: Internet :: WWW/HTTP :: WSGI :: Application",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    # Flask核心框架
    "Flask==3.0.0",
    "Werkzeug==3.0.1",
    # API文档
    "Flask-RESTX==1.3.0",
    # 数据库相关
    "Flask-SQLAlchemy==3.1.1",
    "SQLAlchemy==2.0.23",
    "PyMySQL==1.1.0",
    "cryptography==41.0.7",
    # CORS支持
    "Flask-CORS==4.0.0",
    # 环境变量管理
    "python-dotenv==1.0.0",
    # 数据验证
    "marshmallow==3.20.1",
    "Flask-Marshmallow==0.15.0",
    # HTTP客户端
    "requests==2.31.0",
    "httpx==0.25.2",
    # 日志和监控
    "gunicorn==21.2.0",
    # 数据迁移
    "Flask-Migrate==4.0.5",
    # 文件解析依赖
    "mammoth==1.6.0",
    "markdownify==0.11.6",
    "PyMuPDF==1.23.7",
    "pyjwt>=2.9.0",
    "psutil>=6.1.1",
]

[project.optional-dependencies]
dev = [
    # 开发工具
    "Flask-DebugToolbar==0.13.1",
    
    # 测试框架
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-flask>=1.2.0",
    
    # 代码质量
    "black>=23.0.0",
    "flake8>=6.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    
    # 安全检查
    "bandit>=1.7.0",
    "safety>=2.3.0",
    
    # Git钩子
    "pre-commit>=3.0.0",
    
    # 类型提示
    "types-requests",
    "sqlalchemy-stubs",
]

prod = [
    # 生产环境依赖
    "gunicorn>=21.0.0",
]

[project.urls]
Homepage = "https://github.com/your-org/pumi-agent"
Documentation = "https://docs.pumi.ai"
Repository = "https://github.com/your-org/pumi-agent.git"
"Bug Tracker" = "https://github.com/your-org/pumi-agent/issues"

[project.scripts]
pumi-api = "run:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.black]
line-length = 100
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.flake8]
max-line-length = 100
exclude = [
    ".git",
    "__pycache__",
    "docs/source/conf.py",
    "old",
    "build",
    "dist",
    ".venv",
    "venv",
    "migrations"
]
ignore = ["E203", "E266", "E501", "W503"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
strict_optional = true

[[tool.mypy.overrides]]
module = "app.models.*"
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_functions = ["test_*"]
python_classes = ["Test*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-fail-under=80"
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "migrations"]
skips = ["B101", "B601"]
