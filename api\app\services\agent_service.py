from typing import Dict, List, Optional, Any
import logging
import json
import requests
from app.utils.dify_client import DifyClient

logger = logging.getLogger(__name__)

class AgentService:
    """
    智能体服务
    处理智能体列表获取、数据转换和处理
    """
    
    def __init__(self):
        """初始化服务"""
        self.dify_client = DifyClient()
        
    def get_agent_list(self, page: int = 1, limit: int = 20, 
                      mode: Optional[str] = None) -> Dict[str, Any]:
        """
        获取智能体列表
        
        Args:
            page: 页码，从1开始
            limit: 每页记录数
            mode: 智能体模式筛选 (chat, advanced-chat, agent-chat, workflow)
            
        Returns:
            处理后的智能体列表数据
        """
        try:
            # 调用Dify API获取应用列表
            result = self.dify_client.get_apps(page, limit)
            
            # 如果需要根据模式筛选
            if mode and result.get('data'):
                filtered_data = [item for item in result.get('data', []) 
                                if item.get('mode') == mode]
                result['data'] = filtered_data
                result['total'] = len(filtered_data)  # 这里简化处理，实际可能需要重新计算total
                
            # 处理应用数据：获取详情，检查enable_api，转换为智能体数据
            agents = []
            for app in result.get('data', []):
                # 获取应用详情，以获取enable_api字段
                app_id = app.get('id')
                if not app_id:
                    continue
                    
                try:
                    app_detail = self.dify_client.get_app_detail(app_id)
                    if app_detail.get('enable_api'):
                        # 只有启用了API的应用才转换为智能体
                        agent = self._convert_app_to_agent(app_detail)
                        if agent:
                            agents.append(agent)
                except Exception as e:
                    logger.warning(f"获取应用[{app_id}]详情失败: {str(e)}")
                    continue
                
            return {
                'code': 200,
                'message': 'success',
                'data': {
                    'page': result.get('page', page),
                    'limit': result.get('limit', limit),
                    'total': len(agents),  # 更新为实际过滤后的数量
                    'has_more': result.get('has_more', False),
                    'items': agents
                }
            }
            
        except Exception as e:
            logger.error(f"获取智能体列表失败: {str(e)}")
            return {
                'code': 500,
                'message': f"获取智能体列表失败: {str(e)}",
                'data': None
            }
    
    def get_agent_detail(self, agent_id: str) -> Dict[str, Any]:
        """
        获取智能体详情
        
        Args:
            agent_id: 智能体ID
            
        Returns:
            智能体详情数据
        """
        try:
            # 调用Dify API获取应用详情
            app_detail = self.dify_client.get_app_detail(agent_id)
            
            # 检查应用是否启用API
            if not app_detail.get('enable_api'):
                return {
                    'code': 403,
                    'message': '该智能体未启用API访问',
                    'data': None
                }
                
            # 格式转换
            agent = self._convert_app_to_agent(app_detail)
            
            return {
                'code': 200,
                'message': 'success',
                'data': agent
            }
            
        except Exception as e:
            logger.error(f"获取智能体详情失败: {str(e)}")
            return {
                'code': 500,
                'message': f"获取智能体详情失败: {str(e)}",
                'data': None
            }
    
    def get_agent_tags(self) -> Dict[str, Any]:
        """
        获取智能体标签分类
        
        Returns:
            标签名称列表
        """
        try:
            # 调用Dify API获取标签
            tags_data = self.dify_client.get_tags(tag_type="app")
            
            # 从标签数据中提取名称列表
            tag_names = []
            
            # Dify API直接返回标签列表，没有data字段包装
            if isinstance(tags_data, list):
                tag_names = [tag.get('name') for tag in tags_data if tag.get('name')]
            elif isinstance(tags_data, dict) and 'data' in tags_data:
                # 兼容可能的data字段包装情况
                tag_names = [tag.get('name') for tag in tags_data.get('data', []) if tag.get('name')]
            
            # 处理标签数据
            return {
                'code': 200,
                'message': 'success',
                'data': tag_names
            }
            
        except Exception as e:
            logger.error(f"获取智能体标签失败: {str(e)}")
            return {
                'code': 500,
                'message': f"获取智能体标签失败: {str(e)}",
                'data': None
            }
            
    def get_agent_conversation_list(
        self, 
        agent_id: str, 
        user_id: str,
        last_id: Optional[str] = None,
        limit: int = 20,
        sort_by: str = "-updated_at"
    ) -> Dict[str, Any]:
        """
        获取智能体对话历史列表
        
        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            last_id: 上一页最后一条记录的ID
            limit: 每页记录数
            sort_by: 排序方式
            
        Returns:
            对话历史列表数据
        """
        try:
            # 先获取应用详情，检查是否启用API
            app_detail = self.dify_client.get_app_detail(agent_id)
            
            # 检查应用是否启用API
            if not app_detail.get('enable_api'):
                return {
                    'code': 403,
                    'message': '该智能体未启用API访问',
                    'data': []
                }
                
            # 获取智能体访问凭证
            credentials = self.dify_client.get_app_credentials(agent_id)
            
            # 检查凭证是否有效
            if not credentials or 'api_key' not in credentials:
                logger.error(f"获取智能体[{agent_id}]访问凭证失败")
                return {
                    'code': 400,
                    'message': '获取智能体访问凭证失败',
                    'data': []
                }
                
            # 提取API密钥
            agent_api_key = credentials.get('api_key')
            
            # 调用Dify API获取对话列表
            result = self.dify_client.get_conversation_list(
                agent_api_key=agent_api_key,
                user_id=user_id,
                last_id=last_id,
                limit=limit,
                sort_by=sort_by
            )
            
            # 转换为期望的对话格式
            conversations = []
            if isinstance(result, dict) and 'data' in result:
                for item in result.get('data', []):
                    # 转换时间戳为ISO格式
                    created_at = self._format_timestamp(item.get('created_at'))
                    updated_at = self._format_timestamp(item.get('updated_at'))
                    
                    # 转换为期望的格式
                    conversation = {
                        'conversation_id': item.get('id'),
                        'title': item.get('name', ''),
                        'created_at': created_at,
                        'updated_at': updated_at
                    }
                    conversations.append(conversation)
            
            # 返回标准格式的响应
            return {
                'code': 200,
                'message': 'success',
                'data': conversations
            }
            
        except Exception as e:
            logger.error(f"获取智能体对话历史列表失败: {str(e)}")
            return {
                'code': 500,
                'message': f"获取智能体对话历史列表失败: {str(e)}",
                'data': []
            }
    
    def _format_timestamp(self, timestamp) -> str:
        """
        将时间戳转换为ISO格式的日期时间字符串
        
        Args:
            timestamp: Unix时间戳（秒）或ISO格式字符串
            
        Returns:
            ISO格式的日期时间字符串 (YYYY-MM-DDTHH:MM:SS)
        """
        import datetime
        
        if not timestamp:
            return ""
            
        # 如果已经是ISO格式字符串，直接返回
        if isinstance(timestamp, str) and "T" in timestamp:
            return timestamp
            
        try:
            # 尝试将时间戳转换为整数
            ts = int(timestamp)
            # 转换为日期时间对象
            dt = datetime.datetime.fromtimestamp(ts)
            # 格式化为ISO格式字符串
            return dt.strftime("%Y-%m-%dT%H:%M:%S")
        except (ValueError, TypeError):
            # 转换失败，返回原始值
            return str(timestamp)
    
    def chat_with_agent(self, agent_id: str, query: str, conversation_id: str = "",
                        user: str = None, inputs: Dict = None, files: List[Dict] = None,
                        response_mode: str = "streaming", stream: bool = True) -> Dict[str, Any]:
        """
        与智能体对话
        
        Args:
            agent_id: 智能体ID
            query: 用户问题
            conversation_id: 对话ID，为空表示新对话
            user: 用户标识
            inputs: 额外输入参数
            files: 文件列表
            response_mode: 响应模式（streaming或blocking）
            stream: 是否返回流式响应对象
            
        Returns:
            如果stream=True且response_mode=streaming，返回原始响应对象
            否则返回处理后的响应数据
        """
        try:
            # 先获取应用详情，检查是否启用API
            app_detail = self.dify_client.get_app_detail(agent_id)
            
            # 检查应用是否启用API
            if not app_detail.get('enable_api'):
                return {
                    'code': 403,
                    'message': '该智能体未启用API访问',
                    'data': None
                }
                
            # 获取智能体访问凭证
            credentials = self.dify_client.get_app_credentials(agent_id)
            
            # 检查凭证是否有效
            if not credentials or 'api_key' not in credentials:
                logger.error(f"获取智能体[{agent_id}]访问凭证失败")
                return {
                    'code': 400,
                    'message': '获取智能体访问凭证失败',
                    'data': None
                }
                
            # 提取API密钥
            agent_api_key = credentials.get('api_key')
            
            # 调用智能体对话API
            response = self.dify_client.chat_with_agent(
                agent_api_key=agent_api_key,
                query=query,
                conversation_id=conversation_id,
                user=user,
                inputs=inputs,
                files=files,
                response_mode=response_mode,
                stream=stream
            )
            
            # 流式响应直接返回响应对象
            if stream and response_mode == "streaming":
                return response
                
            # 非流式响应处理为标准格式
            return {
                'code': 200,
                'message': 'success',
                'data': response
            }
                
        except Exception as e:
            logger.error(f"与智能体对话失败: {str(e)}")
            return {
                'code': 500,
                'message': f"与智能体对话失败: {str(e)}",
                'data': None
            }
    
    def _convert_app_to_agent(self, app: Dict[str, Any]) -> Dict[str, Any]:
        """
        将Dify应用数据转换为智能体数据
        
        Args:
            app: Dify应用数据
            
        Returns:
            转换后的智能体数据
        """
        # 提取tag名称列表
        tag_names = []
        if app.get('tags'):
            tag_names = [tag.get('name') for tag in app.get('tags', []) if tag.get('name')]
        
        # 处理图标URL，使用我们自己的代理API
        proxy_icon_url = None
        icon_id = None
        
        # 如果是图片类型的图标
        if app.get('icon_type') == 'image' and app.get('icon'):
            icon_id = app.get('icon')
            original_url = app.get('icon_url', '')
            
            # 提取原始URL中的签名参数
            query_params = ""
            if original_url and '?' in original_url:
                query_string = original_url.split('?', 1)[1]
                query_params = f"?{query_string}"
            
            # 构建带有原始签名参数的代理URL
            proxy_icon_url = f"/agent/icons/{icon_id}{query_params}"
        
        # 提取model_config中的opening_statement
        opening_statement = ""
        model_config = app.get('model_config', {})
        if model_config:
            opening_statement = model_config.get('opening_statement', '')

        # 构建智能体数据
        agent = {
            'id': app.get('id'),
            'name': app.get('name'),
            'description': app.get('description'),
            'mode': app.get('mode'),
            'icon_type': app.get('icon_type'),
            'icon': app.get('icon'),  # 原始icon ID
            'icon_background': app.get('icon_background'),
            'icon_url': proxy_icon_url,  # 转换后的代理URL
            'original_icon_url': app.get('icon_url'),  # 保留原始URL以供参考
            'opening_statement': opening_statement,  # 添加开场白
            'tags': tag_names,
            'created_at': app.get('created_at'),
            'updated_at': app.get('updated_at'),
        }
        
        return agent
        
    def upload_file(self, agent_id: str, file_path: str, user_id: str, mime_type: str = None) -> Dict[str, Any]:
        """
        上传文件到指定的智能体
        
        Args:
            agent_id: 智能体ID
            file_path: 文件路径
            user_id: 用户ID
            mime_type: 文件MIME类型，如果不提供会根据文件扩展名猜测
            
        Returns:
            上传结果，包含文件ID等信息
        """
        try:
            # 获取智能体访问凭证
            credentials = self.dify_client.get_app_credentials(agent_id)
            
            # 检查凭证是否有效
            if not credentials or 'api_key' not in credentials:
                logger.error(f"获取智能体[{agent_id}]访问凭证失败")
                return {
                    'code': 400,
                    'message': '获取智能体访问凭证失败',
                    'data': None
                }
                
            # 提取API密钥
            agent_api_key = credentials.get('api_key')
            
            # 调用Dify客户端上传文件
            result = self.dify_client.upload_file(
                agent_api_key=agent_api_key,
                file_path=file_path,
                user_id=user_id,
                mime_type=mime_type
            )
            
            # 返回标准格式的响应
            return {
                'code': 200,
                'message': 'success',
                'data': result
            }
            
        except Exception as e:
            logger.error(f"上传文件失败: {str(e)}")
            return {
                'code': 500,
                'message': f"上传文件失败: {str(e)}",
                'data': None
            }
    
    def get_agent_parameters(self, agent_id: str) -> Dict[str, Any]:
        """
        获取智能体参数信息
        
        Args:
            agent_id: 智能体ID
            
        Returns:
            智能体参数信息，包含opening_statement、suggested_questions、user_input_form等
        """
        try:
            # 先获取应用详情，检查是否启用API
            app_detail = self.dify_client.get_app_detail(agent_id)
            
            # 检查应用是否启用API
            if not app_detail.get('enable_api'):
                return {
                    'code': 403,
                    'message': '该智能体未启用API访问',
                    'data': None
                }
                
            # 获取智能体访问凭证
            credentials = self.dify_client.get_app_credentials(agent_id)
            
            # 检查凭证是否有效
            if not credentials or 'api_key' not in credentials:
                logger.error(f"获取智能体[{agent_id}]访问凭证失败")
                return {
                    'code': 400,
                    'message': '获取智能体访问凭证失败',
                    'data': None
                }
            
            # 获取智能体API密钥
            api_key = credentials.get('api_key')
            
            # 调用Dify客户端获取参数信息
            parameters = self.dify_client.get_app_parameters(api_key)
            
            return {
                'code': 200,
                'message': 'success',
                'data': parameters
            }
            
        except Exception as e:
            logger.error(f"获取智能体参数信息失败: {str(e)}")
            return {
                'code': 500,
                'message': f"获取智能体参数信息失败: {str(e)}",
                'data': None
            } 