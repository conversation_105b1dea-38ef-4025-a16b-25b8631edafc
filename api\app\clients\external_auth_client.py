"""
简化的外部认证客户端
只负责验证用户名密码，不处理令牌管理
"""

import requests
import logging
from typing import Dict, Optional
from flask import current_app
from app.core.config import get_config

logger = logging.getLogger(__name__)


class ExternalAuthClient:
    """
    简化的外部认证客户端
    职责：仅验证用户名密码是否正确
    """
    
    def __init__(self):
        self.config = get_config()
        self.base_url = self.config.AUTH_SERVICE_BASE_URL
        self.timeout = self.config.AUTH_SERVICE_TIMEOUT
        
    def validate_credentials(self, username: str, password: str) -> Dict:
        """
        验证用户名密码是否正确
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Dict: 验证结果
            {
                'valid': bool,           # 验证是否成功
                'user_info': dict,       # 用户基本信息（如果验证成功）
                'error': str             # 错误信息（如果验证失败）
            }
        """
        try:
            # 检查临时用户（以tmp_开头的用户ID）
            if username.startswith('tmp_'):
                return self._handle_temp_user(username, password)
            
            # 调用外部认证系统验证密码
            auth_url = f"{self.base_url}{self.config.AUTH_SERVICE_LOGIN_ENDPOINT}"
            payload = {
                "user_data": {
                    "user_id": username,
                    "password": password
                }
            }

            # 生成客户端指纹（外部服务可能需要）
            import uuid
            client_fp = str(uuid.uuid4())

            headers = {
                "Content-Type": "application/json",
                "client-fp": client_fp  # 添加客户端指纹头
            }
            
            logger.info(f"验证用户凭据: {username}")
            
            response = requests.post(
                auth_url,
                json=payload,
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                auth_data = response.json()
                
                # 解析响应数据
                user_info = self._parse_auth_response(auth_data)
                
                if user_info:
                    logger.info(f"用户 {username} 验证成功")
                    return {
                        'valid': True,
                        'user_info': user_info,
                        'error': None
                    }
                else:
                    logger.warning(f"用户 {username} 验证响应格式不正确")
                    return {
                        'valid': False,
                        'user_info': None,
                        'error': '认证响应格式不正确'
                    }
            
            elif response.status_code == 401:
                logger.warning(f"用户 {username} 验证失败：用户名或密码错误")
                return {
                    'valid': False,
                    'user_info': None,
                    'error': '用户名或密码错误'
                }
            
            else:
                logger.error(f"外部认证系统错误: {response.status_code} - {response.text}")
                return {
                    'valid': False,
                    'user_info': None,
                    'error': f'认证服务错误: {response.status_code}'
                }
                
        except requests.exceptions.Timeout:
            logger.error(f"外部认证系统超时: {username}")
            return {
                'valid': False,
                'user_info': None,
                'error': '认证服务超时'
            }
            
        except requests.exceptions.ConnectionError:
            logger.error(f"无法连接到外部认证系统: {username}")
            return {
                'valid': False,
                'user_info': None,
                'error': '无法连接到认证服务'
            }
            
        except Exception as e:
            logger.error(f"验证用户凭据时发生错误: {e}")
            return {
                'valid': False,
                'user_info': None,
                'error': '认证过程中发生错误'
            }
    
    def _handle_temp_user(self, username: str, password: str) -> Dict:
        """
        处理临时用户验证
        
        Args:
            username: 临时用户名（以tmp_开头）
            password: 密码
            
        Returns:
            Dict: 验证结果
        """
        # 临时用户的简单验证逻辑
        # 这里可以根据实际需求实现临时用户的验证规则
        
        # 示例：临时用户密码验证（可以根据实际需求修改）
        if password == "temp_password" or len(password) >= 6:
            logger.info(f"临时用户 {username} 验证成功")
            return {
                'valid': True,
                'user_info': {
                    'id': username,
                    'name': username,
                    'role': 'temp_user',
                    'external_user_id': username
                },
                'error': None
            }
        else:
            logger.warning(f"临时用户 {username} 验证失败")
            return {
                'valid': False,
                'user_info': None,
                'error': '临时用户密码不正确'
            }
    
    def _parse_auth_response(self, auth_data: Dict) -> Optional[Dict]:
        """
        解析外部认证系统的响应数据
        
        Args:
            auth_data: 外部认证系统返回的数据
            
        Returns:
            Dict: 解析后的用户信息，如果解析失败返回None
        """
        try:
            # 检查是否有嵌套的data字段（新格式）
            if "data" in auth_data and isinstance(auth_data["data"], dict):
                auth_info = auth_data["data"]
                external_user_id = auth_info.get("id")
                username = auth_info.get("name")
                role = auth_info.get("role")
            else:
                # 旧格式：直接从顶层获取信息
                external_user_id = auth_data.get("id")
                username = auth_data.get("name")
                role = auth_data.get("role")
            
            # 验证必要字段
            if not external_user_id:
                logger.error(f"外部认证响应缺少用户ID: {auth_data}")
                return None
            
            return {
                'id': external_user_id,
                'name': username or external_user_id,
                'role': role or 'student',
                'external_user_id': external_user_id
            }
            
        except Exception as e:
            logger.error(f"解析认证响应时发生错误: {e}")
            return None
    
    def get_user_info(self, username: str) -> Optional[Dict]:
        """
        获取用户基本信息（如果外部系统提供此功能）
        
        Args:
            username: 用户名
            
        Returns:
            Dict: 用户信息，如果获取失败返回None
        """
        try:
            # 这里可以实现获取用户信息的逻辑
            # 目前返回None，表示不支持此功能
            logger.debug(f"获取用户信息功能暂未实现: {username}")
            return None
            
        except Exception as e:
            logger.warning(f"获取用户信息失败: {e}")
            return None
    
    def health_check(self) -> bool:
        """
        检查外部认证系统是否可用
        
        Returns:
            bool: True表示可用，False表示不可用
        """
        try:
            # 发送健康检查请求
            health_url = f"{self.base_url}/health"
            response = requests.get(health_url, timeout=5)
            
            is_healthy = response.status_code == 200
            
            if is_healthy:
                logger.debug("外部认证系统健康检查通过")
            else:
                logger.warning(f"外部认证系统健康检查失败: {response.status_code}")
            
            return is_healthy
            
        except Exception as e:
            logger.error(f"外部认证系统健康检查异常: {e}")
            return False


# 全局外部认证客户端实例
external_auth_client = ExternalAuthClient()
