"""
统一错误处理装饰器
提供标准化的错误处理逻辑，减少重复代码并保持现有的异常体系
"""

import functools
import logging
import traceback
from flask import request, jsonify, current_app
from typing import Callable, Any
from .errors import (
    APIError, AuthError, ValidationError, ServerError, 
    TokenError, PermissionDenied, UserBanned, ResourceNotFound,
    ChannelConnectionError, ChannelAuthError, ChannelModelError,
    ChannelRateLimitError, ChannelTimeoutError
)


def handle_service_errors(func: Callable) -> Callable:
    """
    服务层错误处理装饰器
    
    用于标准化服务层的错误处理逻辑：
    - 捕获业务异常并重新抛出（保持异常链）
    - 捕获系统异常并转换为ServerError
    - 添加统一的日志记录
    
    Args:
        func: 要装饰的服务方法
        
    Returns:
        装饰后的方法
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        
        try:
            # 记录服务方法调用
            logger.debug(f"调用服务方法: {func.__name__}")
            
            # 执行原始方法
            result = func(*args, **kwargs)
            
            logger.debug(f"服务方法执行成功: {func.__name__}")
            return result
            
        except (APIError, AuthError, ValidationError, TokenError, 
                PermissionDenied, UserBanned, ResourceNotFound,
                ChannelConnectionError, ChannelAuthError, ChannelModelError,
                ChannelRateLimitError, ChannelTimeoutError) as e:
            # 业务异常：记录警告并重新抛出
            logger.warning(f"服务方法 {func.__name__} 业务异常: {e}")
            raise e
            
        except Exception as e:
            # 系统异常：记录错误并转换为ServerError
            logger.error(f"服务方法 {func.__name__} 系统异常: {e}", exc_info=True)
            raise ServerError(f"服务处理失败: {str(e)}")
    
    return wrapper


def handle_route_errors(func: Callable) -> Callable:
    """
    路由层错误处理装饰器
    
    用于标准化路由层的错误处理逻辑：
    - 捕获所有异常并返回标准JSON响应
    - 保持现有的错误响应格式
    - 添加请求上下文信息到日志
    
    Args:
        func: 要装饰的路由方法
        
    Returns:
        装饰后的方法
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        
        # 获取请求上下文信息
        request_info = {
            'method': request.method,
            'path': request.path,
            'remote_addr': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', 'Unknown')
        }
        
        try:
            # 记录请求信息
            logger.debug(f"处理请求: {request_info['method']} {request_info['path']}")
            
            # 执行原始方法
            result = func(*args, **kwargs)
            
            logger.debug(f"请求处理成功: {request_info['method']} {request_info['path']}")
            return result
            
        except APIError as e:
            # API业务异常：返回标准错误响应
            logger.warning(f"API业务异常 - {request_info}: {e}")

            # 检查异常是否有自定义响应格式
            if hasattr(e, 'response_data'):
                response = jsonify(e.response_data)
            else:
                response = jsonify({
                    'error': e.message
                })
            response.status_code = e.status_code
            return response
            
        except Exception as e:
            # 系统异常：记录详细错误并返回通用错误响应
            logger.error(f"系统异常 - {request_info}: {e}", exc_info=True)
            
            # 根据环境返回不同的错误信息
            if current_app.config.get('DEBUG'):
                # 开发环境返回详细错误信息
                error_message = str(e)
                if hasattr(e, '__traceback__'):
                    error_message += f"\n\n{traceback.format_exc()}"
            else:
                # 生产环境返回通用错误信息
                error_message = "服务器内部错误，请稍后重试"
            
            response = jsonify({
                'error': error_message
            })
            response.status_code = 500
            return response
    
    return wrapper


def handle_async_service_errors(func: Callable) -> Callable:
    """
    异步服务层错误处理装饰器
    
    专门用于处理异步服务方法的错误
    
    Args:
        func: 要装饰的异步服务方法
        
    Returns:
        装饰后的异步方法
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        
        try:
            # 记录异步服务方法调用
            logger.debug(f"调用异步服务方法: {func.__name__}")
            
            # 执行原始异步方法
            result = await func(*args, **kwargs)
            
            logger.debug(f"异步服务方法执行成功: {func.__name__}")
            return result
            
        except (APIError, AuthError, ValidationError, TokenError, 
                PermissionDenied, UserBanned, ResourceNotFound,
                ChannelConnectionError, ChannelAuthError, ChannelModelError,
                ChannelRateLimitError, ChannelTimeoutError) as e:
            # 业务异常：记录警告并重新抛出
            logger.warning(f"异步服务方法 {func.__name__} 业务异常: {e}")
            raise e
            
        except Exception as e:
            # 系统异常：记录错误并转换为ServerError
            logger.error(f"异步服务方法 {func.__name__} 系统异常: {e}", exc_info=True)
            raise ServerError(f"异步服务处理失败: {str(e)}")
    
    return wrapper


def log_route_context(func: Callable) -> Callable:
    """
    路由上下文日志装饰器

    只记录请求上下文信息，不改变错误处理逻辑
    用于统一路由的日志记录格式

    Args:
        func: 要装饰的路由方法

    Returns:
        装饰后的方法
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)

        # 获取请求上下文信息
        request_info = {
            'method': request.method,
            'path': request.path,
            'remote_addr': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', 'Unknown')
        }

        # 记录请求开始
        logger.debug(f"处理请求: {request_info['method']} {request_info['path']} - IP: {request_info['remote_addr']}")

        try:
            # 执行原始方法
            result = func(*args, **kwargs)

            # 记录请求成功
            logger.debug(f"请求处理成功: {request_info['method']} {request_info['path']}")
            return result

        except Exception as e:
            # 只记录异常，不改变异常处理
            logger.error(f"请求处理异常 - {request_info}: {e}", exc_info=True)
            raise e  # 重新抛出异常，让原有的错误处理逻辑处理

    return wrapper


def log_performance(threshold_ms: int = 1000):
    """
    性能监控装饰器
    
    记录方法执行时间，超过阈值时发出警告
    
    Args:
        threshold_ms: 性能警告阈值（毫秒）
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import time
            logger = logging.getLogger(func.__module__)
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                end_time = time.time()
                duration_ms = (end_time - start_time) * 1000
                
                if duration_ms > threshold_ms:
                    logger.warning(f"方法 {func.__name__} 执行时间过长: {duration_ms:.2f}ms")
                else:
                    logger.debug(f"方法 {func.__name__} 执行时间: {duration_ms:.2f}ms")
        
        return wrapper
    return decorator
