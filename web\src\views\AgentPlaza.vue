<template>
  <div class="agent-plaza-container">
    <div class="plaza-content">
      <header class="plaza-header">
        <div class="header-left">
          <h1 class="plaza-title">智能体应用平台</h1>
        </div>
        <div class="header-right">
          <div class="my-agents-btn">我创建的</div>
          <button class="create-agent-btn">
            <span class="plus-icon">+</span> 创建 AI 智能体
          </button>
        </div>
      </header>

      <!-- 分类标签 -->
      <div class="category-tabs">
        <div class="tabs-wrapper">
          <div 
            v-for="category in agentStore.categories" 
            :key="category.id" 
            :class="['tab', { active: category.active }]"
            @click="switchCategory(category.id)">
            {{ category.name }}
          </div>
          <div v-if="isLoading && agentStore.categories.length === 0" class="tab loading-tab">
            <span class="loading-dots"></span>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-container">
            <input type="text" placeholder="搜索智能体" v-model="searchQuery" class="search-input" />
            <img src="../assets/img/sousuo.png" alt="搜索" class="search-icon" />
          </div>
        </div>
      </div>
      
      <div class="agent-content-wrapper">
        <!-- 加载中提示 -->
        <div v-if="agentStore.isLoading" class="loading-container">
          <div class="loading-spinner"></div>
          <p class="loading-text">正在加载智能体...</p>
        </div>

        <!-- 无数据提示 -->
        <div v-else-if="!agentStore.isLoading && displayAgents.length === 0" class="no-data-container">
          <p class="no-data-text">暂无智能体数据</p>
        </div>

        <!-- 智能体列表 -->
        <div v-else class="agent-grid">
          <div 
            v-for="agent in displayAgents" 
            :key="agent.id" 
            class="agent-card"
            @click="navigateToAgent(agent.id)">
            <div class="agent-header">
              <div class="agent-avatar">
                <!-- 根据图标类型显示不同内容 -->
                <img 
                  v-if="agent.icon_type === 'image' && agent.icon_url" 
                  :src="getIconUrl(agent.icon_url)" 
                  :alt="agent.name" />
                <span v-else-if="agent.icon_type === 'emoji'" class="agent-emoji">
                  {{ agent.icon_url || '🤖' }}
                </span>
                <span v-else class="agent-emoji">🤖</span>
              </div>
              <div class="agent-title">
                <h3>{{ agent.name }}</h3>
                <p class="agent-desc">{{ agent.description || '暂无描述' }}</p>
              </div>
            </div>
            <div class="agent-footer">
              <div class="usage-info">
                <!-- 如果有标签则显示第一个标签 -->
                <span v-if="agent.tags && agent.tags.length > 0">{{ agent.tags[0] }}</span>
                <span v-if="agent.tags && agent.tags.length > 0" class="dot">•</span>
                <span>{{ formatDate(agent.created_at) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 分页导航 -->
        <div v-if="!agentStore.isLoading && totalPages > 0" class="pagination">
          <div class="pagination-content">
            <div class="page-buttons">
              <button 
                class="page-btn prev" 
                :disabled="currentPage === 1"
                @click="changePage(currentPage - 1)">
                上一页
              </button>
              
              <div class="page-numbers">
                <!-- 在移动设备上简化页码显示 -->
                <template v-if="totalPages <= 7">
                  <button 
                    v-for="page in totalPages" 
                    :key="page"
                    :class="['page-num', { active: currentPage === page }]"
                    @click="changePage(page)">
                    {{ page }}
                  </button>
                </template>
                
                <template v-else>
                  <button 
                    v-if="currentPage > 3" 
                    class="page-num"
                    @click="changePage(1)">
                    1
                  </button>
                  
                  <span v-if="currentPage > 3" class="ellipsis">...</span>
                  
                  <button 
                    v-for="page in displayedPages" 
                    :key="page"
                    :class="['page-num', { active: currentPage === page }]"
                    @click="changePage(page)">
                    {{ page }}
                  </button>
                  
                  <span v-if="currentPage < totalPages - 2" class="ellipsis">...</span>
                  
                  <button 
                    v-if="currentPage < totalPages - 2" 
                    class="page-num"
                    @click="changePage(totalPages)">
                    {{ totalPages }}
                  </button>
                </template>
              </div>
              
              <button 
                class="page-btn next" 
                :disabled="currentPage === totalPages"
                @click="changePage(currentPage + 1)">
                下一页
              </button>
            </div>
            
            <div class="page-info">
              共 {{ pagination.total }} 条 / {{ totalPages }} 页
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, reactive } from 'vue';
import { useApiStore } from '../stores/apiStore';
import { useAgentStore } from '../stores/agentStore';
import { useRouter } from 'vue-router';

// 搜索状态
const searchQuery = ref('');
const isLoading = ref(false);
const currentTag = ref('');

// 分页状态
const currentPage = ref(1);
const pageSize = ref(9);
const pagination = reactive({
  total: 0,
  hasMore: false
});

// 获取 store 实例
const apiStore = useApiStore();
const agentStore = useAgentStore();
const router = useRouter();

// 本地存储所有智能体数据（只请求一次）
const allAgents = ref([]);

// 根据当前选中的标签和搜索关键词过滤智能体
const filteredAgents = computed(() => {
  let result = [...allAgents.value];
  
  // 标签筛选
  if (currentTag.value && currentTag.value !== 'all') {
    result = result.filter(agent => 
      agent.tags && 
      agent.tags.includes(currentTag.value)
    );
  }
  
  // 关键词搜索
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(
      agent => 
        agent.name.toLowerCase().includes(query) || 
        (agent.description && agent.description.toLowerCase().includes(query))
    );
  }
  
  // 更新总数据量
  pagination.total = result.length;
  
  return result;
});

// 当前页要展示的智能体
const displayAgents = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  return filteredAgents.value.slice(startIndex, startIndex + pageSize.value);
});

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(filteredAgents.value.length / pageSize.value) || 0;
});

// 切换分类后重置页码并触发重新计算
const switchCategory = (categoryId) => {
  // 更新激活状态
  agentStore.categories.forEach(cat => {
    cat.active = (cat.id === categoryId);
  });
  
  // 设置当前标签
  currentTag.value = categoryId;
  
  // 重置页码
  currentPage.value = 1;
};

// 计算要显示的页码数字
const displayedPages = computed(() => {
  const pages = [];
  const maxVisiblePages = 5;
  
  let startPage = Math.max(1, currentPage.value - Math.floor(maxVisiblePages / 2));
  const endPage = Math.min(startPage + maxVisiblePages - 1, totalPages.value);
  
  if (endPage - startPage + 1 < maxVisiblePages) {
    startPage = Math.max(1, endPage - maxVisiblePages + 1);
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }
  
  return pages;
});

// 更改页码
const changePage = (page) => {
  if (page < 1 || page > totalPages.value) return;
  currentPage.value = page;
};

// 监听搜索框输入变化
watch(searchQuery, () => {
  // 搜索时重置页码
  currentPage.value = 1;
});

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp * 1000);
  return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
};

// 获取图标URL
const getIconUrl = (url) => {
  if (!url) return '';
  // 检查是否是完整URL，如果不是，添加baseURL
  if (url.startsWith('http')) {
    return url;
  } else {
    // 假设使用API配置中的baseUrl
    return '/api' + url;
  }
};

// 导航到智能体详情页
const navigateToAgent = (agentId) => {
  router.push({ name: 'AgentChat', params: { agentId: agentId.toString() } });
};

// 获取分类标签数据
const fetchCategories = async () => {
  try {
    isLoading.value = true;
    await apiStore.getAgentTags();
    
    // 获取当前选中的标签
    const activeCategory = agentStore.categories.find(cat => cat.active);
    if (activeCategory) {
      currentTag.value = activeCategory.id === 'all' ? '' : activeCategory.id;
    }
    
    isLoading.value = false;
  } catch (error) {
    console.error('获取分类标签失败:', error);
    isLoading.value = false;
  }
};

// 获取所有智能体列表
const fetchAllAgents = async () => {
  try {
    isLoading.value = true;
    
    // 请求足够多的数据，实际项目中可能需要多次请求合并
    const result = await apiStore.getAgentList({
      page: 1,
      limit: 100 // 假设最多获取100条数据
    });
    
    if (result && result.items) {
      // 保存所有智能体数据到本地
      allAgents.value = result.items;
      
      // 更新总数量
      pagination.total = result.total;
      pagination.hasMore = result.has_more;
    } else {
      allAgents.value = [];
    }
    
    isLoading.value = false;
  } catch (error) {
    console.error('获取智能体列表失败:', error);
    isLoading.value = false;
  }
};

// 收集所有可用标签
const collectTags = () => {
  // 将代码中已使用的标签收集起来（仅在后端未返回标签时使用）
  const tags = new Set();
  
  allAgents.value.forEach(agent => {
    if (agent.tags && Array.isArray(agent.tags)) {
      agent.tags.forEach(tag => tags.add(tag));
    }
  });
  
  return Array.from(tags);
};



// 组件挂载时加载数据
onMounted(async () => {
  // 获取分类和智能体列表
  await fetchCategories();
  await fetchAllAgents();
  
  // 如果后端没有返回标签，则从智能体数据中收集
  if (agentStore.categories.length <= 1) { // 只有"全部"标签
    const collectedTags = collectTags();
    if (collectedTags.length > 0) {
      const tagObjects = collectedTags.map((tag, index) => ({
        id: tag,
        name: tag,
        active: index === 0
      }));
      
      // 添加"全部"标签
      const allTagObject = { id: 'all', name: '全部', active: true };
      
      // 更新标签列表
      agentStore.setCategories([allTagObject, ...tagObjects]);
    }
  }
});
</script>

<style scoped lang="scss">
.agent-plaza-container {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  position: relative;
}

.plaza-content {
  padding: 20px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  box-sizing: border-box;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  padding-bottom: 40px;
}

.agent-content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 0;
  padding-bottom: 16px;
  position: relative;
}

.plaza-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 28px;
  padding-bottom: 14px;
  position: relative;
}

.plaza-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #1677ff, #4096ff);
  border-radius: 3px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.plaza-title {
  font-size: 26px;
  font-weight: bold;
  color: #1a1a1a;
  margin: 0;
  position: relative;
  letter-spacing: -0.5px;
}

.my-agents-btn {
  padding: 7px 16px;
  font-size: 14px;
  color: #1677ff;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s;
  border: 1px solid rgba(22, 119, 255, 0.3);
  background: rgba(22, 119, 255, 0.05);
  font-weight: 500;
}

.my-agents-btn:hover {
  background: rgba(22, 119, 255, 0.1);
  border-color: rgba(22, 119, 255, 0.5);
  transform: translateY(-1px);
}

.create-agent-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 20px;
  background: #1677ff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
}

.create-agent-btn:hover {
  background: #4096ff;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(22, 119, 255, 0.4);
}

.plus-icon {
  font-size: 16px;
  font-weight: bold;
}

/* 分类标签样式 */
.category-tabs {
  display: flex;
  margin-bottom: 28px;
  align-items: center;
  padding-bottom: 14px;
  justify-content: space-between;
  position: relative;
}

.category-tabs::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #eaeaea;
  z-index: 1;
}

.tabs-wrapper {
  display: flex;
  gap: 16px;
  align-items: center;
}

.tab {
  padding: 8px 18px;
  border-radius: 30px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.25s ease;
  position: relative;
  font-weight: 500;
}

.tab.active {
  background: #1677ff;
  color: white;
  box-shadow: 0 4px 10px rgba(22, 119, 255, 0.25);
}

.tab:not(.active) {
  background: #f5f7fa;
  color: #606060;
}

.tab:not(.active):hover {
  background: #e8f1ff;
  color: #1677ff;
  transform: translateY(-2px);
}

.loading-tab {
  background: #f5f7fa;
  width: 80px;
}

.loading-dots {
  display: inline-block;
  position: relative;
  width: 40px;
  height: 10px;
}

.loading-dots::after {
  content: '...';
  position: absolute;
  left: 0;
  top: -8px;
  font-size: 20px;
  letter-spacing: 2px;
  animation: dotAnimation 1.5s infinite;
}

@keyframes dotAnimation {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60% { content: '...'; }
  80%, 100% { content: ''; }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  flex: 1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(22, 119, 255, 0.1);
  border-radius: 50%;
  border-top: 3px solid #1677ff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 12px;
  color: #606060;
  font-size: 14px;
}

.no-data-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px;
  flex: 1;
}

.no-data-text {
  color: #999;
  font-size: 16px;
}

.search-wrapper {
  flex-shrink: 0;
}

.search-container {
  position: relative;
  width: 265px;
}

.search-input {
  width: 100%;
  padding: 10px 16px 10px 42px;
  border: 1px solid #e6e6e6;
  border-radius: 24px;
  font-size: 14px;
  outline: none;
  transition: all 0.3s;
  background: #f5f7fa;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  box-sizing: border-box;
}

.search-input:focus {
  border-color: #1677ff;
  background: #fff;
  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.1);
}

.search-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  color: #999;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.search-container:hover .search-icon,
.search-input:focus + .search-icon {
  opacity: 1;
}

/* 智能体卡片样式 */
.agent-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  row-gap: 20px;
  flex: 1;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.agent-card {
  background: #fff;
  border-radius: 12px;
  border: 1px solid #eaeaea;
  padding: 16px;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.3, 0, 0.2, 1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.02);
  position: relative;
  overflow: hidden;
  will-change: transform, box-shadow, border-color;
  min-height: 120px;
  max-height: 160px;
  height: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.agent-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
  transform: translateY(-3px);
  border-color: #e6e6e6;
}

.agent-header {
  display: flex;
  gap: 10px;
  margin-bottom: 6px;
}

.agent-avatar {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #f0f4ff, #e6f7ff);
  display: flex;
  align-items: center;
  justify-content: center;
}

.agent-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.agent-emoji {
  font-size: 28px;
  text-align: center;
  user-select: none;
}

.agent-card:hover .agent-avatar img {
  transform: scale(1.05);
}

.agent-title {
  flex: 1;
}

.agent-title h3 {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  transition: color 0.3s, transform 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.agent-card:hover .agent-title h3 {
  transform: translateX(2px);
}

.agent-desc {
  margin: 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-height: 54px;
  opacity: 0.9;
}

.agent-footer {
  margin-top: 6px;
  padding-top: 6px;
  border-top: 1px solid #f0f0f0;
  position: relative;
}

.agent-footer::before {
  content: none;
}

.usage-info {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
  transition: color 0.3s;
}

.agent-card:hover .usage-info {
  color: inherit;
}

.dot {
  margin: 0 6px;
  opacity: 0.6;
}

/* 分页导航 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 32px;
  padding-top: 16px;
  padding-bottom: 32px;
  border-top: 1px solid #eaeaea;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  z-index: 5;
}

.pagination-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
  max-width: 100%;
}

.page-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 8px;
}

.page-btn {
  padding: 7px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 70px;
  text-align: center;
}

.page-btn:hover:not(:disabled) {
  border-color: #1677ff;
  color: #1677ff;
}

.page-btn:disabled {
  color: #d9d9d9;
  cursor: not-allowed;
  background: #f5f5f5;
}

.page-btn.prev {
  margin-right: 8px;
}

.page-btn.next {
  margin-left: 8px;
}

.page-numbers {
  display: flex;
  align-items: center;
}

.page-num {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  border-radius: 4px;
  margin: 0 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.page-num:hover:not(.active) {
  background: #f0f0f0;
}

.page-num.active {
  background: #1677ff;
  color: white;
}

.ellipsis {
  width: 32px;
  text-align: center;
}

.page-info {
  font-size: 14px;
  color: #999;
  text-align: center;
}

@media (max-width: 768px) {
  .agent-plaza-container {
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch;
  }
  
  .plaza-content {
    padding-bottom: 60px;
  }
  
  .plaza-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .category-tabs {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .tabs-wrapper {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 8px;
    gap: 8px;
  }
  
  .search-wrapper {
    width: 100%;
    margin-top: 12px;
  }
  
  .search-container {
    width: 100%;
  }
  
  .agent-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
  
  .pagination {
    margin-top: 20px;
    padding-bottom: 60px;
    position: relative;
    z-index: 10;
    bottom: 0;
  }
  
  .page-buttons {
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;
  }
  
  .page-numbers {
    flex-wrap: wrap;
    justify-content: center;
    margin: 0 4px;
    max-width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .page-btn {
    padding: 5px 12px;
    margin: 0;
    min-width: 60px;
  }
  
  .page-btn.prev,
  .page-btn.next {
    margin: 0;
  }
  
  .page-num {
    width: 28px;
    height: 28px;
    margin: 2px;
  }
  
  .page-info {
    margin-top: 8px;
  }
}

@media (max-width: 480px) {
  .plaza-content {
    padding: 15px;
    padding-bottom: 80px;
  }
  
  .pagination {
    padding-bottom: 80px;
    margin-bottom: 20px;
  }
  
  .page-buttons {
    width: 100%;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  
  .page-numbers {
    max-width: 60%;
    overflow-x: auto;
    justify-content: flex-start;
    -webkit-overflow-scrolling: touch;
    padding: 0 4px;
    margin: 0 4px;
  }
  
  .page-btn {
    min-width: 50px;
    padding: 4px 10px;
    font-size: 13px;
  }
  
  .page-num {
    width: 26px;
    height: 26px;
    font-size: 13px;
  }
  
  .page-info {
    font-size: 12px;
  }
}
</style>