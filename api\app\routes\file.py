"""文件相关路由"""
import os
from flask import request, Response, jsonify, g, current_app
from flask_restx import Namespace, Resource
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage

from app.core.auth_decorators import jwt_required
from app.services.file.file_processor import FileProcessor
from app.models.file import File
from app.utils.dependencies import get_current_active_user
from app.utils.json_utils import serialize_datetime
from app.utils.error_handlers import log_route_context

from app.schemas.file.request import (
    file_upload_request_model,
    file_process_request_model,
    file_reprocess_request_model,
    file_content_request_model,
)
from app.schemas.file.response import (
    file_response_model,
    file_list_response_model,
    file_upload_response_model,
    file_process_response_model,
    file_content_response_model,
)

file_ns = Namespace('file', description='文件操作相关API')

# 定义请求模型
file_upload_req = file_upload_request_model(file_ns)
file_process_req = file_process_request_model(file_ns)
file_reprocess_req = file_reprocess_request_model(file_ns)
file_content_req = file_content_request_model(file_ns)

# 定义响应模型
file_resp = file_response_model(file_ns)
file_list_resp = file_list_response_model(file_ns)
file_upload_resp = file_upload_response_model(file_ns)
file_process_resp = file_process_response_model(file_ns)
file_content_resp = file_content_response_model(file_ns)

# 定义文件上传解析器
upload_parser = file_ns.parser()
upload_parser.add_argument('file', location='files', type=FileStorage, required=True, help='上传的文件')
upload_parser.add_argument('description', type=str, help='文件描述')

@file_ns.route('/upload')
class FileUpload(Resource):
    """文件上传接口"""
    @jwt_required()
    @file_ns.expect(upload_parser)
    @file_ns.response(201, '上传成功', file_upload_resp)
    @file_ns.response(400, '上传失败')
    def post(self):
        """上传文件"""
        # 使用解析器获取文件
        try:
            args = upload_parser.parse_args()
            uploaded_file = args['file']
            description = args.get('description', '')
            
            if not uploaded_file:
                return {'success': False, 'message': '未找到上传的文件'}, 400
                
            current_app.logger.info(f"正在处理文件上传: {uploaded_file.filename}, 类型: {type(uploaded_file)}")
            
            # 获取文件处理器
            file_processor = FileProcessor(current_app.db.session)
            
            # 检查文件类型是否支持
            if not file_processor.is_valid_file_type(uploaded_file.filename):
                return {
                    'success': False, 
                    'message': f'不支持的文件类型，支持的类型: {", ".join(file_processor.ALLOWED_EXTENSIONS)}'
                }, 400
            
            # 检查文件大小 - 尝试从不同的属性获取
            file_size = getattr(uploaded_file, 'content_length', None)
            if file_size is None:
                # 尝试其他可能的属性
                if hasattr(uploaded_file, 'size'):
                    file_size = uploaded_file.size
                elif hasattr(uploaded_file, '_file') and hasattr(uploaded_file._file, 'tell') and hasattr(uploaded_file._file, 'seek'):
                    pos = uploaded_file._file.tell()
                    uploaded_file._file.seek(0, os.SEEK_END)
                    file_size = uploaded_file._file.tell()
                    uploaded_file._file.seek(pos)  # 重置文件指针
                else:
                    # 如果无法判断大小，假设它不超过限制
                    file_size = 0
            
            if file_size > file_processor.MAX_FILE_SIZE:
                return {
                    'success': False, 
                    'message': f'文件大小超过限制，最大支持 {file_processor.MAX_FILE_SIZE_MB}MB'
                }, 400
            
            # 获取当前活跃用户
            current_user = get_current_active_user()
            user_id = current_user.user_id
            
            # 确保user_id是整数类型
            if not isinstance(user_id, int):
                try:
                    user_id = int(user_id)
                except (ValueError, TypeError):
                    current_app.logger.error(f"用户ID格式错误: {user_id}, 类型: {type(user_id)}")
                    return {'success': False, 'message': '用户身份验证错误'}, 400
            
            current_app.logger.info(f"正在保存文件，用户ID: {user_id}, 类型: {type(user_id)}")
            db_file = file_processor.save_file(uploaded_file, user_id, description)
            
            # 上传后立即处理文件，不使用异步任务队列
            current_app.logger.info(f"正在处理文件: {db_file.file_id}")
            try:
                # 直接调用处理方法，同步等待处理完成
                file_processor.process_file(db_file.file_id)
                
                # 重新获取文件状态
                current_app.db.session.refresh(db_file)
                
                # 处理文件成功的情况
                if db_file.status == "processed":
                    message = "文件上传并处理成功"
                elif db_file.status == "failed":
                    message = "文件上传成功但处理失败"
                else:
                    message = "文件上传成功，处理状态未知"
                    
                current_app.logger.info(f"文件处理完成，状态: {db_file.status}")
            except Exception as e:
                current_app.logger.error(f"文件处理失败: {str(e)}")
                message = "文件上传成功但处理出错"
                # 不要因为处理失败而返回错误，仍然返回文件上传成功
            
            # 序列化响应，确保datetime被正确处理
            response_data = {
                'success': True,
                'message': message,
                'file': {
                    'file_id': db_file.file_id,
                    'filename': db_file.filename,
                    'file_type': db_file.file_type,
                    'file_size': db_file.file_size,
                    'upload_time': db_file.upload_time,
                    'status': db_file.status,
                    'description': db_file.description
                }
            }
            
            # 使用serialize_datetime处理datetime对象
            return serialize_datetime(response_data), 201
        except Exception as e:
            current_app.logger.error(f"文件上传失败: {str(e)}", exc_info=True)
            return {'success': False, 'message': f'文件上传失败: {str(e)}'}, 400


@file_ns.route('/list')
class FileList(Resource):
    """文件列表接口"""
    @jwt_required()
    @file_ns.response(200, '成功', file_list_resp)
    def get(self):
        """获取文件列表"""
        skip = request.args.get('skip', default=0, type=int)
        limit = request.args.get('limit', default=20, type=int)
        
        file_processor = FileProcessor(current_app.db.session)
        current_user = get_current_active_user()
        user_id = current_user.user_id
        
        files = file_processor.get_user_files(user_id, skip, limit)
        total = file_processor.count_user_files(user_id)
        
        response_data = {
            'total': total,
            'skip': skip,
            'limit': limit,
            'files': [{
                'file_id': f.file_id,
                'filename': f.filename,
                'file_type': f.file_type,
                'file_size': f.file_size,
                'upload_time': f.upload_time,
                'status': f.status,
                'description': f.description
            } for f in files]
        }
        
        return serialize_datetime(response_data), 200


@file_ns.route('/detail/<string:file_id>')
class FileDetail(Resource):
    """文件详情接口"""
    @jwt_required()
    @file_ns.response(200, '成功', file_resp)
    @file_ns.response(404, '文件不存在')
    def get(self, file_id):
        """获取文件详情"""
        file_processor = FileProcessor(current_app.db.session)
        current_user = get_current_active_user()
        user_id = current_user.user_id
        
        file = current_app.db.session.query(File).filter(
            File.file_id == file_id,
            File.user_id == user_id
        ).first()
        
        if not file:
            return {'message': '文件不存在'}, 404
        
        response_data = {
            'file_id': file.file_id,
            'filename': file.filename,
            'file_type': file.file_type,
            'file_size': file.file_size,
            'upload_time': file.upload_time,
            'status': file.status,
            'description': file.description
        }
        
        return serialize_datetime(response_data), 200


@file_ns.route('/process')
class FileProcess(Resource):
    """文件处理接口"""
    @jwt_required()
    @file_ns.expect(file_process_req)
    @file_ns.response(200, '成功', file_process_resp)
    @file_ns.response(404, '文件不存在')
    @file_ns.response(400, '处理失败')
    def post(self):
        """处理文件"""
        data = request.json
        file_id = data.get('file_id')
        
        file_processor = FileProcessor(current_app.db.session)
        current_user = get_current_active_user()
        user_id = current_user.user_id
        
        # 获取文件信息
        file = current_app.db.session.query(File).filter(
            File.file_id == file_id,
            File.user_id == user_id
        ).first()
        
        if not file:
            return {'success': False, 'message': '文件不存在'}, 404
        
        try:
            # 处理文件 - 直接调用，不使用await
            file_processor.process_file(file_id)
            
            # 重新获取更新后的文件信息
            file = current_app.db.session.query(File).filter(
                File.file_id == file_id
            ).first()
            
            response_data = {
                'success': True,
                'message': '文件处理成功',
                'file': {
                    'file_id': file.file_id,
                    'filename': file.filename,
                    'file_type': file.file_type,
                    'file_size': file.file_size,
                    'upload_time': file.upload_time,
                    'status': file.status,
                    'description': file.description
                }
            }
            
            return serialize_datetime(response_data), 200
        except Exception as e:
            current_app.logger.error(f"文件处理失败: {str(e)}")
            return {'success': False, 'message': f'文件处理失败: {str(e)}'}, 400


@file_ns.route('/reprocess')
class FileReprocess(Resource):
    """文件重新处理接口"""
    @jwt_required()
    @file_ns.expect(file_reprocess_req)
    @file_ns.response(200, '成功', file_process_resp)
    @file_ns.response(404, '文件不存在')
    def post(self):
        """重新处理文件"""
        data = request.json
        file_id = data.get('file_id')
        
        file_processor = FileProcessor(current_app.db.session)
        current_user = get_current_active_user()
        user_id = current_user.user_id
        
        # 获取文件信息
        file = current_app.db.session.query(File).filter(
            File.file_id == file_id,
            File.user_id == user_id
        ).first()
        
        if not file:
            return {'success': False, 'message': '文件不存在'}, 404
        
        # 重置文件状态
        success = file_processor.reprocess_file(file_id)
        
        if not success:
            return {'success': False, 'message': '重置文件状态失败'}, 400
        
        # 重新获取文件状态
        file = current_app.db.session.query(File).filter(
            File.file_id == file_id
        ).first()
        
        response_data = {
            'success': True,
            'message': '文件已重置，等待处理',
            'file': {
                'file_id': file.file_id,
                'filename': file.filename,
                'file_type': file.file_type,
                'file_size': file.file_size,
                'upload_time': file.upload_time,
                'status': file.status,
                'description': file.description
            }
        }
        
        return serialize_datetime(response_data), 200


@file_ns.route('/content')
class FileContent(Resource):
    """文件内容接口"""
    @jwt_required()
    @file_ns.expect(file_content_req)
    @file_ns.response(200, '成功', file_content_resp)
    @file_ns.response(400, '获取失败')
    def post(self):
        """获取文件内容"""
        data = request.json
        file_ids = data.get('file_ids', [])
        
        if not file_ids:
            return {'success': False, 'message': '未指定文件ID'}, 400
        
        file_processor = FileProcessor(current_app.db.session)
        current_user = get_current_active_user()
        user_id = current_user.user_id
        
        # 获取文件内容
        try:
            files_content = file_processor.get_files_content(file_ids, user_id)
            
            if not files_content:
                return {'success': False, 'message': '未找到有效文件或文件未处理完成'}, 400
            
            response_data = {
                'success': True,
                'message': '获取文件内容成功',
                'files': files_content
            }
            
            return serialize_datetime(response_data), 200
        except Exception as e:
            current_app.logger.error(f"获取文件内容失败: {str(e)}")
            return {'success': False, 'message': f'获取文件内容失败: {str(e)}'}, 400


@file_ns.route('/delete/<string:file_id>')
class FileDelete(Resource):
    """文件删除接口"""
    @jwt_required()
    @log_route_context
    @file_ns.response(200, '删除成功')
    @file_ns.response(404, '文件不存在')
    def delete(self, file_id):
        """删除文件"""
        file_processor = FileProcessor(current_app.db.session)
        current_user = get_current_active_user()
        user_id = current_user.user_id
        
        # 获取文件信息
        file = current_app.db.session.query(File).filter(
            File.file_id == file_id,
            File.user_id == user_id
        ).first()
        
        if not file:
            return {'success': False, 'message': '文件不存在'}, 404
        
        # 删除文件
        try:
            # 删除文件内容
            file_processor.delete_file_contents(str(user_id), file_id)
            
            # 删除原始文件
            if os.path.exists(file.file_path):
                os.remove(file.file_path)
            
            # 删除数据库记录
            current_app.db.session.delete(file)
            current_app.db.session.commit()
            
            return {'success': True, 'message': '文件删除成功'}, 200
        except Exception as e:
            current_app.logger.error(f"文件删除失败: {str(e)}")
            return {'success': False, 'message': f'文件删除失败: {str(e)}'}, 400 