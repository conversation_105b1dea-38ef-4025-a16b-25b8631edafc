"""
AIGC工具坊API控制器
处理HTTP请求和响应，调用服务层处理业务逻辑
"""

from flask import Blueprint, request, Response, send_file
from flask_restx import Namespace, Resource, fields
from werkzeug.datastructures import FileStorage
import os

from app.core.auth_decorators import jwt_required
from app.services.aigc_service import (
    AigcCategoryService,
    AigcToolService,
    AigcFavoriteService,
    AigcStatsService
)
from app.utils.errors import APIError
from app import db

# 创建蓝图
aigc_tools_bp = Blueprint('aigc_tools', __name__, url_prefix='/api/aigc-tools')

# 创建命名空间
ns = Namespace('aigc-tools', description='AIGC工具坊相关接口')

# 定义API模型
category_model = ns.model('Category', {
    'id': fields.Integer(description='分类ID'),
    'name': fields.String(required=True, description='分类名称'),
    'description': fields.String(description='分类描述'),
    'icon': fields.String(description='分类图标'),
    'color': fields.String(description='主题色'),
    'sort_order': fields.Integer(description='排序权重'),
    'is_active': fields.Boolean(description='是否启用'),
    'tool_count': fields.Integer(description='工具数量'),
    'created_at': fields.String(description='创建时间'),
    'updated_at': fields.String(description='更新时间')
})

tool_model = ns.model('Tool', {
    'id': fields.Integer(description='工具ID'),
    'name': fields.String(required=True, description='工具名称'),
    'description': fields.String(required=True, description='工具描述'),
    'url': fields.String(required=True, description='工具链接'),
    'icon': fields.String(description='工具图标(emoji)'),
    'icon_type': fields.String(description='图标类型(emoji|image)', enum=['emoji', 'image']),
    'icon_image_url': fields.String(description='图片图标URL'),
    'category_id': fields.Integer(required=True, description='分类ID'),
    'category': fields.String(description='分类名称'),
    'tags': fields.List(fields.String, description='标签列表'),
    'is_active': fields.Boolean(description='是否启用'),
    'is_hot': fields.Boolean(description='是否热门'),
    'is_favorite': fields.Boolean(description='是否已收藏'),
    'click_count': fields.Integer(description='点击次数'),
    'favorite_count': fields.Integer(description='收藏次数'),
    'sort_order': fields.Integer(description='排序权重'),
    'created_at': fields.String(description='创建时间'),
    'updated_at': fields.String(description='更新时间')
})

favorite_model = ns.model('Favorite', {
    'id': fields.Integer(description='收藏ID'),
    'user_id': fields.Integer(description='用户ID'),
    'tool_id': fields.Integer(description='工具ID'),
    'created_at': fields.String(description='收藏时间')
})

stats_model = ns.model('Stats', {
    'total_tools': fields.Integer(description='总工具数'),
    'active_tools': fields.Integer(description='启用工具数'),
    'hot_tools': fields.Integer(description='热门工具数'),
    'total_clicks': fields.Integer(description='总点击数'),
    'total_categories': fields.Integer(description='总分类数'),
    'active_categories': fields.Integer(description='启用分类数')
})


@ns.route('/categories')
class CategoryListAPI(Resource):
    @ns.doc('get_categories')
    @ns.marshal_list_with(category_model)
    def get(self):
        """获取分类列表"""
        try:
            is_active = request.args.get('is_active')
            if is_active is not None:
                is_active = is_active.lower() == 'true'

            categories = AigcCategoryService.get_categories(is_active=is_active)
            return categories

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'获取分类列表失败: {str(e)}')

    @ns.doc('create_category')
    @ns.expect(category_model)
    @ns.marshal_with(category_model)
    @jwt_required()
    def post(self):
        """创建新分类"""
        try:
            data = request.get_json()
            category = AigcCategoryService.create_category(data)
            return category, 201

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'创建分类失败: {str(e)}')


@ns.route('/categories/<int:category_id>')
class CategoryAPI(Resource):
    @ns.doc('get_category')
    @ns.marshal_with(category_model)
    def get(self, category_id):
        """获取分类详情"""
        try:
            category = AigcCategoryServi-ce.get_category_by_id(category_id)
            return category

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'获取分类详情失败: {str(e)}')

    @ns.doc('update_category')
    @ns.expect(category_model)
    @ns.marshal_with(category_model)
    @jwt_required()
    def put(self, category_id):
        """更新分类"""
        try:
            data = request.get_json()
            category = AigcCategoryService.update_category(category_id, data)
            return category

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'更新分类失败: {str(e)}')

    @ns.doc('delete_category')
    @jwt_required()
    def delete(self, category_id):
        """删除分类"""
        try:
            AigcCategoryService.delete_category(category_id)
            return {'message': '分类删除成功'}

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'删除分类失败: {str(e)}')


@ns.route('/tools')
class ToolListAPI(Resource):
    def _get_optional_user_id(self):
        """可选地获取当前用户ID，不强制要求登录"""
        try:
            # 获取Authorization头
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                return None

            # 提取访问令牌
            access_token = auth_header[7:]  # 移除 "Bearer " 前缀

            # 验证访问令牌
            from app.core.jwt_manager import jwt_manager
            payload = jwt_manager.decode_access_token(access_token)

            return payload['user_id']

        except Exception:
            # 如果JWT验证失败，返回None（用户未登录或token无效）
            return None
    @ns.doc('get_tools')
    def get(self):
        """获取工具列表"""
        try:
            # 获取查询参数
            category_id = request.args.get('category_id', type=int)
            is_active = request.args.get('is_active')
            is_hot = request.args.get('is_hot')
            search = request.args.get('search')
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            sort_by = request.args.get('sort_by', 'sort_order')

            # 转换布尔参数
            if is_active is not None:
                is_active = is_active.lower() == 'true'
            if is_hot is not None:
                is_hot = is_hot.lower() == 'true'

            # 尝试获取当前用户ID（可选，不强制要求登录）
            user_id = self._get_optional_user_id()

            result = AigcToolService.get_tools(
                category_id=category_id,
                is_active=is_active,
                is_hot=is_hot,
                search=search,
                page=page,
                per_page=per_page,
                sort_by=sort_by,
                user_id=user_id
            )

            return result

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'获取工具列表失败: {str(e)}')

    @ns.doc('create_tool')
    @ns.expect(tool_model)
    @ns.marshal_with(tool_model)
    @jwt_required()
    def post(self):
        """创建新工具"""
        try:
            data = request.get_json()
            tool = AigcToolService.create_tool(data)
            return tool, 201

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'创建工具失败: {str(e)}')


@ns.route('/tools/<int:tool_id>')
class ToolAPI(Resource):
    def _get_optional_user_id(self):
        """可选地获取当前用户ID，不强制要求登录"""
        try:
            # 获取Authorization头
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                return None

            # 提取访问令牌
            access_token = auth_header[7:]  # 移除 "Bearer " 前缀

            # 验证访问令牌
            from app.core.jwt_manager import jwt_manager
            payload = jwt_manager.decode_access_token(access_token)

            return payload['user_id']

        except Exception:
            # 如果JWT验证失败，返回None（用户未登录或token无效）
            return None
    @ns.doc('get_tool')
    @ns.marshal_with(tool_model)
    def get(self, tool_id):
        """获取工具详情"""
        try:
            user_id = self._get_optional_user_id()
            result = AigcToolService.get_tool_by_id(tool_id, user_id)
            return result
        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'获取工具失败: {str(e)}')

    @ns.doc('update_tool')
    @ns.expect(tool_model)
    @ns.marshal_with(tool_model)
    @jwt_required()
    def put(self, tool_id):
        """更新工具"""
        try:
            data = request.get_json()
            result = AigcToolService.update_tool(tool_id, data)
            return result

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'更新工具失败: {str(e)}')

    @ns.doc('delete_tool')
    @jwt_required()
    def delete(self, tool_id):
        """删除工具"""
        try:
            # 使用服务层删除工具（会自动清理图标文件）
            AigcToolService.delete_tool(tool_id)
            return {'message': '工具删除成功'}

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'删除工具失败: {str(e)}')







@ns.route('/tools/<int:tool_id>/click')
class ToolClickAPI(Resource):
    @ns.doc('record_click')
    def post(self, tool_id):
        """记录工具点击"""
        try:
            user_id = getattr(request, 'current_user_id', None)
            result = AigcToolService.record_click(
                tool_id=tool_id,
                user_id=user_id,
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )

            return {'message': '点击记录成功', **result}

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'记录点击失败: {str(e)}')


@ns.route('/favorites')
class FavoriteListAPI(Resource):
    @ns.doc('get_favorites')
    def get(self):
        """获取用户收藏的工具列表"""
        try:
            user_id = getattr(request, 'current_user_id', None)
            if not user_id:
                ns.abort(401, '请先登录')

            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)

            result = AigcFavoriteService.get_user_favorites(
                user_id=user_id,
                page=page,
                per_page=per_page
            )

            return result

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'获取收藏列表失败: {str(e)}')


@ns.route('/tools/<int:tool_id>/favorite')
class ToolFavoriteAPI(Resource):
    @ns.doc('toggle_favorite')
    @jwt_required()
    def post(self, tool_id):
        """切换工具收藏状态"""
        try:
            user_id = request.current_user_id
            result = AigcFavoriteService.toggle_favorite(user_id, tool_id)
            return result

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'收藏操作失败: {str(e)}')


@ns.route('/stats')
class StatsAPI(Resource):
    @ns.doc('get_stats')
    @ns.marshal_with(stats_model)
    def get(self):
        """获取统计数据"""
        try:
            stats = AigcStatsService.get_stats()
            return stats

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'获取统计数据失败: {str(e)}')


@ns.route('/upload-icon')
class ToolIconUploadAPI(Resource):
    """工具图标上传API"""

    @ns.doc('upload_tool_icon')
    @jwt_required()
    def post(self):
        """上传工具图标"""
        try:
            # 检查是否有文件上传
            if 'file' not in request.files:
                ns.abort(400, '没有上传文件')

            file = request.files['file']
            if file.filename == '':
                ns.abort(400, '文件名不能为空')

            # 获取工具ID
            tool_id = request.form.get('tool_id')
            if not tool_id:
                ns.abort(400, '工具ID不能为空')

            try:
                tool_id = int(tool_id)
            except ValueError:
                ns.abort(400, '工具ID必须是数字')

            # 使用服务层处理图标上传
            result = AigcToolService.upload_icon(tool_id, file)

            return {
                'code': 200,
                'message': '图标上传成功',
                'data': result
            }

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except ValueError as e:
            ns.abort(400, str(e))
        except Exception as e:
            ns.abort(500, f'上传图标失败: {str(e)}')


@ns.route('/icons/<int:tool_id>')
class ToolIconAPI(Resource):
    """工具图标管理API"""

    @ns.doc('get_tool_icon')
    def get(self, tool_id):
        """获取工具图标"""
        try:
            # 使用服务层获取图标文件路径
            icon_path = AigcToolService.get_icon_path(tool_id)

            # 返回图标文件
            return send_file(
                icon_path,
                as_attachment=False,
                mimetype='image/png'  # 默认MIME类型，浏览器会自动检测
            )

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'获取图标失败: {str(e)}')

    @ns.doc('delete_tool_icon')
    @jwt_required()
    def delete(self, tool_id):
        """删除工具图标"""
        try:
            # 使用服务层处理图标删除
            result = AigcToolService.delete_icon(tool_id)

            return {
                'code': 200,
                'message': result['message'],
                'data': {
                    'tool': result['tool']
                }
            }

        except APIError as e:
            ns.abort(e.status_code, e.message)
        except Exception as e:
            ns.abort(500, f'删除图标失败: {str(e)}')


# 注册到蓝图
def register_routes(api):
    """注册路由到API"""
    api.add_namespace(ns, path='/api/aigc-tools')
