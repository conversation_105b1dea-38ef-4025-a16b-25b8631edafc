import { defineStore } from 'pinia';

export const useApiConfigStore = defineStore('apiConfig', {
  state: () => ({
    // 使用相对路径，通过代理访问后端
    baseUrl: '/api', // 相对于当前域名的路径，对应Vite代理配置中的'/api'前缀
    
    // 文件服务相对路径，如果与API服务使用相同代理，则与baseUrl相同
    // 如果需要单独配置文件服务代理，可改为'/file-api'
    get fileServerUrl() {
      return this.baseUrl; // 当前与API服务使用相同的代理路径
    },
    
    endpoints: {
      login: '/auth/login', //登录接口 (已更新为新接口)
      refresh_token: '/auth/refresh', //刷新token接口 (已更新为新接口)
      userInfo: '/auth/verify', //获取用户信息 (已更新为新接口)
      logout: '/auth/logout', //登出接口 (新增)
      orders: '/orders',
      // 其他端点...
      chat:{
        conversations:"/chat/conversations", // 获取用户所有的对话记录
        delete_conversation:"/chat/conversations/delete", // 删除用户的对话记录
        conversation_history:"/chat/history", //获取相应对话的对话记录
        model_list:"/chat/models", // 获取模型列表
        send_message: "/chat/message" // 发送消息
      },
      agent: {
        tags: "/agent/tags", // 获取智能体分类标签
        list: "/agent/list", // 获取智能体列表
        detail: "/agent/:id", // 获取智能体详情，使用:id占位符表示动态ID
        parameters: "/agent/:id/parameters", // 获取智能体参数信息
        chat: "/agent/chat" // 智能体聊天接口
      },
      file: {
        upload: '/file/upload', // 文件上传接口
        delete: '/file/delete'  // 文件删除接口
      },
      channels: {
        get_model_list: '/channels/get_model_list', // 获取所有启用的模型列表
        get_channel_model_list: '/channels/get_channel_model_list', // 获取指定渠道的模型列表
        change_model_status: '/channels/change_model_status', // 切换模型启用/禁用状态
        add_model: '/channels/add_model', // 添加模型到渠道
        get_channel_list: '/channels/get_channel_list', // 获取渠道列表
        create: '/channels/create', // 创建渠道
        delete_channel: '/channels/delete_channel', // 删除渠道
        change_channel_status: '/channels/change_channel_status' // 切换渠道状态
      },
      aigc: {
        categories: '/aigc-tools/categories', // 获取分类列表
        tools: '/aigc-tools/tools', // 获取工具列表
        tool_detail: '/aigc-tools/tools/:id', // 获取工具详情
        tool_click: '/aigc-tools/tools/:id/click', // 记录工具点击
        favorites: '/aigc-tools/favorites', // 获取收藏列表
        toggle_favorite: '/aigc-tools/tools/:id/favorite', // 切换收藏状态
        stats: '/aigc-tools/stats', // 获取统计数据
        upload_icon: '/aigc-tools/upload-icon', // 上传工具图标
        tool_icon: '/aigc-tools/icons/:id' // 获取工具图标
      },
      admin: {
        system_monitor: '/admin/system/monitor', // 获取系统监控数据
        system_health: '/admin/system/health', // 获取系统健康状态
        dashboard_overview: '/admin/dashboard/overview', // 获取仪表盘概览
        dashboard_charts: '/admin/dashboard/charts', // 获取图表数据
        cache_info: '/admin/cache/info', // 获取缓存信息
        cache_clear: '/admin/cache/clear' // 清空缓存
      },
      admin: {
        system_monitor: '/admin/system/monitor', // 获取系统监控数据
        system_health: '/admin/system/health', // 获取系统健康状态
        dashboard_overview: '/admin/dashboard/overview', // 获取仪表盘概览
        dashboard_charts: '/admin/dashboard/charts', // 获取图表数据
        cache_info: '/admin/cache/info', // 获取缓存信息
        cache_clear: '/admin/cache/clear' // 清空缓存
      }
    }
  }),
  getters: {
    // 方法一：调用URL的方法
    fullUrls(state) {
      return {
        login: state.baseUrl + state.endpoints.login,
        userInfo: state.baseUrl + state.endpoints.userInfo,
        refresh_token: state.baseUrl + state.endpoints.refresh_token,
        orders: state.baseUrl + state.endpoints.orders,
        // 其他端点...
      }
    },
    // 方法二：动态获取URL的方法
    getUrl: (state) => (endpoint) => {
      if (!state.endpoints[endpoint]) {
        console.log(`在接口数据里面没有找到"${endpoint}"接口`);
        return '';
      }
      return state.baseUrl + state.endpoints[endpoint];
    },
    // 获取嵌套的端点URL，支持动态参数替换
    getNestUrl: (state) => (parent, child, params = {}) => {
      if (!state.endpoints[parent] || !state.endpoints[parent][child]) {
        console.log(`在接口数据里面没有找到"${parent}.${child}"接口`);
        return '';
      }
      
      let url = state.baseUrl + state.endpoints[parent][child];
      
      // 替换URL中的动态参数占位符，如 :id
      if (params) {
        Object.keys(params).forEach(key => {
          url = url.replace(`:${key}`, params[key]);
        });
      }
      
      return url;
    },
    // 文件服务器URL获取方法
    getFileUrl: (state) => (endpoint) => {
      if (!state.endpoints.file[endpoint]) {
        console.log(`在文件服务接口里面没有找到"${endpoint}"接口`);
        return '';
      }
      return state.fileServerUrl + state.endpoints.file[endpoint];
    }
  },
});