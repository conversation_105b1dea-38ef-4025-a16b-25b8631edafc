<template>
  <div>
    <!-- 已上传文件显示区域 -->
    <div class="uploaded-files" v-if="uploadedFiles.length > 0">
      <div v-for="(file, index) in uploadedFiles" :key="index" class="file-item">
        <div class="file-icon">
          <img src="../../assets/img/Pdf.png" alt="pdf" class="pdf-icon" v-if="file.type === 'application/pdf'" />
          <img src="../../assets/img/Docx.png" alt="docx" class="pdf-icon"
            v-else-if="file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'" />
        </div>
        <div class="file-info">
          <div class="file-name" :title="file.name">
            {{ getDisplayFileName(file.name) }}
          </div>
          <div class="file-size">
            {{ getFileTypeText(file.type) }} · {{ formatFileSize(file.size) }}
          </div>
          <div class="file-status">
            <span v-if="file.status === 'uploading'" class="upload-status">上传中...</span>
            <span v-else-if="file.status === 'success'" class="upload-success">已上传</span>
          </div>
        </div>
        <div class="file-close" @click="removeFile(index)">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
            <path fill="currentColor"
              d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41z">
            </path>
          </svg>
        </div>
      </div>
    </div>

    <!-- 上传按钮 -->
    <el-upload class="upload-btn" action="#" :auto-upload="false" :show-file-list="false" @change="handleFileChange">
      <el-button link>
        <img src="../../assets/svg/upload.svg" alt="upload" class="upload-icon" />
      </el-button>
    </el-upload>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { externalService } from '@/utils/axios'

// 移除未使用的props
// const props = defineProps({
//   // 这里可以添加需要的属性
// })

const emit = defineEmits(['update:files'])

// 上传文件列表
const uploadedFiles = ref([])

// 添加格式化文件大小的函数
const formatFileSize = (size) => {
  if (size < 1024) {
    return size + 'B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(0) + 'KB'
  } else {
    return (size / (1024 * 1024)).toFixed(0) + 'MB'
  }
}

// 移除文件函数
const removeFile = async (index) => {
  const fileToRemove = uploadedFiles.value[index]

  if (fileToRemove.file_id) {
    try {
      // 调用删除文件API - 修复：使用DELETE方法和URL参数
      await externalService.delete(`http://***********:8000/file/delete/${fileToRemove.file_id}`)

      // 从本地列表中移除
      uploadedFiles.value.splice(index, 1)

      // 通知父组件文件列表已更新
      emit('update:files', uploadedFiles.value)

      // 显示删除成功提示
      ElMessage.success('文件已删除')
    } catch (error) {
      console.error('删除文件失败:', error)
      ElMessage.error('删除文件失败，请重试')
    }
  } else {
    // 如果没有file_id，可能是上传失败的文件，直接从本地移除
    uploadedFiles.value.splice(index, 1)
    emit('update:files', uploadedFiles.value)
  }
}

// 文件变化处理函数
const handleFileChange = async (file) => {
  // 检查文件类型
  const fileType = file.raw.type
  if (fileType === 'application/pdf' || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    try {
      // 创建FormData对象用于文件上传
      const formData = new FormData()
      formData.append('file', file.raw)
      formData.append('description', '从聊天界面上传')

      // 生成唯一ID用于标识文件
      const fileId = Date.now().toString()

      // 显示上传中状态
      const tempFileObj = {
        id: fileId,
        name: file.raw.name,
        size: file.raw.size,
        type: file.raw.type,
        status: 'uploading'
      }

      // 先添加到列表中显示上传中状态
      uploadedFiles.value.push(tempFileObj)
      emit('update:files', uploadedFiles.value)

      // 调用上传API
      const response = await externalService.post('http://***********:8000/file/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      // 上传成功，更新文件信息
      if (response.data && response.data.file_id) {
        // 更新临时对象为服务器返回的数据
        const index = uploadedFiles.value.findIndex(f => f.id === fileId)
        if (index !== -1) {
          // 使用解构和重新赋值确保响应式更新
          const updatedFiles = [...uploadedFiles.value]
          updatedFiles[index] = {
            ...updatedFiles[index],
            file_id: response.data.file_id,
            status: 'success'
          }
          uploadedFiles.value = updatedFiles

          // 通知父组件文件列表已更新
          emit('update:files', uploadedFiles.value)
        } else {
          // 找不到要更新的文件对象
        }
      } else {
        throw new Error('上传响应缺少file_id')
      }
    } catch (error) {
      ElMessage.error('上传文件失败，请重试')

      // 从列表中移除上传失败的文件
      const index = uploadedFiles.value.findIndex(f =>
        f.name === file.raw.name && f.size === file.raw.size && f.status === 'uploading'
      )

      if (index !== -1) {
        uploadedFiles.value.splice(index, 1)
        emit('update:files', uploadedFiles.value)
      }
    }
  } else {
    ElMessage.warning('目前只支持PDF和DOCX文件')
  }
}

// 获取文件类型显示文本
const getFileTypeText = (fileType) => {
  if (fileType === 'application/pdf') {
    return 'PDF';
  } else if (fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    return 'DOCX';
  }
  return '文件';
}

// 分离文件名和后缀名
const splitFileName = (fileName) => {
  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1) {
    return { name: fileName, ext: '' };
  }
  return {
    name: fileName.substring(0, lastDotIndex),
    ext: fileName.substring(lastDotIndex) // 保留后缀名的点号
  };
}

// 修改文件名显示逻辑，当文件名较短时完整显示，当过长时只显示名称部分并加省略号
const getDisplayFileName = (fileName) => {
  const maxLength = 15; // 最大显示长度

  // 如果整个文件名（包括扩展名）不超过最大长度，则完整显示
  if (fileName.length <= maxLength) {
    return fileName;
  }

  const split = splitFileName(fileName);

  // 如果文件名部分已经很长，则截断文件名并添加省略号
  if (split.name.length > maxLength - 3) { // 为省略号预留3个字符
    return split.name.substring(0, maxLength - 3) + '...';
  }

  // 否则显示尽可能多的文件名，加上省略号
  return split.name + '...';
}
</script>

<style scoped>
/* 上传文件显示样式 */
.uploaded-files {
  width: 100%;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 6px 10px;
  margin-bottom: 8px;
  width: 195px;
  min-height: 52px;
  box-sizing: border-box;
}

.file-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
}

.pdf-icon {
  width: 32px;
  height: 32px;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  max-width: 120px;
  gap: 1px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  max-width: 140px;
  line-height: 1.4;
}

.file-size {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  line-height: 1.3;
}

.file-status {
  font-size: 12px;
  line-height: 1.3;
}

.file-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  cursor: pointer;
  color: #909399;
  border-radius: 50%;
}

.file-close:hover {
  background: rgba(0, 0, 0, 0.06);
  color: #333;
}

/* 上传按钮样式 */
.upload-btn {
  margin-left: -2px;
}

.upload-btn :deep(.el-button) {
  padding: 8px;
  font-size: 20px;
  color: #333;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-btn :deep(.el-button:hover) {
  background: rgba(0, 0, 0, 0.06) !important;
  border-color: #e4e7ed;
  color: #333;
}

.upload-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0);
  /* 将SVG图标变为黑色 */
}

/* 上传状态样式 */
.upload-status {
  color: #409eff;
  font-size: 12px;
  animation: blink 1.5s infinite;
  white-space: nowrap;
}

.upload-success {
  color: #67c23a;
  font-size: 12px;
  white-space: nowrap;
}

@keyframes blink {
  0% {
    opacity: 0.5;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.5;
  }
}
</style>