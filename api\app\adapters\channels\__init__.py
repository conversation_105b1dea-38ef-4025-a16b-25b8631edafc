"""渠道适配器包"""
from .base import BaseChannel
from .factory import ChannelFactory
from .mock import MockChannel
from .openai_channel import OpenAIChannel
from .ollama_channel import OllamaChannel
from .zhipu_channel import ZhipuChannel

# 注册渠道类型
ChannelFactory.register_channel("openai", OpenAIChannel)
ChannelFactory.register_channel("ollama", OllamaChannel)
ChannelFactory.register_channel("zhipu", ZhipuChannel)
ChannelFactory.register_channel('mock', MockChannel)

__all__ = [
    'BaseChannel', 
    'ChannelFactory', 
    'MockChannel',
    'OpenAIChannel',
    'OllamaChannel',
    'ZhipuChannel'
] 