<template>
  <header class="main-header">
    <!-- 智能体信息显示区域 (只在智能体聊天页面显示在左侧) -->
    <div class="agent-info-section" v-if="showAgentInfo">
      <div class="back-button" @click="goBack">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M19 12H5"></path>
          <path d="M12 19l-7-7 7-7"></path>
        </svg>
      </div>
      <div class="agent-info">
        <div class="agent-avatar" :class="{'loading': isAgentLoading}" :style="agent.iconType === 'emoji' ? { backgroundColor: agent.iconBackground || '#FFEAD5' } : {}">
          <img 
            v-if="agent.iconType === 'image' && agent.iconUrl" 
            :src="agent.iconUrl" 
            :alt="agent.name" 
            @error="handleImageError" />
          <span v-else-if="agent.iconType === 'emoji'" class="agent-emoji">
            {{ agent.iconUrl || '🤖' }}
          </span>
          <span v-else class="agent-emoji">🤖</span>
          <div v-if="isAgentLoading" class="loading-overlay">
            <span class="loading-spinner"></span>
          </div>
        </div>
        <div class="agent-details">
          <h3 class="agent-name">{{ agent.name || '智能体助手' }}</h3>
          <p class="agent-description" :class="{'loading-text': isAgentLoading}">
            {{ isAgentLoading ? '加载中...' : (agent.description || '开始与智能体对话吧') }}
          </p>
        </div>
      </div>
    </div>

    <!-- 用户头像和下拉菜单（始终显示在右侧） -->
    <span class="user-avatar-menu">
      <div class="avatar-dropdown">
        <!-- 用户头像，点击时切换下拉菜单显示状态 -->
        <img src="../assets/svg/logo.svg" alt="普米智能体" class="user-avatar" @click="toggleDropdown" draggable="false" />
        <!-- 下拉菜单，根据showDropdown状态显示或隐藏 -->
        <div class="dropdown-menu" v-show="showDropdown">
          <!-- 管理后台选项 - 只有管理员和教师可见 -->
          <div v-if="isAdminOrTeacher" class="dropdown-item" @click="handleAdmin">
            <el-icon>
              <Management />
            </el-icon>
            <span>管理后台</span>
          </div>
          <!-- 设置选项 -->
          <div class="dropdown-item" @click="handleSettings">
            <el-icon>
              <Setting />
            </el-icon>
            <span>设置</span>
          </div>
          <!-- 退出登录选项 -->
          <div class="dropdown-item" @click="handleLogout">
            <el-icon>
              <SwitchButton />
            </el-icon>
            <span>退出登录</span>
          </div>
        </div>
      </div>
    </span>
  </header>
</template>

<script setup>
import { useAuthStore } from '@/stores/authStore';
import { useUserStore } from '@/stores/userStore';
import { ElMessage } from 'element-plus';
import { useRouter, useRoute } from 'vue-router'
import { Setting, Management } from '@element-plus/icons-vue'
import { onMounted, ref, computed, reactive, watch } from 'vue'
import { useApiStore } from '@/stores/apiStore';
import * as tokenService from '@/services/tokenService';

const router = useRouter()
const route = useRoute()
const apiStore = useApiStore()
const authStore = useAuthStore()
const userStore = useUserStore()
// 响应式获取用户名
const name = computed(() => userStore.userProfile.username || '用户')
const isAgentLoading = ref(false)

// 检查用户是否是管理员或教师
const isAdminOrTeacher = computed(() => {
  const userRole = userStore.userProfile.role
  return userRole === 'admin' || userRole === 'teacher'
})

// 判断当前是否在智能体聊天页面
const showAgentInfo = computed(() => {
  return route.name === 'AgentChat' && route.params.agentId;
});

// 智能体数据
const agent = reactive({
  id: '',
  name: '智能体助手',
  description: '我是一个AI智能体，可以帮助你回答问题',
  iconType: 'emoji',
  iconUrl: '🤖',
  iconBackground: '#FFEAD5'
});

// 处理图片加载错误
const handleImageError = () => {
  // 图片加载失败，回退到emoji模式
  agent.iconType = 'emoji';
  agent.iconUrl = '🤖';
};

// 返回上一页
const goBack = () => {
  router.go(-1);
};

const handleLogout = async () => {
  try {
    // 调用新的登出API
    await apiStore.logout()
    router.push({
      path: '/login',
      replace: true
    })
    ElMessage.success('退出成功')
  } catch (error) {
    // 即使API调用失败，也要清除本地数据并跳转
    authStore.clearAuthData()
    router.push({
      path: '/login',
      replace: true
    })
    ElMessage.success('退出成功')
  }
}

const showDropdown = ref(false)

const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

const handleSettings = () => {
  ElMessage.info('设置功能开发中')
  showDropdown.value = false
}

const handleAdmin = () => {
  router.push('/admin')
  showDropdown.value = false
}

// 获取图标URL处理函数
const getIconUrl = (url) => {
  if (!url) return '';
  // 检查是否是完整URL，如果不是，添加baseURL
  if (url.startsWith('http')) {
    return url;
  } else {
    // 假设使用API配置中的baseUrl
    return '/api' + url;
  }
};

// 获取智能体信息
const loadAgentInfo = async () => {
  if (showAgentInfo.value && route.params.agentId) {
    try {
      // 设置加载状态
      isAgentLoading.value = true;
      
      // 调用API获取智能体的详细信息
      const agentData = await apiStore.getAgentDetail(route.params.agentId);
      
      if (agentData) {
        // 更新agent对象
        agent.id = agentData.id;
        agent.name = agentData.name;
        agent.description = agentData.description || '暂无描述';
        
        // 处理图标类型
        agent.iconType = agentData.icon_type || 'emoji';
        
        if (agent.iconType === 'emoji') {
          // 使用返回的emoji图标
          agent.iconUrl = agentData.icon;
          agent.iconBackground = agentData.icon_background || '#FFEAD5';
        } else if (agent.iconType === 'image' && agentData.icon_url) {
          // 使用返回的图片URL，但需要处理相对路径
          agent.iconUrl = getIconUrl(agentData.icon_url);
        } else {
          // 默认为机器人emoji
          agent.iconType = 'emoji';
          agent.iconUrl = '🤖';
          agent.iconBackground = '#FFEAD5';
        }
        
        // 更新文档标题
        document.title = `${agent.name} | 普米智能体`;
      }
    } catch (error) {
      console.error('获取智能体数据失败:', error);
      // 加载失败时使用默认值
      agent.id = route.params.agentId;
      agent.name = '智能体助手';
      agent.description = '无法获取智能体信息';
      agent.iconType = 'emoji';
      agent.iconUrl = '🤖';
      agent.iconBackground = '#FFEAD5';
    } finally {
      // 无论成功还是失败，都结束加载状态
      setTimeout(() => {
        isAgentLoading.value = false;
      }, 300); // 添加短暂延迟，使过渡更平滑
    }
  }
};

onMounted(async () => {
  try {
    // 检查令牌是否存在且有效
    if (!tokenService.getAccessToken() || tokenService.isTokenExpired(tokenService.getAccessToken())) {
      router.replace('/login');
      return;
    }

    // 尝试获取用户信息
    try {
      await apiStore.getUserInfo();
      // 用户信息会自动保存到userStore，name计算属性会自动更新
    } catch (error) {
      // API请求错误，交由axios拦截器处理
      console.error('获取用户信息失败:', error);
      // 注意：401错误会被axios拦截器捕获并处理令牌刷新
      // 这里不需要手动处理刷新令牌了
    }

    // 如果是智能体聊天页面，加载智能体信息
    loadAgentInfo();
  } catch (error) {
    ElMessage.error('会话已过期，请重新登录');
    authStore.clearAuthData();
    router.replace('/login');
  }
})

// 监听路由参数变化，当agentId变化时重新加载智能体信息
watch(
  () => route.params.agentId,
  (newAgentId, oldAgentId) => {
    if (newAgentId && newAgentId !== oldAgentId) {
      // 设置加载状态，但保留当前信息直到新数据加载完成
      isAgentLoading.value = true;
      
      // 加载新的智能体信息
      loadAgentInfo();
    }
  }
);
</script>

<style scoped lang="scss">
.main-header {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  background-color: #fafbfc;
}

/**
 * 智能体信息区域样式 - 在左侧显示
 */
.agent-info-section {
  display: flex;
  align-items: center;
  max-width: 70%;
  justify-content: flex-start;
  overflow: hidden;
}

.back-button {
  width: 32px;
  height: 32px;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: background 0.2s;
  color: #555;
  margin-right: 12px;
  
  &:hover {
    background: #f0f0f0;
    color: #1677ff;
  }
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 12px;
  overflow: hidden;
  transition: opacity 0.3s ease;
}

.agent-avatar {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  background: linear-gradient(135deg, #f0f4ff, #e6f7ff);
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  
  &.loading {
    opacity: 0.7;
  }
  
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
  }
  
  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #eee;
    border-top: 2px solid #1677ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
  }
  
  .agent-emoji {
    font-size: 20px;
    text-align: center;
    user-select: none;
    transition: opacity 0.3s ease;
  }
}

.agent-details {
  overflow: hidden;
  transition: all 0.3s ease;
  
  .agent-name {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: opacity 0.3s ease;
  }
  
  .agent-description {
    margin: 0;
    font-size: 12px;
    color: #666;
    max-width: 280px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: opacity 0.3s ease;
    
    &.loading-text {
      color: #aaa;
      animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.6; }
    }
  }
}

/**
 * 用户头像菜单容器 - 在右侧显示
 */
.user-avatar-menu {
  cursor: pointer;
  display: flex;
  align-items: center;
  margin-left: auto;
}

/**
 * 用户头像样式及其交互效果
 */
.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #eef2f6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 6px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 头像悬停效果 */
.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  background: #e6f0ff;
}

/* 头像点击效果 */
.user-avatar:active {
  transform: scale(0.98);
  transition: all 0.1s ease;
}

/**
 * 头像下拉菜单容器
 */
.avatar-dropdown {
  position: relative;
  cursor: pointer;
}

/**
 * 下拉菜单样式及动画
 */
.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 5px;
  margin-top: 8px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 120px;
  z-index: 1000;
  padding: 4px;
  transform-origin: top right;
  animation: dropdownFadeIn 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 下拉菜单动画效果 */
@keyframes dropdownFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-8px);
  }

  50% {
    opacity: 0.5;
    transform: scale(1.05) translateY(-4px);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/**
 * 下拉菜单项样式
 */
.dropdown-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  border-radius: 8px;
  transition: all 0.2s ease;
}

/* 菜单项悬停效果 */
.dropdown-item:hover {
  background: transparent;
  color: #409eff;
}

/* 菜单项图标样式 */
.dropdown-item .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* 菜单项文本样式 */
.dropdown-item span {
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .main-header {
    padding: 0 16px;
  }
  
  .agent-details .agent-description {
    max-width: 180px;
  }
  
  .back-button {
    margin-left: 8px;
  }
}
</style>