import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import Elementplus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
// 导入全局axios拦截器配置
import './services/axiosConfig'
// 导入后台令牌刷新服务
import tokenRefreshService from './services/tokenRefreshService'

const app = createApp(App)
const pinia = createPinia()

// 配置Pinia插件
pinia.use(piniaPluginPersistedstate)

// 注册应用插件和组件
app.use(pinia)
app.use(router)
app.use(Elementplus)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 启动后台令牌刷新服务
if (process.env.NODE_ENV !== 'production') {
  console.log('启动后台令牌刷新服务...')
  // 导入测试工具
  import('@/utils/tokenTest').then(({ tokenTester }) => {
    console.log('令牌测试工具已加载，使用 testTokenRefresh() 进行测试')
    console.log('使用 checkTokenHealth() 快速检查令牌健康状态')
  })
}
tokenRefreshService.start()

app.mount('#app')
