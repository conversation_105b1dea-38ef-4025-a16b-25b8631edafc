"""
AIGC工具坊服务层
包含AIGC工具坊相关的业务逻辑和数据访问方法
"""

from typing import List, Dict, Optional
from sqlalchemy import desc, func, or_
from sqlalchemy.exc import IntegrityError

from app import db
from app.models import AigcCategory, AigcTool, AigcUserFavorite, AigcClickLog
from app.utils.errors import APIError
from app.services.file.icon_processor import IconProcessor


class AigcCategoryService:
    """AIGC分类服务类"""

    @staticmethod
    def get_categories(is_active: Optional[bool] = None) -> List[Dict]:
        """获取分类列表"""
        query = AigcCategory.query

        if is_active is not None:
            query = query.filter(AigcCategory.is_active == is_active)

        categories = query.order_by(desc(AigcCategory.sort_order), AigcCategory.created_at).all()
        return [category.to_dict() for category in categories]
    
    @staticmethod
    def get_category_by_id(category_id: int) -> Dict:
        """根据ID获取分类"""
        category = AigcCategory.query.get(category_id)
        if not category:
            raise APIError('分类不存在', 404)
        return category.to_dict()
    
    @staticmethod
    def create_category(data: Dict) -> Dict:
        """创建分类"""
        # 验证必填字段
        if not data.get('name'):
            raise APIError('分类名称不能为空', 400)
        
        # 检查分类名称是否已存在
        existing = AigcCategory.query.filter_by(name=data['name']).first()
        if existing:
            raise APIError('分类名称已存在', 400)
        
        try:
            category = AigcCategory(
                name=data['name'],
                description=data.get('description'),
                icon=data.get('icon'),
                color=data.get('color', '#1677ff'),
                sort_order=data.get('sort_order', 0),
                is_active=data.get('is_active', True)
            )
            
            db.session.add(category)
            db.session.commit()
            
            return category.to_dict()
            
        except Exception as e:
            db.session.rollback()
            raise APIError(f'创建分类失败: {str(e)}', 500)
    
    @staticmethod
    def update_category(category_id: int, data: Dict) -> Dict:
        """更新分类"""
        category = AigcCategory.query.get(category_id)
        if not category:
            raise APIError('分类不存在', 404)
        
        # 检查分类名称是否已被其他分类使用
        if data.get('name') and data['name'] != category.name:
            existing = AigcCategory.query.filter_by(name=data['name']).first()
            if existing:
                raise APIError('分类名称已存在', 400)
        
        try:
            # 更新字段
            for field in ['name', 'description', 'icon', 'color', 'sort_order', 'is_active']:
                if field in data:
                    setattr(category, field, data[field])
            
            db.session.commit()
            return category.to_dict()
            
        except Exception as e:
            db.session.rollback()
            raise APIError(f'更新分类失败: {str(e)}', 500)
    
    @staticmethod
    def delete_category(category_id: int) -> None:
        """删除分类"""
        category = AigcCategory.query.get(category_id)
        if not category:
            raise APIError('分类不存在', 404)
        
        # 检查是否有工具使用此分类
        tool_count = AigcTool.query.filter_by(category_id=category_id).count()
        if tool_count > 0:
            raise APIError(f'该分类下还有{tool_count}个工具，无法删除', 400)
        
        try:
            db.session.delete(category)
            db.session.commit()
            
        except Exception as e:
            db.session.rollback()
            raise APIError(f'删除分类失败: {str(e)}', 500)


class AigcToolService:
    """AIGC工具服务"""
    
    @staticmethod
    def get_tools(
        category_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        is_hot: Optional[bool] = None,
        search: Optional[str] = None,
        page: int = 1,
        per_page: int = 20,
        sort_by: str = 'sort_order',
        user_id: Optional[int] = None
    ) -> Dict:
        """获取工具列表"""
        query = AigcTool.query
        
        # 应用筛选条件
        if category_id:
            query = query.filter(AigcTool.category_id == category_id)
        
        if is_active is not None:
            query = query.filter(AigcTool.is_active == is_active)
        
        if is_hot is not None:
            query = query.filter(AigcTool.is_hot == is_hot)
        
        if search:
            search_term = f'%{search}%'
            query = query.filter(
                or_(
                    AigcTool.name.like(search_term),
                    AigcTool.description.like(search_term)
                )
            )
        
        # 应用排序
        if sort_by == 'click_count':
            query = query.order_by(desc(AigcTool.click_count))
        elif sort_by == 'created_at':
            query = query.order_by(desc(AigcTool.created_at))
        elif sort_by == 'name':
            query = query.order_by(AigcTool.name)
        else:  # sort_order
            query = query.order_by(desc(AigcTool.sort_order), desc(AigcTool.created_at))
        
        # 分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        
        tools = [tool.to_dict(user_id=user_id) for tool in pagination.items]
        
        return {
            'tools': tools,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        }
    
    @staticmethod
    def get_tool_by_id(tool_id: int, user_id: Optional[int] = None) -> Dict:
        """根据ID获取工具"""
        tool = AigcTool.query.get(tool_id)
        if not tool:
            raise APIError('工具不存在', 404)
        return tool.to_dict(user_id=user_id)
    
    @staticmethod
    def create_tool(data: Dict) -> Dict:
        """创建工具"""
        # 验证必填字段
        required_fields = ['name', 'description', 'url', 'category_id']
        for field in required_fields:
            if not data.get(field):
                raise APIError(f'{field}不能为空', 400)
        
        # 验证分类是否存在
        category = AigcCategory.query.get(data['category_id'])
        if not category:
            raise APIError('指定的分类不存在', 400)
        
        # 检查工具名称是否已存在
        existing = AigcTool.query.filter_by(name=data['name']).first()
        if existing:
            raise APIError('工具名称已存在', 400)
        
        try:
            tool = AigcTool(
                name=data['name'],
                description=data['description'],
                url=data['url'],
                icon=data.get('icon'),
                icon_type=data.get('icon_type', 'emoji'),
                icon_image_url=data.get('icon_image_url'),
                category_id=data['category_id'],
                tags=data.get('tags', []),
                is_active=data.get('is_active', True),
                is_hot=data.get('is_hot', False),
                sort_order=data.get('sort_order', 0)
            )
            
            db.session.add(tool)
            db.session.commit()
            
            return tool.to_dict()
            
        except Exception as e:
            db.session.rollback()
            raise APIError(f'创建工具失败: {str(e)}', 500)
    
    @staticmethod
    def update_tool(tool_id: int, data: Dict) -> Dict:
        """更新工具"""
        tool = AigcTool.query.get(tool_id)
        if not tool:
            raise APIError('工具不存在', 404)
        
        # 检查工具名称是否已被其他工具使用
        if data.get('name') and data['name'] != tool.name:
            existing = AigcTool.query.filter_by(name=data['name']).first()
            if existing:
                raise APIError('工具名称已存在', 400)
        
        # 验证分类是否存在
        if data.get('category_id') and data['category_id'] != tool.category_id:
            category = AigcCategory.query.get(data['category_id'])
            if not category:
                raise APIError('指定的分类不存在', 400)
        
        try:
            # 更新字段
            for field in ['name', 'description', 'url', 'icon', 'icon_type', 'icon_image_url',
                         'category_id', 'tags', 'is_active', 'is_hot', 'sort_order']:
                if field in data:
                    setattr(tool, field, data[field])
            
            db.session.commit()
            return tool.to_dict()
            
        except Exception as e:
            db.session.rollback()
            raise APIError(f'更新工具失败: {str(e)}', 500)
    
    @staticmethod
    def delete_tool(tool_id: int) -> None:
        """删除工具"""
        tool = AigcTool.query.get(tool_id)
        if not tool:
            raise APIError('工具不存在', 404)

        try:
            # 如果有图片图标，先删除图标文件
            if tool.icon_type == 'image' and tool.icon_image_url:
                icon_processor = IconProcessor(db.session)
                icon_processor.delete_icon(tool.icon_image_url)

            # 删除相关的收藏记录和点击日志
            AigcUserFavorite.query.filter_by(tool_id=tool_id).delete()
            AigcClickLog.query.filter_by(tool_id=tool_id).delete()

            db.session.delete(tool)
            db.session.commit()

        except Exception as e:
            db.session.rollback()
            raise APIError(f'删除工具失败: {str(e)}', 500)

    @staticmethod
    def upload_icon(tool_id: int, file, user_id: Optional[int] = None) -> Dict:
        """上传工具图标"""
        # 验证工具是否存在
        tool = AigcTool.query.get(tool_id)
        if not tool:
            raise APIError('工具不存在', 404)

        try:
            # 创建图标处理器
            icon_processor = IconProcessor(db.session)

            # 如果工具已有图片图标，先删除旧的
            if tool.icon_type == 'image' and tool.icon_image_url:
                icon_processor.delete_icon(tool.icon_image_url)

            # 保存新图标文件
            icon_url = icon_processor.save_icon(file, tool_id, user_id)

            # 更新工具的图标信息
            tool.icon_type = 'image'
            tool.icon_image_url = icon_url

            db.session.commit()

            return {
                'tool': tool.to_dict(user_id=user_id),
                'icon_url': icon_url
            }

        except ValueError as e:
            # IconProcessor抛出的验证错误
            raise APIError(str(e), 400)
        except Exception as e:
            db.session.rollback()
            raise APIError(f'上传图标失败: {str(e)}', 500)

    @staticmethod
    def delete_icon(tool_id: int, user_id: Optional[int] = None) -> Dict:
        """删除工具图标"""
        # 验证工具是否存在
        tool = AigcTool.query.get(tool_id)
        if not tool:
            raise APIError('工具不存在', 404)

        # 检查是否有图片图标
        if tool.icon_type != 'image' or not tool.icon_image_url:
            return {
                'tool': tool.to_dict(user_id=user_id),
                'message': '工具没有图片图标，无需删除'
            }

        try:
            # 创建图标处理器
            icon_processor = IconProcessor(db.session)

            # 删除图标文件
            icon_processor.delete_icon(tool.icon_image_url)

            # 更新工具的图标信息，回退到emoji
            tool.icon_type = 'emoji'
            tool.icon_image_url = None

            db.session.commit()

            return {
                'tool': tool.to_dict(user_id=user_id),
                'message': '图标删除成功'
            }

        except Exception as e:
            db.session.rollback()
            raise APIError(f'删除图标失败: {str(e)}', 500)

    @staticmethod
    def get_icon_path(tool_id: int) -> Optional[str]:
        """获取工具图标文件路径"""
        # 验证工具是否存在
        tool = AigcTool.query.get(tool_id)
        if not tool:
            raise APIError('工具不存在', 404)

        # 检查是否有图片图标
        if tool.icon_type != 'image' or not tool.icon_image_url:
            raise APIError('工具没有图片图标', 404)

        # 创建图标处理器
        icon_processor = IconProcessor(db.session)

        # 获取图标文件路径
        icon_path = icon_processor.get_icon_path(tool.icon_image_url)
        if not icon_path:
            raise APIError('图标文件不存在', 404)

        return icon_path

    @staticmethod
    def switch_icon_type(tool_id: int, icon_type: str, user_id: Optional[int] = None) -> Dict:
        """切换图标类型"""
        if icon_type not in ['emoji', 'image']:
            raise APIError('图标类型必须是emoji或image', 400)

        # 验证工具是否存在
        tool = AigcTool.query.get(tool_id)
        if not tool:
            raise APIError('工具不存在', 404)

        # 如果切换到emoji类型，需要清理图片文件
        if icon_type == 'emoji' and tool.icon_type == 'image' and tool.icon_image_url:
            try:
                icon_processor = IconProcessor(db.session)
                icon_processor.delete_icon(tool.icon_image_url)

                tool.icon_type = 'emoji'
                tool.icon_image_url = None

                db.session.commit()

            except Exception as e:
                db.session.rollback()
                raise APIError(f'切换图标类型失败: {str(e)}', 500)

        # 如果切换到image类型，但没有图片，只更新类型
        elif icon_type == 'image':
            tool.icon_type = 'image'
            db.session.commit()

        return {
            'tool': tool.to_dict(user_id=user_id),
            'message': f'图标类型已切换为{icon_type}'
        }

    @staticmethod
    def record_click(tool_id: int, user_id: Optional[int] = None, ip_address: str = None, user_agent: str = None) -> Dict:
        """记录工具点击"""
        tool = AigcTool.query.get(tool_id)
        if not tool:
            raise APIError('工具不存在', 404)
        
        try:
            # 增加点击次数
            tool.click_count += 1
            
            # 记录点击日志
            click_log = AigcClickLog(
                user_id=user_id,
                tool_id=tool_id,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            db.session.add(click_log)
            db.session.commit()
            
            return {'click_count': tool.click_count}
            
        except Exception as e:
            db.session.rollback()
            raise APIError(f'记录点击失败: {str(e)}', 500)


class AigcFavoriteService:
    """AIGC收藏服务"""

    @staticmethod
    def get_user_favorites(user_id: int, page: int = 1, per_page: int = 20) -> Dict:
        """获取用户收藏的工具列表"""
        query = db.session.query(AigcTool).join(
            AigcUserFavorite, AigcTool.id == AigcUserFavorite.tool_id
        ).filter(
            AigcUserFavorite.user_id == user_id,
            AigcTool.is_active == True
        ).order_by(desc(AigcUserFavorite.created_at))

        pagination = query.paginate(page=page, per_page=per_page, error_out=False)

        tools = [tool.to_dict(user_id=user_id) for tool in pagination.items]

        return {
            'tools': tools,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        }

    @staticmethod
    def toggle_favorite(user_id: int, tool_id: int) -> Dict:
        """切换工具收藏状态"""
        tool = AigcTool.query.get(tool_id)
        if not tool:
            raise APIError('工具不存在', 404)

        # 检查是否已收藏
        favorite = AigcUserFavorite.query.filter_by(
            user_id=user_id,
            tool_id=tool_id
        ).first()

        try:
            if favorite:
                # 取消收藏
                db.session.delete(favorite)
                tool.favorite_count = max(0, tool.favorite_count - 1)
                is_favorite = False
                message = '取消收藏成功'
            else:
                # 添加收藏
                favorite = AigcUserFavorite(user_id=user_id, tool_id=tool_id)
                db.session.add(favorite)
                tool.favorite_count += 1
                is_favorite = True
                message = '收藏成功'

            db.session.commit()

            return {
                'message': message,
                'is_favorite': is_favorite,
                'favorite_count': tool.favorite_count
            }

        except IntegrityError:
            db.session.rollback()
            raise APIError('收藏操作失败，请稍后重试', 400)
        except Exception as e:
            db.session.rollback()
            raise APIError(f'收藏操作失败: {str(e)}', 500)


class AigcStatsService:
    """AIGC统计服务"""

    @staticmethod
    def get_stats() -> Dict:
        """获取统计数据"""
        try:
            stats = {
                'total_tools': AigcTool.query.count(),
                'active_tools': AigcTool.query.filter_by(is_active=True).count(),
                'hot_tools': AigcTool.query.filter_by(is_hot=True, is_active=True).count(),
                'total_clicks': db.session.query(func.sum(AigcTool.click_count)).scalar() or 0,
                'total_categories': AigcCategory.query.count(),
                'active_categories': AigcCategory.query.filter_by(is_active=True).count()
            }

            return stats

        except Exception as e:
            raise APIError(f'获取统计数据失败: {str(e)}', 500)
