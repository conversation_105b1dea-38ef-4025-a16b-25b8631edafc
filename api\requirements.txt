# ⚠️ 已迁移到 pyproject.toml
# 此文件保留作为备份，新项目请使用 uv 管理依赖
#
# 迁移命令：
# uv sync                    # 安装所有依赖
# uv add package-name        # 添加新依赖
# uv add --dev package-name  # 添加开发依赖
# uv run python script.py   # 运行脚本
#
# 原始依赖列表（已迁移）：

# Flask核心框架
Flask==3.0.0
Werkzeug==3.0.1

# API文档
Flask-RESTX==1.3.0

# 数据库相关
Flask-SQLAlchemy==3.1.1
SQLAlchemy==2.0.23
PyMySQL==1.1.0
cryptography==41.0.7

# JWT认证 - 使用自定义JWT管理器，无需外部依赖

# CORS支持
Flask-CORS==4.0.0

# 环境变量管理
python-dotenv==1.0.0

# 数据验证
marshmallow==3.20.1
Flask-Marshmallow==0.15.0

# HTTP客户端
requests==2.31.0
httpx==0.25.2

# 日志和监控
gunicorn==21.2.0

# 开发工具（可选）
Flask-DebugToolbar==0.13.1

# 数据迁移
Flask-Migrate==4.0.5

# 文件解析依赖
mammoth==1.6.0
markdownify==0.11.6
PyMuPDF==1.23.7  # 用于解析PDF文件