<template>
  <div class="history-container">
    <div class="history-title">历史对话</div>
    <div class="history-list">
      <div v-for="chat in chatList" :key="chat.conversation_id" class="menu-item history-item" @click="selectChat(chat.conversation_id)">
        <div class="menu-icon">
          <img src="../../assets/img/His_talk.png" alt="历史对话" width="24" height="24" />
        </div>
        <div class="chat-info">
          <span class="menu-text history-title-text" :class="{ 'title-animate': chat.animateTitle }">{{ chat.title
          }}</span>
        </div>
      </div>
      <!-- 无历史对话时显示提示 -->
      <div v-if="!chatList.length" class="empty-history">
        <p>暂无历史对话</p>
        <p class="empty-tip">点击上方"新对话"开始聊天</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 接收来自父组件的属性
defineProps({
  chatList: {
    type: Array,
    default: () => []
  }
})

// 向父组件发送事件
const emit = defineEmits(['select-chat'])

// 选择历史对话
const selectChat = (id) => {
  emit('select-chat', id)
}
</script>

<style scoped>
.history-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  /* 防止内容溢出 */
  height: 100%;
  /* 填充父容器高度 */
}

.history-title {
  font-size: 13px;
  color: #909399;
  margin: 12px 0 4px 20px;
  flex-shrink: 0;
  /* 不收缩 */
}

.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px;
  max-height: 100%;
  /* 最大高度为父容器剩余空间 */
  scrollbar-width: thin;
  /* Firefox */
  scrollbar-color: #d4d4d4 transparent;
  /* Firefox */
}

/* Webkit浏览器的滚动条样式 */
.history-list::-webkit-scrollbar {
  width: 6px;
}

.history-list::-webkit-scrollbar-track {
  background: transparent;
}

.history-list::-webkit-scrollbar-thumb {
  background-color: #d4d4d4;
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}

.history-item {
  height: 48px;
}

/* 菜单项样式 - 需要和SidebarMenu组件保持一致 */
.menu-item {
  display: flex;
  align-items: center;
  height: 48px;
  width: 100%;
  padding: 0 15px;
  border-radius: 10px;
  margin: 4px 0;
  box-sizing: border-box;
  cursor: pointer;
  transition: background 0.2s;
  color: #333;
}

.menu-item:hover {
  background: rgba(0, 0, 0, 0.06);
}

.menu-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: #606060;
}

.chat-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.menu-text {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-title-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: opacity 0.3s ease;
  /* 添加基础过渡效果 */
}

/* 添加标题动画类 */
.title-animate {
  animation: titleFade 1.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.empty-history {
  padding: 20px 0;
  text-align: center;
  color: #909399;
}

.empty-tip {
  font-size: 12px;
  margin-top: 8px;
  color: #c0c4cc;
}

@keyframes titleFade {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 0.1;
  }

  40% {
    opacity: 0.3;
  }

  60% {
    opacity: 0.5;
  }

  80% {
    opacity: 0.8;
  }

  100% {
    opacity: 1;
  }
}
</style>