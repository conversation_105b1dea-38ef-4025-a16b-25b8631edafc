"""
依赖函数模块
提供路由依赖，如获取当前用户等
"""

from flask import request
from app.services.user_service import UserService
from app.services.auth_service import auth_service
from app.utils.errors import TokenError, UserBanned
from app.models.user import User
from typing import Optional


def get_current_user() -> User:
    """
    获取当前用户（需要在@jwt_required装饰的路由中使用）

    Returns:
        当前用户对象

    Raises:
        TokenError: 令牌无效或用户不存在
    """
    if not hasattr(request, 'current_user_id'):
        raise TokenError("请求中没有用户信息")

    user_id = request.current_user_id
    if not user_id:
        raise TokenError("令牌中没有用户信息")

    user = auth_service.get_current_user_by_id(user_id)
    if not user:
        raise TokenError("用户不存在")

    return user


def get_current_active_user() -> User:
    """
    获取当前活跃用户（需要在@jwt_required装饰的路由中使用）
    
    Returns:
        当前活跃用户对象
        
    Raises:
        TokenError: 令牌无效或用户不存在
        UserBanned: 用户已被禁用
    """
    user = get_current_user()
    
    if user.disabled:
        raise UserBanned()
    
    return user


def get_current_admin_user() -> User:
    """
    获取当前管理员用户（需要在@jwt_required装饰的路由中使用）
    
    Returns:
        当前管理员用户对象
        
    Raises:
        TokenError: 令牌无效或用户不存在
        UserBanned: 用户已被禁用
        PermissionDenied: 用户不是管理员
    """
    from app.utils.errors import PermissionDenied
    
    user = get_current_active_user()
    
    if not user.is_admin:
        raise PermissionDenied("需要管理员权限")
    
    return user 