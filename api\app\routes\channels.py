"""
渠道和模型相关路由
包含渠道和模型的增删改查功能
"""

from flask import request
from flask_restx import Namespace, Resource

from app.core.auth_decorators import jwt_required

from app.services.channel_service import ChannelService, ModelService
from app.schemas.channel_schemas import (
    channel_create_model,
    channel_response_model,
    model_response_model,
    model_create_model,
    channel_id_model,
    model_operation_model
)
from app.utils.dependencies import get_current_active_user
from app.utils.errors import PermissionDenied


channels_ns = Namespace('channels', description='渠道和模型管理')

# 将模型添加到当前命名空间
channels_ns.models[channel_create_model.name] = channel_create_model
channels_ns.models[channel_response_model.name] = channel_response_model
channels_ns.models[model_response_model.name] = model_response_model
channels_ns.models[model_create_model.name] = model_create_model
channels_ns.models[channel_id_model.name] = channel_id_model
channels_ns.models[model_operation_model.name] = model_operation_model


@channels_ns.route('/get_channel_list')
class ChannelListResource(Resource):
    @channels_ns.marshal_list_with(channel_response_model)
    @jwt_required()
    @channels_ns.doc(security=['Login'])
    def get(self):
        """获取所有渠道列表"""
        # 验证用户权限
        user = get_current_active_user()
        if not user.is_admin and not user.is_teacher:
            raise PermissionDenied("只有管理员和教师可以查看渠道列表")

        channels = ChannelService.get_channel_list()
        return channels


@channels_ns.route('/create')
class ChannelCreateResource(Resource):
    @channels_ns.expect(channel_create_model)
    @channels_ns.marshal_with(channel_response_model, code=201)
    @jwt_required()
    @channels_ns.doc(security=['Login'])
    def post(self):
        """创建新的渠道"""
        # 验证用户权限
        user = get_current_active_user()
        if not user.is_admin:
            raise PermissionDenied("只有管理员可以创建渠道")

        data = request.get_json()
        channel = ChannelService.create_channel(
            channel_name=data['channel_name'],
            channel_url=data['channel_url'],
            channel_api_key=data['channel_api_key'],
            channel_api_type=data['channel_api_type']
        )

        return channel, 201


@channels_ns.route('/get_model_list')
class ModelListResource(Resource):
    @channels_ns.marshal_list_with(model_response_model)
    def get(self):
        """获取所有启用的模型列表"""
        models = ModelService.get_model_list()
        return models


@channels_ns.route('/get_channel_model_list')
class ChannelModelListResource(Resource):
    @channels_ns.expect(channel_id_model)
    @channels_ns.marshal_list_with(model_response_model)
    @jwt_required()
    @channels_ns.doc(security=['Login'])
    def post(self):
        """获取指定渠道的模型列表"""
        # 验证用户权限
        user = get_current_active_user()
        if not user.is_admin:
            raise PermissionDenied("只有管理员可以查看渠道模型列表")

        data = request.get_json()
        channel_id = data['channel_id']

        models = ModelService.get_channel_model_list(channel_id)
        if models is None:
            raise ValueError("渠道不存在")

        return models


@channels_ns.route('/delete_channel')
class ChannelDeleteResource(Resource):
    @jwt_required()
    @channels_ns.expect(channel_id_model)
    @channels_ns.doc(security=['Login'])
    def post(self):
        """删除渠道"""
        # 验证用户权限
        user = get_current_active_user()
        if not user.is_admin:
            raise PermissionDenied("只有管理员可以删除渠道")

        data = request.get_json()
        channel_id = data['channel_id']

        success = ChannelService.delete_channel(channel_id)
        if not success:
            raise ValueError("渠道不存在")

        return {"message": "渠道删除成功"}, 204


@channels_ns.route('/change_channel_status')
class ChannelStatusResource(Resource):
    @jwt_required()
    @channels_ns.expect(channel_id_model)
    @channels_ns.doc(security=['Login'])
    def post(self):
        """切换渠道启用/禁用状态"""
        # 验证用户权限
        user = get_current_active_user()
        if not user.is_admin:
            raise PermissionDenied("只有管理员可以修改渠道状态")

        data = request.get_json()
        channel_id = data['channel_id']

        success = ChannelService.change_channel_status(channel_id)
        if not success:
            raise ValueError("渠道不存在")

        return {"message": "渠道状态修改成功"}


@channels_ns.route('/change_model_status')
class ModelStatusResource(Resource):
    @jwt_required()
    @channels_ns.expect(model_operation_model)
    @channels_ns.doc(security=['Login'])
    def post(self):
        """切换模型启用/禁用状态"""
        # 验证用户权限
        user = get_current_active_user()
        if not user.is_admin:
            raise PermissionDenied("只有管理员可以修改模型状态")

        data = request.get_json()
        channel_id = data['channel_id']
        model_name = data['model_name']

        success = ModelService.change_model_status(channel_id, model_name)
        if not success:
            raise ValueError("模型不存在")

        return {"message": "模型状态修改成功"}


@channels_ns.route('/add_model')
class ModelAddResource(Resource):
    @channels_ns.expect(model_create_model)
    @channels_ns.marshal_list_with(model_response_model, code=201)
    @jwt_required()
    @channels_ns.doc(security=['Login'])
    def post(self):
        """添加模型到渠道"""
        # 验证用户权限
        user = get_current_active_user()
        if not user.is_admin:
            raise PermissionDenied("只有管理员可以添加模型")

        data = request.get_json()
        channel_id = data['channel_id']
        model_names = data['models_name']

        models = ModelService.add_models(channel_id, model_names)
        return models, 201


@channels_ns.route('/delete_model')
class ModelDeleteResource(Resource):
    @jwt_required()
    @channels_ns.expect(model_operation_model)
    @channels_ns.doc(security=['Login'])
    def post(self):
        """删除模型"""
        # 验证用户权限
        user = get_current_active_user()
        if not user.is_admin:
            raise PermissionDenied("只有管理员可以删除模型")

        data = request.get_json()
        channel_id = data['channel_id']
        model_name = data['model_name']

        success = ModelService.delete_model(channel_id, model_name)
        if not success:
            raise ValueError("模型不存在")

        return {"message": "模型删除成功"}, 204


# 配置安全认证
authorizations = {
    'Login': {
        'type': 'oauth2',
        'flow': 'password',
        'tokenUrl': '/auth/login',
        'description': '使用用户名和密码登录获取JWT令牌'
    }
}

channels_ns.authorizations = authorizations