"""
智谱 AI API 渠道实现
"""
import json
import asyncio
from typing import List, Dict, Any, AsyncGenerator, Optional
from httpx import get, AsyncClient, ConnectTimeout, ReadTimeout
from .base import BaseChannel
from app.utils.errors import (
    ChannelConnectionError,
    ChannelAuthError,
    ChannelModelError,
    ChannelRateLimitError,
    ChannelTimeoutError
)


class ZhipuChannel(BaseChannel):
    """智谱 AI API 渠道实现"""
    
    def __init__(self, base_url: str = None, api_key: str = None, **kwargs):
        """
        初始化智谱渠道
        
        Args:
            base_url: 智谱 API基础URL
            api_key: 智谱 API密钥
            **kwargs: 其他参数
        """
        super().__init__(base_url, api_key, **kwargs)
        if base_url:
            self.base_url = base_url.rstrip('/')
        
    async def chat(
        self,
        model_name: str,
        message: str,
        history: List[Dict[str, str]] = None,
        stream: bool = True,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        使用智谱AI API发送聊天请求并获取响应

        Args:
            model_name: 模型名称
            message: 用户消息
            history: 历史消息列表
            stream: 是否使用流式响应
            **kwargs: 额外参数

        Returns:
            生成器，产生响应文本
        """
        # 构建消息列表
        messages = []
        if history:
            messages.extend(history)
        messages.append({"role": "user", "content": message})

        # 构建请求参数
        payload = {
            "model": model_name,
            "messages": messages,
            "stream": stream
        }
        
        # 添加额外参数，如temperature等
        for key, value in kwargs.items():
            if key not in payload and value is not None:
                payload[key] = value
        
        # 设置请求头
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 构建请求URL
        url = f"{self.base_url}/v4/chat/completions"

        # 发送请求
        async with AsyncClient() as client:
            try:
                if stream:
                    async with client.stream("POST", url, json=payload, headers=headers, timeout=60.0) as response:
                        if response.status_code != 200:
                            # 添加临时诊断日志
                            error_detail = ""
                            try:
                                error_detail = await response.text()
                                print(f"智谱API错误响应 ({response.status_code}): {error_detail}")
                            except:
                                print(f"无法获取错误详情，状态码: {response.status_code}")
                                
                            if response.status_code == 401:
                                raise ChannelAuthError("zhipu")
                            elif response.status_code == 404:
                                raise ChannelModelError("zhipu", f"{model_name} - 详细错误: {error_detail}")
                            elif response.status_code == 429:
                                raise ChannelRateLimitError("zhipu")
                            elif response.status_code == 500:
                                raise ChannelConnectionError("zhipu", f"服务器内部错误: {error_detail}")
                            else:
                                raise ChannelConnectionError("zhipu", f"API返回错误状态码: {response.status_code}, 详情: {error_detail}")

                        async for line in response.aiter_lines():
                            if not line.strip():
                                continue
                                
                            # 跳过可能的状态码响应
                            if line.startswith('{"status":') and 'OK' in line:
                                continue
                                
                            # 处理SSE格式
                            if line.startswith("data: "):
                                line = line[6:]
                                
                            # 跳过结束标记
                            if line == "[DONE]":
                                continue
                            
                            # 解析JSON响应
                            try:
                                data = json.loads(line)
                                if "choices" in data and len(data["choices"]) > 0:
                                    delta = data["choices"][0].get("delta", {})
                                    content = delta.get("content", "")
                                    
                                    if content:  # 只处理有内容的响应
                                        yield content
                            except json.JSONDecodeError:
                                continue
                            except Exception as e:
                                raise ChannelConnectionError("zhipu", f"处理响应出错: {str(e)}")
                else:
                    # 非流式处理
                    response = await client.post(url, json=payload, headers=headers, timeout=60.0)
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        if "choices" in data and len(data["choices"]) > 0:
                            content = data["choices"][0]["message"]["content"]
                            if content:  # 确保内容不为空
                                yield content
                        else:
                            raise ChannelConnectionError("zhipu", "响应格式异常")
                    else:
                        if response.status_code == 401:
                            raise ChannelAuthError("zhipu")
                        elif response.status_code == 404:
                            raise ChannelModelError("zhipu", model_name)
                        elif response.status_code == 429:
                            raise ChannelRateLimitError("zhipu")
                        else:
                            raise ChannelConnectionError("zhipu", f"API返回错误状态码: {response.status_code}")
            except (ConnectTimeout, ReadTimeout, asyncio.TimeoutError) as e:
                raise ChannelTimeoutError("zhipu")
            except (ChannelAuthError, ChannelModelError, ChannelRateLimitError, ChannelTimeoutError, ChannelConnectionError):
                raise
            except Exception as e:
                raise ChannelConnectionError("zhipu", str(e))

    async def get_available_models(self) -> List[Dict[str, Any]]:
        """
        获取智谱AI可用的模型列表

        Returns:
            可用模型信息列表
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 尝试不同的端点
            test_endpoints = ["/models", "/v4/models"]
            
            for endpoint in test_endpoints:
                try:
                    url = f"{self.base_url}{endpoint}"
                    response = get(url, headers=headers, timeout=10)
                    if response.status_code == 200:
                        data = response.json()
                        
                        # 处理不同的响应格式
                        models = []
                        model_data = []
                        
                        if "data" in data and isinstance(data["data"], list):
                            # 标准格式
                            model_data = data["data"]
                        elif "models" in data and isinstance(data["models"], list):
                            # 智谱AI特有格式
                            model_data = data["models"]
                        elif isinstance(data, list):
                            # 直接数组格式
                            model_data = data
                            
                        for model in model_data:
                            if isinstance(model, dict):
                                model_id = model.get("id", model.get("name", ""))
                                if model_id:
                                    models.append({
                                        "model_name": model_id,
                                        "model_id": model.get("id", model_id),
                                        "channel_id": 0,  # 这会在服务层被正确设置
                                        "channel_name": "智谱AI",
                                        "channel_type": "zhipu"
                                    })
                            elif isinstance(model, str) and model:
                                models.append({
                                    "model_name": model,
                                    "model_id": model,
                                    "channel_id": 0,
                                    "channel_name": "智谱AI",
                                    "channel_type": "zhipu"
                                })
                        
                        if models:
                            return models
                            
                except Exception:
                    continue
                    
        except Exception:
            pass
        
        # 如果API调用失败，返回已知的智谱AI模型列表
        default_models = [
            "glm-4",
            "glm-4-flash", 
            "glm-4-flash-250414",
            "glm-4-air",
            "glm-4-airx",
            "glm-4-plus",
            "glm-4v",
            "glm-4v-plus",
            "chatglm-6b",
            "chatglm2-6b",
            "chatglm3-6b"
        ]
        
        return [
            {
                "model_name": model,
                "model_id": model,
                "channel_id": 0,
                "channel_name": "智谱AI",
                "channel_type": "zhipu"
            }
            for model in default_models
        ]
        
    async def generate_title(self, model_name: str, message: str) -> str:
        """
        根据用户的第一条消息生成对话标题

        Args:
            model_name: 模型名称
            message: 用户的第一条消息

        Returns:
            生成的标题
        """
        prompt = f"""请根据以下用户消息生成一个简短、有意义的对话标题：
要求：
1. 不超过8个字
2. 不要使用任何标点符号
3. 只返回标题，不要其他任何内容

用户消息：
{message}"""

        try:
            full_response = ""
            async for chunk in self.chat(
                model_name=model_name,
                message=prompt,
                stream=False
            ):
                full_response += chunk

            if full_response:
                title = full_response.strip()
                title = ''.join(char for char in title if not char in '，。！？、；：""''（）【】《》')
                return title[:8]
        except Exception:
            pass

        # 简单地截取消息前几个字符作为标题
        if len(message) <= 8:
            return message
            
        # 尝试在句子边界截断
        for boundary in ['.', '。', '!', '！', '?', '？', '\n']:
            first_sentence = message.split(boundary)[0]
            if 3 <= len(first_sentence) <= 8:
                return first_sentence
                
        # 返回前8个字符
        return message[:8]
        
    async def check_connection(self) -> bool:
        """
        检查智谱AI API连接是否正常
        
        Returns:
            如果API连接正常返回True，否则返回False
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 智谱AI的模型列表端点
            test_endpoints = ["/models", "/v4/models"]
            
            for endpoint in test_endpoints:
                try:
                    url = f"{self.base_url}{endpoint}"
                    response = get(url, headers=headers, timeout=10)
                    if response.status_code == 200:
                        return True
                    elif response.status_code in [401, 403]:
                        # 认证错误，但端点正确
                        return False
                except Exception:
                    continue
                    
            return False
        except Exception:
            return False 