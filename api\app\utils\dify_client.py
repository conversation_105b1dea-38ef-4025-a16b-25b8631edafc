import requests
from typing import Dict, List, Optional, Any, Union
import logging
from app.core.config import get_config
import os

config = get_config()
logger = logging.getLogger(__name__)

class DifyClient:
    """
    Dify API客户端
    用于封装对Dify API的调用
    """
    
    def __init__(
        self, 
        base_url: str = None, 
        api_key: str = None, 
        workspace_id: str = None
    ):
        """
        初始化Dify客户端
        
        Args:
            base_url: Dify API基础URL
            api_key: Dify API密钥
            workspace_id: 工作区ID
        """
        self.base_url = base_url or config.DIFY_API_URL
        self.api_key = api_key or config.DIFY_API_KEY
        self.workspace_id = workspace_id or config.DIFY_WORKSPACE_ID
        
    def _get_headers(self) -> Dict[str, str]:
        """
        获取API请求头
        
        Returns:
            包含认证信息的请求头字典
        """
        return {
            "Authorization": f"Bearer {self.api_key}",
            "X-WORKSPACE-ID": self.workspace_id,
            "Content-Type": "application/json"
        }
        
    def get_apps(self, page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """
        获取应用列表(智能体列表)
        
        Args:
            page: 页码，从1开始
            limit: 每页记录数
            
        Returns:
            包含应用列表的字典
            
        Raises:
            Exception: 如果API调用失败
        """
        try:
            url = f"{self.base_url}/console/api/apps"
            params = {"page": page, "limit": limit}
            
            response = requests.get(
                url,
                params=params,
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取应用列表失败: {response.status_code} - {response.text}")
                response.raise_for_status()
                
        except requests.RequestException as e:
            logger.error(f"请求异常: {str(e)}")
            raise Exception(f"调用Dify API失败: {str(e)}")
            
        return {"data": [], "page": page, "limit": limit, "total": 0, "has_more": False}
        
    def get_app_detail(self, app_id: str) -> Dict[str, Any]:
        """
        获取应用详情
        
        Args:
            app_id: 应用ID
            
        Returns:
            应用详情字典
            
        Raises:
            Exception: 如果API调用失败
        """
        try:
            url = f"{self.base_url}/console/api/apps/{app_id}"
            
            response = requests.get(
                url,
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取应用详情失败: {response.status_code} - {response.text}")
                response.raise_for_status()
                
        except requests.RequestException as e:
            logger.error(f"请求异常: {str(e)}")
            raise Exception(f"调用Dify API失败: {str(e)}")
            
        return {}
        
    def get_app_credentials(self, app_id: str) -> Dict[str, Any]:
        """
        获取应用访问凭证
        
        Args:
            app_id: 应用ID
            
        Returns:
            访问凭证字典，包含api_key
            
        Raises:
            Exception: 如果API调用失败
        """
        try:
            # 修正API路径
            url = f"{self.base_url}/console/api/apps/{app_id}/api-keys"
            
            response = requests.get(
                url,
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                # 解析返回的API密钥列表
                result = response.json()
                api_keys = result.get('data', [])
                
                # 如果有API密钥，使用第一个
                if api_keys and len(api_keys) > 0:
                    # 返回修正后的凭证格式
                    return {
                        'api_key': api_keys[0].get('token'),
                        'api_key_id': api_keys[0].get('id'),
                        'created_at': api_keys[0].get('created_at')
                    }
                else:
                    logger.error(f"应用[{app_id}]没有可用的API密钥")
                    return {}
            else:
                logger.error(f"获取应用凭证失败: {response.status_code} - {response.text}")
                response.raise_for_status()
                
        except requests.RequestException as e:
            logger.error(f"请求异常: {str(e)}")
            raise Exception(f"调用Dify API失败: {str(e)}")
            
        return {}
        
    def get_tags(self, tag_type: str = "app") -> List[Dict[str, Any]]:
        """
        获取标签列表
        
        Args:
            tag_type: 标签类型，默认为"app"（智能体标签）
            
        Returns:
            标签列表
            
        Raises:
            Exception: 如果API调用失败
        """
        try:
            url = f"{self.base_url}/console/api/tags"
            params = {"type": tag_type}
            
            response = requests.get(
                url,
                params=params,
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取标签列表失败: {response.status_code} - {response.text}")
                response.raise_for_status()
                
        except requests.RequestException as e:
            logger.error(f"请求异常: {str(e)}")
            raise Exception(f"调用Dify API失败: {str(e)}")
            
        return []
        
    def get_conversation_list(
        self,
        agent_api_key: str,
        user_id: str,
        last_id: Optional[str] = None,
        limit: int = 20,
        sort_by: str = "-updated_at"
    ) -> Dict[str, Any]:
        """
        获取智能体对话历史列表
        
        Args:
            agent_api_key: 智能体API密钥
            user_id: 用户标识
            last_id: 上一页最后一条记录的ID，用于分页
            limit: 每页记录数，默认20条，最大100条
            sort_by: 排序方式，默认按更新时间倒序排序
            
        Returns:
            对话历史列表数据
            
        Raises:
            Exception: 如果API调用失败
        """
        try:
            # 构建API URL
            url = f"{self.base_url}/v1/conversations"
            
            # 构建请求参数
            params = {
                "user": user_id,
                "limit": limit
            }
            
            # 添加可选参数
            if last_id:
                params["last_id"] = last_id
                
            if sort_by:
                params["sort_by"] = sort_by
                
            # 设置请求头
            headers = {
                "Authorization": f"Bearer {agent_api_key}",
                "Content-Type": "application/json"
            }
            
            # 发送请求
            response = requests.get(
                url,
                params=params,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取对话列表失败: {response.status_code} - {response.text}")
                response.raise_for_status()
                
        except requests.RequestException as e:
            logger.error(f"请求异常: {str(e)}")
            raise Exception(f"获取对话历史列表失败: {str(e)}")
            
        return {"limit": limit, "has_more": False, "data": []}
        
    def chat_with_agent(
        self, 
        agent_api_key: str,
        query: str, 
        conversation_id: str = "", 
        user: str = None, 
        inputs: Dict = None,
        files: List[Dict] = None,
        response_mode: str = "streaming",
        stream: bool = True
    ) -> Union[Dict, requests.Response]:
        """
        与智能体对话
        
        Args:
            agent_api_key: 智能体API密钥
            query: 用户问题
            conversation_id: 对话ID，为空表示新对话
            user: 用户ID
            inputs: 额外输入参数
            files: 文件列表
            response_mode: 响应模式，streaming或blocking
            stream: 是否返回流式响应对象
            
        Returns:
            如果stream=True，返回requests.Response对象用于流式处理
            否则返回响应字典
            
        Raises:
            Exception: 如果API调用失败
        """
        try:
            # 获取应用凭据中的API基础URL
            # 注意：这里使用agent_detail中的URL而不是client配置的URL
            url = f"{self.base_url}/v1/chat-messages"
            
            # 构造请求数据
            payload = {
                "inputs": inputs or {},
                "query": query,
                "response_mode": response_mode,
                "conversation_id": conversation_id,
                "user": user,
                "files": files or []
            }
            
            # 设置请求头
            headers = {
                "Authorization": f"Bearer {agent_api_key}",
                "Content-Type": "application/json"
            }
            
            # 发送请求
            if stream and response_mode == "streaming":
                # 返回原始响应对象用于流式处理
                response = requests.post(
                    url,
                    json=payload,
                    headers=headers,
                    stream=True,
                    timeout=60
                )
                response.raise_for_status()
                return response
            else:
                # 返回解析后的JSON
                response = requests.post(
                    url,
                    json=payload,
                    headers=headers,
                    timeout=60
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    logger.error(f"与智能体对话失败: {response.status_code} - {response.text}")
                    response.raise_for_status()
                    
        except requests.RequestException as e:
            logger.error(f"请求异常: {str(e)}")
            raise Exception(f"调用智能体对话API失败: {str(e)}")
            
        return {}
        
    def upload_file(
        self,
        agent_api_key: str,
        file_path: str,
        user_id: str,
        mime_type: str = None
    ) -> Dict[str, Any]:
        """
        上传文件到Dify
        
        Args:
            agent_api_key: 智能体API密钥
            file_path: 文件路径
            user_id: 用户ID
            mime_type: 文件MIME类型，如果不提供会根据文件扩展名猜测
            
        Returns:
            上传成功后的文件信息
            
        Raises:
            Exception: 如果API调用失败
        """
        try:
            # 构建API URL
            url = f"{self.base_url}/v1/files/upload"
            
            # 设置请求头
            headers = {
                "Authorization": f"Bearer {agent_api_key}"
            }
            
            # 如果没有提供mime_type，尝试根据文件扩展名猜测
            if not mime_type:
                import mimetypes
                mime_type, _ = mimetypes.guess_type(file_path)
                
            # 如果仍然无法确定mime_type，使用默认值
            if not mime_type:
                mime_type = "application/octet-stream"
                
            # 使用上下文管理器确保文件句柄正确关闭
            with open(file_path, 'rb') as f:
                # 准备文件和表单数据
                files = {
                    'file': (
                        os.path.basename(file_path),
                        f,
                        mime_type
                    )
                }

                data = {
                    'user': user_id
                }

                # 发送请求
                response = requests.post(
                    url,
                    headers=headers,
                    files=files,
                    data=data,
                    timeout=60
                )
            
            # 接受200和201状态码作为成功响应
            if response.status_code in [200, 201]:
                return response.json()
            else:
                logger.error(f"上传文件失败: {response.status_code} - {response.text}")
                response.raise_for_status()
                
        except requests.RequestException as e:
            logger.error(f"请求异常: {str(e)}")
            raise Exception(f"上传文件失败: {str(e)}")
        except Exception as e:
            logger.error(f"上传文件过程中发生错误: {str(e)}")
            raise Exception(f"上传文件失败: {str(e)}")
            
        return {}
        
    def get_app_parameters(self, app_api_key: str) -> Dict[str, Any]:
        """
        获取应用参数信息
        
        Args:
            app_api_key: 应用API密钥
            
        Returns:
            应用参数信息，包含opening_statement、suggested_questions、user_input_form等
            
        Raises:
            Exception: 如果API调用失败
        """
        try:
            # 构建API URL
            url = f"{self.base_url}/v1/parameters"
            
            # 设置请求头
            headers = {
                "Authorization": f"Bearer {app_api_key}",
                "Content-Type": "application/json"
            }
            
            # 发送请求
            response = requests.get(
                url,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取应用参数信息失败: {response.status_code} - {response.text}")
                response.raise_for_status()
                
        except requests.RequestException as e:
            logger.error(f"请求异常: {str(e)}")
            raise Exception(f"调用Dify API失败: {str(e)}")
            
        return {} 