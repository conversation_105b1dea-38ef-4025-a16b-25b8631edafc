"""
聊天API路由和入口点
包含消息处理、对话历史、模型列表等API
"""

from flask import request, jsonify, make_response, Response
from flask_restx import Namespace, Resource
from sqlalchemy.orm import Session
from typing import List, Dict, Any
import json

from app.core.auth_decorators import jwt_required, get_jwt_identity

from app import db
from app.utils.dependencies import get_current_active_user
from app.utils.errors import ResourceNotFound
from app.services.chat_service import ChatService
from app.services.model_router import ModelRouter
from app.utils.chat.streaming import generate_stream_response, create_streaming_response
from app.models.chat import Conversation, ChatMessage
from app.models.channel import ChannelList, ModelList

from app.schemas.chat.request import message_create_model, get_history_request_model, get_models_request_model
from app.schemas.chat.response import (
    message_response_model, 
    history_response_model, 
    conversation_response_model,
    model_list_response_model,
    conversation_files_response_model,
    chat_message_response_model,
    model_info_response_model,
    file_brief_response_model
)

# 创建命名空间
chat_ns = Namespace('chat', description='聊天API')

# 将模型添加到当前命名空间
chat_ns.models[message_create_model.name] = message_create_model
chat_ns.models[get_history_request_model.name] = get_history_request_model
chat_ns.models[get_models_request_model.name] = get_models_request_model
chat_ns.models[message_response_model.name] = message_response_model
chat_ns.models[history_response_model.name] = history_response_model
chat_ns.models[conversation_response_model.name] = conversation_response_model
chat_ns.models[model_list_response_model.name] = model_list_response_model
chat_ns.models[conversation_files_response_model.name] = conversation_files_response_model
chat_ns.models[chat_message_response_model.name] = chat_message_response_model
chat_ns.models[model_info_response_model.name] = model_info_response_model
chat_ns.models[file_brief_response_model.name] = file_brief_response_model


@chat_ns.route('/message')
class Message(Resource):
    @jwt_required()
    @chat_ns.expect(message_create_model)
    def post(self):
        """创建聊天消息并获取AI响应"""
        try:
            data = request.get_json()
            
            message_data = {
                'query': data.get('query'),
                'response_mode': data.get('response_mode', 'streaming'),
                'conversation_id': data.get('conversation_id'),
                'model_name': data.get('model_name'),
                'enable_thinking': data.get('enable_thinking', False),
                'file_ids': data.get('file_ids')
            }
            
            # 获取当前用户
            current_user = get_current_active_user()
            
            if message_data['response_mode'] == 'streaming':
                # 创建独立的数据库会话，不受主请求生命周期影响
                stream_db = Session(db.engine)

                try:
                    # 初始化服务
                    chat_service = ChatService(stream_db)

                    # 定义直接生成器函数
                    def generate():
                        try:
                            # 创建异步生成器包装器
                            import asyncio

                            # 异步函数转同步生成器的转换函数
                            async def async_to_sync():
                                try:
                                    # 调用ChatService的process_message方法
                                    async_generator = chat_service.process_message(
                                        user_id=current_user.user_id,
                                        model_name=message_data['model_name'],
                                        message=message_data['query'],
                                        conversation_id=message_data['conversation_id'],
                                        stream=True,
                                        file_ids=message_data['file_ids'],
                                        enable_thinking=message_data['enable_thinking']
                                    )

                                    # 消费异步生成器并转发响应
                                    async for chunk in async_generator:
                                        # 处理错误
                                        if 'error' in chunk:
                                            yield f"data: {json.dumps(chunk)}\n\n"
                                            if chunk.get('status') == 'error':
                                                yield "data: [DONE]\n\n"
                                                return
                                        # 处理常规响应
                                        else:
                                            yield f"data: {json.dumps(chunk)}\n\n"

                                    # 发送完成消息
                                    yield "data: [DONE]\n\n"
                                except Exception as e:
                                    yield f"data: {json.dumps({'error': str(e), 'status': 'error'})}\n\n"
                                    yield "data: [DONE]\n\n"

                            # 导入异步到同步转换工具
                            import queue
                            import threading

                            # 创建队列用于线程间通信
                            response_queue = queue.Queue()

                            # 线程函数：运行异步函数并将结果放入队列
                            def run_async():
                                loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(loop)

                                try:
                                    # 创建协程
                                    async def process():
                                        async for result in async_to_sync():
                                            response_queue.put(result)
                                        # 放入None表示结束
                                        response_queue.put(None)

                                    # 运行协程
                                    loop.run_until_complete(process())
                                except Exception as e:
                                    response_queue.put(f"data: {json.dumps({'error': str(e), 'status': 'error'})}\n\n")
                                    response_queue.put("data: [DONE]\n\n")
                                    response_queue.put(None)
                                finally:
                                    loop.close()

                            # 启动异步处理线程
                            thread = threading.Thread(target=run_async)
                            thread.daemon = True
                            thread.start()

                            # 从队列中获取结果并返回给客户端
                            while True:
                                try:
                                    chunk = response_queue.get(timeout=0.1)

                                    # None表示结束
                                    if chunk is None:
                                        break

                                    # 返回块
                                    yield chunk
                                except queue.Empty:
                                    # 队列为空但线程还活着，继续等待
                                    if thread.is_alive():
                                        continue
                                    # 线程已死且队列为空，退出循环
                                    break

                            # 等待线程完成
                            thread.join(timeout=1.0)

                        except Exception as e:
                            yield f"data: {json.dumps({'error': str(e), 'status': 'error'})}\n\n"
                            yield "data: [DONE]\n\n"

                        finally:
                            # 关闭数据库会话
                            stream_db.close()

                    # 返回流式响应
                    return Response(generate(), mimetype='text/event-stream')
                except Exception as e:
                    # 如果在创建流式响应过程中发生异常，确保关闭数据库会话
                    stream_db.close()
                    raise e
            else:
                # 非流式响应的处理
                # 使用主数据库会话
                chat_service = ChatService(db)
                
                # 创建响应收集器
                async def collect_response():
                    full_response = ""
                    conversation_id = None
                    title = None
                    error = None
                    
                    try:
                        # 调用ChatService的process_message方法
                        async for chunk in chat_service.process_message(
                            user_id=current_user.user_id,
                            model_name=message_data['model_name'],
                            message=message_data['query'],
                            conversation_id=message_data['conversation_id'],
                            stream=False,
                            file_ids=message_data['file_ids'],
                            enable_thinking=message_data['enable_thinking']
                        ):
                            # 处理错误
                            if 'error' in chunk:
                                error = chunk['error']
                                if 'conversation_id' in chunk:
                                    conversation_id = chunk['conversation_id']
                                if 'title' in chunk:
                                    title = chunk['title']
                                break
                            
                            # 处理正常响应
                            if 'content' in chunk:
                                full_response += chunk['content']
                            if 'conversation_id' in chunk and not conversation_id:
                                conversation_id = chunk['conversation_id']
                            if 'title' in chunk and not title:
                                title = chunk['title']
                    
                    except Exception as e:
                        error = str(e)
                    
                    # 返回收集的结果
                    if error:
                        return {'error': error, 'conversation_id': conversation_id, 'title': title}
                    else:
                        return {'content': full_response, 'conversation_id': conversation_id, 'title': title}
                
                # 使用事件循环运行异步函数
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    result = loop.run_until_complete(collect_response())
                finally:
                    loop.close()
                
                # 返回JSON响应
                if 'error' in result:
                    return {'error': result['error'], 'conversation_id': result.get('conversation_id'), 'title': result.get('title')}, 400
                else:
                    return {'content': result['content'], 'conversation_id': result['conversation_id'], 'title': result.get('title')}
                
        except Exception as e:
            return {'error': str(e)}, 500
    
    async def _collect_async_generator(self, generator):
        """收集异步生成器的结果"""
        results = []
        async for item in generator:
            results.append(item)
        return results


@chat_ns.route('/history')
class History(Resource):
    @jwt_required()
    @chat_ns.expect(get_history_request_model)
    @chat_ns.marshal_with(history_response_model)
    def post(self):
        """获取指定对话的历史消息"""
        try:
            data = request.get_json()
            conversation_id = data.get('conversation_id')
            
            # 获取当前用户
            get_current_active_user()
            
            # 获取对话历史
            messages = db.session.query(ChatMessage).filter(
                ChatMessage.conversation_id == conversation_id
            ).order_by(ChatMessage.created_at.asc()).all()
            
            return {'messages': messages}
            
        except Exception as e:
            chat_ns.abort(500, f"服务器内部错误: {str(e)}")


@chat_ns.route('/conversations')
class Conversations(Resource):
    @jwt_required()
    @chat_ns.marshal_with(conversation_response_model, as_list=True)
    def get(self):
        """获取当前用户的所有对话"""
        try:
            # 获取当前用户
            current_user = get_current_active_user()
            
            # 获取用户的所有对话
            conversations = db.session.query(Conversation).filter(
                Conversation.user_id == current_user.user_id
            ).all()
            
            return conversations
            
        except Exception as e:
            chat_ns.abort(500, f"服务器内部错误: {str(e)}")


@chat_ns.route('/conversations/delete')
class DeleteConversation(Resource):
    @jwt_required()
    @chat_ns.marshal_with(conversation_response_model)
    def post(self):
        """删除指定对话"""
        try:
            data = request.get_json()
            conversation_id = data.get('conversation_id')
            
            if not conversation_id:
                chat_ns.abort(400, "缺少对话ID")
            
            # 获取当前用户
            get_current_active_user()
            
            # 获取对话
            conversation = db.session.query(Conversation).filter(
                Conversation.conversation_id == conversation_id
            ).first()
            
            if not conversation:
                chat_ns.abort(404, "对话不存在")
                
            # 删除关联的消息
            db.session.query(ChatMessage).filter(
                ChatMessage.conversation_id == conversation_id
            ).delete()
            
            # 删除对话
            db.session.delete(conversation)
            db.session.commit()
            
            # 返回删除的对话ID
            return {
                'conversation_id': conversation_id,
                'title': "",  # 已删除，不需要返回标题
                'created_at': None,
                'updated_at': None
            }
            
        except Exception as e:
            chat_ns.abort(500, f"服务器内部错误: {str(e)}")


@chat_ns.route('/models')
class Models(Resource):
    @jwt_required()
    @chat_ns.expect(get_models_request_model)
    @chat_ns.marshal_with(model_list_response_model)
    def post(self):
        """获取所有可用的模型列表"""
        try:
            # 获取当前用户
            get_current_active_user()
            
            # 获取可用模型
            model_router = ModelRouter(db.session)
            models = model_router.get_all_available_models()
            
            return {'models': models}
            
        except Exception as e:
            chat_ns.abort(500, f"服务器内部错误: {str(e)}")


@chat_ns.route('/conversation/<string:conversation_id>/files')
class ConversationFiles(Resource):
    @jwt_required()
    @chat_ns.marshal_with(conversation_files_response_model)
    def get(self, conversation_id):
        """获取对话关联的文件列表"""
        try:
            # 获取当前用户
            current_user = get_current_active_user()
            
            # 获取对话
            conversation = db.session.query(Conversation).filter(
                Conversation.conversation_id == conversation_id
            ).first()
            
            if not conversation:
                chat_ns.abort(404, "对话不存在")
            
            # 验证对话所有者
            if conversation.user_id != current_user.user_id:
                chat_ns.abort(403, "没有权限访问该对话")
            
            # 获取关联的文件
            files = conversation.files
            
            return {
                'conversation_id': conversation_id,
                'total': len(files),
                'files': files
            }
            
        except Exception as e:
            chat_ns.abort(500, f"服务器内部错误: {str(e)}")
