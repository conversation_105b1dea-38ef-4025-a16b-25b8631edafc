"""文件相关的响应模型"""
from flask_restx import fields
from datetime import datetime

def file_response_model(api):
    """文件详情响应模型"""
    return api.model('FileResponse', {
        'file_id': fields.String(description='文件ID'),
        'filename': fields.String(description='文件名'),
        'file_type': fields.String(description='文件类型'),
        'file_size': fields.Integer(description='文件大小(字节)'),
        'upload_time': fields.DateTime(description='上传时间'),
        'status': fields.String(description='处理状态', enum=['pending', 'processing', 'processed', 'failed']),
        'description': fields.String(description='文件描述'),
    })


def file_list_response_model(api):
    """文件列表响应模型"""
    return api.model('FileListResponse', {
        'total': fields.Integer(description='文件总数'),
        'skip': fields.Integer(description='跳过数量'),
        'limit': fields.Integer(description='限制数量'),
        'files': fields.List(fields.Nested(file_response_model(api)), description='文件列表')
    })


def file_upload_response_model(api):
    """文件上传响应模型"""
    return api.model('FileUploadResponse', {
        'success': fields.Boolean(description='是否成功'),
        'message': fields.String(description='消息'),
        'file': fields.Nested(file_response_model(api), description='文件信息')
    })


def file_content_response_model(api):
    """文件内容响应模型"""
    return api.model('FileContentResponse', {
        'file_id': fields.String(description='文件ID'),
        'filename': fields.String(description='文件名'),
        'content': fields.String(description='文件内容'),
        'file_size': fields.Integer(description='内容大小(字符数)'),
        'upload_time': fields.DateTime(description='上传时间')
    })


def file_process_response_model(api):
    """文件处理响应模型"""
    return api.model('FileProcessResponse', {
        'success': fields.Boolean(description='是否成功'),
        'message': fields.String(description='消息'),
        'file': fields.Nested(file_response_model(api), description='文件信息')
    }) 