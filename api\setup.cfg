[flake8]
max-line-length = 100
exclude = .git,__pycache__,docs/source/conf.py,old,build,dist,.venv,venv
ignore = E203, E266, E501, W503

[mypy]
python_version = 3.10
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = False
disallow_incomplete_defs = False
check_untyped_defs = True
disallow_untyped_decorators = False
no_implicit_optional = True
strict_optional = True

[mypy.plugins.sqlalchemy.ext.*]
ignore_missing_imports = True

[tool:pytest]
testpaths = tests
python_files = test_*.py
python_functions = test_*
python_classes = Test* 