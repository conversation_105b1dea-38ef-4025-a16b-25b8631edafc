"""
管理员接口数据模型
定义管理员相关API的请求和响应模型
"""

from flask_restx import fields, Namespace

# 创建命名空间用于模型定义
admin_ns = Namespace('admin', description='管理员接口')

# 系统监控相关模型
cpu_info_model = admin_ns.model('CPUInfo', {
    'usage': fields.Float(description='CPU使用率(%)', example=45.2),
    'cores': fields.Integer(description='CPU核心数', example=8),
    'frequency': fields.Integer(description='CPU频率(MHz)', example=3200)
})

memory_info_model = admin_ns.model('MemoryInfo', {
    'usage': fields.Float(description='内存使用率(%)', example=68.5),
    'used_gb': fields.Float(description='已用内存(GB)', example=11.2),
    'total_gb': fields.Float(description='总内存(GB)', example=16.0)
})

disk_info_model = admin_ns.model('DiskInfo', {
    'usage': fields.Float(description='磁盘使用率(%)', example=78.3),
    'used_gb': fields.Float(description='已用磁盘(GB)', example=235.6),
    'total_gb': fields.Float(description='总磁盘(GB)', example=300.0)
})

network_info_model = admin_ns.model('NetworkInfo', {
    'speed': fields.String(description='网络速度', example='57MB/s'),
    'upload_speed': fields.String(description='上传速度', example='12MB/s'),
    'download_speed': fields.String(description='下载速度', example='45MB/s')
})

system_monitor_model = admin_ns.model('SystemMonitor', {
    'cpu': fields.Nested(cpu_info_model, description='CPU信息'),
    'memory': fields.Nested(memory_info_model, description='内存信息'),
    'disk': fields.Nested(disk_info_model, description='磁盘信息'),
    'network': fields.Nested(network_info_model, description='网络信息')
})

# 系统健康状态模型
system_services_model = admin_ns.model('SystemServices', {
    'database': fields.String(description='数据库状态', example='healthy'),
    'redis': fields.String(description='Redis状态', example='healthy'),
    'external_auth': fields.String(description='外部认证状态', example='healthy')
})

system_health_model = admin_ns.model('SystemHealth', {
    'status': fields.String(description='系统状态', example='normal', enum=['normal', 'warning', 'error']),
    'last_update': fields.String(description='最后更新时间', example='2025-01-23T10:30:00Z'),
    'services': fields.Nested(system_services_model, description='服务状态')
})

# 统计数据相关模型
user_stats_model = admin_ns.model('UserStats', {
    'total': fields.Integer(description='总用户数', example=1234),
    'growth_rate': fields.Float(description='增长率(%)', example=12.5),
    'online_count': fields.Integer(description='在线用户数', example=128)
})

agent_stats_model = admin_ns.model('AgentStats', {
    'active_count': fields.Integer(description='活跃智能体数', example=56),
    'growth_rate': fields.Float(description='增长率(%)', example=8.3)
})

conversation_stats_model = admin_ns.model('ConversationStats', {
    'today_count': fields.Integer(description='今日对话数', example=3456),
    'growth_rate': fields.Float(description='增长率(%)', example=23.1)
})

file_stats_model = admin_ns.model('FileStats', {
    'total_count': fields.Integer(description='总文件数', example=890),
    'total_size_gb': fields.Float(description='总文件大小(GB)', example=12.5)
})

dashboard_overview_model = admin_ns.model('DashboardOverview', {
    'users': fields.Nested(user_stats_model, description='用户统计'),
    'agents': fields.Nested(agent_stats_model, description='智能体统计'),
    'conversations': fields.Nested(conversation_stats_model, description='对话统计'),
    'files': fields.Nested(file_stats_model, description='文件统计')
})

# 图表数据相关模型
agent_usage_item_model = admin_ns.model('AgentUsageItem', {
    'name': fields.String(description='智能体名称', example='智能助手'),
    'value': fields.Integer(description='使用次数', example=5420),
    'color': fields.String(description='颜色', example='#409EFF')
})

agent_usage_model = admin_ns.model('AgentUsage', {
    'total': fields.Integer(description='总使用次数', example=15420),
    'distribution': fields.List(fields.Nested(agent_usage_item_model), description='使用分布')
})

conversation_activity_item_model = admin_ns.model('ConversationActivityItem', {
    'label': fields.String(description='时间标签', example='00:00'),
    'value': fields.Integer(description='对话数量', example=120)
})

conversation_activity_model = admin_ns.model('ConversationActivity', {
    'data': fields.List(fields.Nested(conversation_activity_item_model), description='活跃度数据'),
    'peak_hour': fields.String(description='峰值时段', example='12:00'),
    'avg_conversations': fields.Integer(description='平均对话数', example=280)
})

charts_data_model = admin_ns.model('ChartsData', {
    'agent_usage': fields.Nested(agent_usage_model, description='智能体使用分布'),
    'conversation_activity': fields.Nested(conversation_activity_model, description='对话活跃度')
})

# 通用响应模型
success_response_model = admin_ns.model('SuccessResponse', {
    'code': fields.Integer(description='状态码', example=200),
    'message': fields.String(description='响应消息', example='success'),
    'data': fields.Raw(description='响应数据'),
    'timestamp': fields.String(description='响应时间戳', example='2025-01-23T10:30:00Z')
})

error_response_model = admin_ns.model('ErrorResponse', {
    'error': fields.String(description='错误类型', example='permission_denied'),
    'error_description': fields.String(description='错误描述', example='需要管理员权限'),
    'message': fields.String(description='错误消息', example='您没有权限访问此资源')
})

# 导出所有模型，方便在路由文件中使用
__all__ = [
    'admin_ns',
    'system_monitor_model',
    'system_health_model',
    'dashboard_overview_model',
    'charts_data_model',
    'success_response_model',
    'error_response_model'
]
