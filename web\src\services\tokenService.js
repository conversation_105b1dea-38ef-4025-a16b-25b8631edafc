/**
 * 令牌服务模块 - 统一状态管理，解决双重存储矛盾
 * 基于矛盾论：建立Pinia与localStorage的统一状态机制
 */
import { jwtDecode } from 'jwt-decode';

// 存储键名
const ACCESS_TOKEN_KEY = 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const USERNAME_KEY = 'username';

/**
 * 统一存储管理器 - 解决双重存储矛盾的核心
 */
class UnifiedStorage {
  constructor() {
    this.cache = new Map();
    this.listeners = new Map();
  }

  set(key, value) {
    // 更新缓存
    this.cache.set(key, value);
    // 更新持久化存储
    localStorage.setItem(key, value);
    // 通知监听器
    this.notify(key, value);
  }

  get(key) {
    // 优先从缓存获取
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }
    
    // 从持久化存储恢复
    const value = localStorage.getItem(key);
    if (value !== null) {
      this.cache.set(key, value);
    }
    return value;
  }

  remove(key) {
    this.cache.delete(key);
    localStorage.removeItem(key);
    this.notify(key, null);
  }

  subscribe(key, callback) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set());
    }
    this.listeners.get(key).add(callback);
    
    return () => this.listeners.get(key).delete(callback);
  }

  notify(key, value) {
    if (this.listeners.has(key)) {
      this.listeners.get(key).forEach(callback => callback(value));
    }
  }

  clear() {
    this.cache.clear();
    localStorage.clear();
  }
}

// 创建单例实例
const unifiedStorage = new UnifiedStorage();

/**
 * 统一令牌管理 - 解决状态管理双重性的核心方法
 */
export const setTokens = (accessToken, refreshToken) => {
  unifiedStorage.set(ACCESS_TOKEN_KEY, accessToken);
  unifiedStorage.set(REFRESH_TOKEN_KEY, refreshToken);
};

export const getAccessToken = () => {
  return unifiedStorage.get(ACCESS_TOKEN_KEY);
};

export const getRefreshToken = () => {
  return unifiedStorage.get(REFRESH_TOKEN_KEY);
};

export const clearTokens = () => {
  unifiedStorage.remove(ACCESS_TOKEN_KEY);
  unifiedStorage.remove(REFRESH_TOKEN_KEY);
  unifiedStorage.remove(USERNAME_KEY);
};

export const setUsername = (username) => {
  unifiedStorage.set(USERNAME_KEY, username);
};

export const getUsername = () => {
  return unifiedStorage.get(USERNAME_KEY);
};

/**
 * 订阅机制 - 实现状态同步
 */
export const subscribeToTokenChanges = (callback) => {
  const unsubAccess = unifiedStorage.subscribe(ACCESS_TOKEN_KEY, callback);
  const unsubRefresh = unifiedStorage.subscribe(REFRESH_TOKEN_KEY, callback);
  
  return () => {
    unsubAccess();
    unsubRefresh();
  };
};

/**
 * 检查令牌是否过期
 * @param {string} token - JWT令牌
 * @returns {boolean} 如果过期或无效则返回true
 */
export const isTokenExpired = (token) => {
  if (!token) return true;
  
  try {
    const decoded = jwtDecode(token);
    // 将JWT的exp时间戳（秒）转换为JavaScript时间戳（毫秒）
    return Date.now() >= decoded.exp * 1000;
  } catch (error) {
    console.error('令牌解析失败', error);
    return true; // 解析失败视为过期
  }
};

/**
 * 检查令牌是否即将过期（默认5分钟内视为即将过期）
 * @param {string} token - JWT令牌
 * @param {number} thresholdMinutes - 阈值（分钟）
 * @returns {boolean} 如果即将过期则返回true
 */
export const isTokenNearExpiry = (token, thresholdMinutes = 5) => {
  if (!token) return true;
  
  try {
    const decoded = jwtDecode(token);
    const thresholdMs = thresholdMinutes * 60 * 1000; // 转换为毫秒
    // 检查令牌是否在5分钟内过期
    return Date.now() + thresholdMs >= decoded.exp * 1000;
  } catch (error) {
    console.error('令牌解析失败', error);
    return true; // 解析失败视为即将过期
  }
};

/**
 * 获取令牌有效期剩余时间（毫秒）
 * @param {string} token - JWT令牌
 * @returns {number} 剩余有效期（毫秒），已过期或无效则返回0
 */
export const getTokenRemainingTime = (token) => {
  if (!token) return 0;
  
  try {
    const decoded = jwtDecode(token);
    const expiryTime = decoded.exp * 1000; // 转换为毫秒
    const remainingTime = expiryTime - Date.now();
    return remainingTime > 0 ? remainingTime : 0;
  } catch (error) {
    console.error('令牌解析失败', error);
    return 0; // 解析失败视为已过期
  }
}; 