<template>
  <div class="admin-dashboard">
    <!-- 页面头部 -->
    <div class="page-header-wrapper">
      <div class="container">
        <div class="page-header">
          <div class="header-content">
            <div class="header-left">
              <h1 class="page-title">仪表盘</h1>
              <p class="page-subtitle">欢迎回来，查看您的系统概览</p>
            </div>
            <div class="header-right">
              <div class="header-stats">
                <div class="quick-stat">
                  <span class="quick-stat-label">总用户数</span>
                  <span class="quick-stat-value">{{ onlineUsers }}</span>
                </div>
                <div class="quick-stat">
                  <span class="quick-stat-label">系统状态</span>
                  <div class="system-status-display">
                    <div class="status-indicator" :class="systemStatusClass"></div>
                    <span class="status-text" :class="systemStatusClass">{{ systemStatus }}</span>
                  </div>
                </div>
                <div class="quick-stat">
                  <span class="quick-stat-label">最后更新</span>
                  <span class="quick-stat-value">{{ lastUpdateTime }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 仪表盘概览 -->
    <div class="dashboard-overview">
      <div class="container">
        <!-- 系统监控区域 -->
        <div class="system-monitor-section">
          <h2 class="section-title">
            <el-icon><Monitor /></el-icon>
            系统监控
          </h2>
          <div class="compact-monitor-grid">
            <!-- CPU监控 -->
            <div class="compact-monitor-item">
              <div class="monitor-header">
                <span class="monitor-label">CPU</span>
                <div class="status-dot" :class="cpuStatusClass"></div>
              </div>
              <div class="monitor-main">
                <div class="monitor-value-large">{{ cpuUsage }}%</div>
                <div class="mini-progress">
                  <div class="progress-fill" :style="{ width: cpuUsage + '%', backgroundColor: cpuColor }"></div>
                </div>
              </div>
              <div class="monitor-details">
                <span class="detail-item">核心: 8</span>
                <span class="detail-item">频率: 3.2GHz</span>
              </div>
            </div>

            <!-- 内存监控 -->
            <div class="compact-monitor-item">
              <div class="monitor-header">
                <span class="monitor-label">内存</span>
                <div class="status-dot" :class="memoryStatusClass"></div>
              </div>
              <div class="monitor-main">
                <div class="monitor-value-large">{{ memoryUsage }}%</div>
                <div class="mini-progress">
                  <div class="progress-fill memory-fill" :style="{ width: memoryUsage + '%' }"></div>
                </div>
              </div>
              <div class="monitor-details">
                <span class="detail-item">已用: {{ memoryUsed }}GB</span>
                <span class="detail-item">总计: {{ memoryTotal }}GB</span>
              </div>
            </div>

            <!-- 磁盘监控 -->
            <div class="compact-monitor-item">
              <div class="monitor-header">
                <span class="monitor-label">磁盘</span>
                <div class="status-dot" :class="diskStatusClass"></div>
              </div>
              <div class="monitor-main">
                <div class="monitor-value-large">{{ diskUsage }}%</div>
                <div class="mini-progress">
                  <div class="progress-fill disk-fill" :style="{ width: diskUsage + '%' }"></div>
                </div>
              </div>
              <div class="monitor-details">
                <span class="detail-item">已用: {{ diskUsed }}GB</span>
                <span class="detail-item">总计: {{ diskTotal }}GB</span>
              </div>
            </div>

            <!-- 网络监控已移除，因为在Windows上可能不准确 -->
          </div>
        </div>

        <!-- 业务统计区域 -->
        <div class="business-stats-section">
          <h2 class="section-title">
            <el-icon><Operation /></el-icon>
            业务统计
          </h2>
          <div class="compact-monitor-grid">
            <!-- 总用户数 -->
            <div class="business-stat-card">
              <div class="stat-card-header">
                <div class="stat-icon-wrapper">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" fill="#409EFF"/>
                  </svg>
                </div>
                <span class="stat-card-title">总用户数</span>
              </div>
              <div class="stat-card-content">
                <div class="stat-main-value">{{ formatNumber(totalUsers) }}</div>
                <div class="stat-change-info">
                  <span class="change-value">+{{ userGrowth }}%</span>
                  <span class="change-period">较上周</span>
                </div>
              </div>
            </div>

            <!-- 总对话数 -->
            <div class="business-stat-card">
              <div class="stat-card-header">
                <div class="stat-icon-wrapper">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="#67C23A" stroke-width="2" fill="none"/>
                  </svg>
                </div>
                <span class="stat-card-title">总对话数</span>
              </div>
              <div class="stat-card-content">
                <div class="stat-main-value">{{ activeAgents }}</div>
                <div class="stat-change-info">
                  <span class="change-value">+{{ agentGrowth }}%</span>
                  <span class="change-period">较昨日</span>
                </div>
              </div>
            </div>

            <!-- 今日对话 -->
            <div class="business-stat-card">
              <div class="stat-card-header">
                <div class="stat-icon-wrapper">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" stroke="#E6A23C" stroke-width="2" fill="none"/>
                  </svg>
                </div>
                <span class="stat-card-title">今日对话</span>
              </div>
              <div class="stat-card-content">
                <div class="stat-main-value">{{ formatNumber(todayConversations) }}</div>
                <div class="stat-change-info">
                  <span class="change-value">+{{ conversationGrowth }}%</span>
                  <span class="change-period">较昨日</span>
                </div>
              </div>
            </div>

            <!-- 文件总数 -->
            <div class="business-stat-card">
              <div class="stat-card-header">
                <div class="stat-icon-wrapper">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" stroke="#909399" stroke-width="2" fill="none"/>
                  </svg>
                </div>
                <span class="stat-card-title">文件总数</span>
              </div>
              <div class="stat-card-content">
                <div class="stat-main-value">{{ formatNumber(totalFiles) }}</div>
                <div class="stat-change-info">
                  <span class="change-value">{{ fileSize }}GB</span>
                  <span class="change-period">存储</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据分析板块已移除 -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Monitor, Operation
} from '@element-plus/icons-vue'
import { useAdminData } from '@/services/adminService'
import { ElMessage } from 'element-plus'

// 使用管理员数据服务
const {
  getSystemMonitor,
  getDashboardOverview
} = useAdminData()

// 响应式数据

// 系统监控数据
const cpuUsage = ref(0)
const memoryUsage = ref(0)
const memoryUsed = ref(0)
const memoryTotal = ref(0)
const diskUsage = ref(0)
const diskUsed = ref(0)
const diskTotal = ref(0)

// 业务统计数据
const totalUsers = ref(0)
const userGrowth = ref(0)
const activeAgents = ref(0)
const agentGrowth = ref(0)
const todayConversations = ref(0)
const conversationGrowth = ref(0)
const totalFiles = ref(0)
const fileSize = ref(0)

// 头部统计数据
const onlineUsers = ref(0)
const systemStatus = ref('正常')
const lastUpdateTime = ref('刚刚')

// 图表数据已移除

// 计算属性
const systemStatusClass = computed(() => {
  if (systemStatus.value === '正常') return 'status-good'
  if (systemStatus.value === '警告') return 'status-warning'
  if (systemStatus.value === '异常') return 'status-danger'
  return 'status-good'
})

const cpuStatusClass = computed(() => {
  if (cpuUsage.value < 50) return 'status-good'
  if (cpuUsage.value < 80) return 'status-warning'
  return 'status-danger'
})

const memoryStatusClass = computed(() => {
  if (memoryUsage.value < 60) return 'status-good'
  if (memoryUsage.value < 85) return 'status-warning'
  return 'status-danger'
})

const diskStatusClass = computed(() => {
  if (diskUsage.value < 70) return 'status-good'
  if (diskUsage.value < 90) return 'status-warning'
  return 'status-danger'
})

const cpuColor = computed(() => {
  if (cpuUsage.value < 50) return '#909399'
  if (cpuUsage.value < 80) return '#E6A23C'
  return '#F56C6C'
})

// 自动计算系统状态
const updateSystemStatus = () => {
  const avgLoad = (cpuUsage.value + memoryUsage.value + diskUsage.value) / 3
  if (avgLoad < 60) {
    systemStatus.value = '正常'
  } else if (avgLoad < 85) {
    systemStatus.value = '警告'
  } else {
    systemStatus.value = '异常'
  }
}

// 图表相关变量已移除

// 方法
const formatNumber = (num) => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 加载系统监控数据
const loadSystemMonitor = async () => {
  try {
    const data = await getSystemMonitor()

    // 更新系统监控数据
    cpuUsage.value = data.cpu.usage
    memoryUsage.value = data.memory.usage
    memoryUsed.value = data.memory.used_gb
    memoryTotal.value = data.memory.total_gb
    diskUsage.value = data.disk.usage
    diskUsed.value = data.disk.used_gb
    diskTotal.value = data.disk.total_gb

    // 更新系统状态
    updateSystemStatus()
    updateLastUpdateTime()
  } catch (err) {
    ElMessage.error('获取系统监控数据失败: ' + err.message)
  }
}

// 加载仪表盘概览数据
const loadDashboardOverview = async () => {
  try {
    const data = await getDashboardOverview()

    // 更新业务统计数据
    totalUsers.value = data.users.total
    userGrowth.value = data.users.growth_rate
    onlineUsers.value = data.users.online_count

    activeAgents.value = data.agents.active_count
    agentGrowth.value = data.agents.growth_rate

    todayConversations.value = data.conversations.today_count
    conversationGrowth.value = data.conversations.growth_rate

    totalFiles.value = data.files.total_count
    fileSize.value = data.files.total_size_gb

  } catch (err) {
    ElMessage.error('获取仪表盘概览失败: ' + err.message)
  }
}

// 刷新所有数据
const refreshCharts = async () => {
  await Promise.all([
    loadSystemMonitor(),
    loadDashboardOverview()
  ])
}

const updateLastUpdateTime = () => {
  const now = new Date()
  lastUpdateTime.value = now.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 模拟实时数据更新
let updateInterval = null

onMounted(async () => {
  updateLastUpdateTime()

  // 初始加载数据
  await refreshCharts()

  // 每30秒更新一次系统监控数据
  updateInterval = setInterval(async () => {
    await loadSystemMonitor()
  }, 30000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style scoped>
.admin-dashboard {
  padding: 0;
  overflow-y: auto;
  min-height: 100vh;
  background: #F5F8FB;
  position: relative;
}





/* 页面头部样式 */
.page-header-wrapper {
  margin: var(--spacing-lg) 0;
}

.page-header {
  background: #ffffff;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg) var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  width: 100%;
  box-sizing: border-box;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
}

.page-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.header-stats {
  display: flex;
  gap: var(--spacing-lg);
}

.quick-stat {
  text-align: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: #ffffff;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  min-width: 90px;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}



.quick-stat:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}



.quick-stat-label {
  display: block;
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.quick-stat-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  transition: color var(--transition-normal);
}



/* 系统状态样式 */
.system-status-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 16px;
  font-weight: 600;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
  flex-shrink: 0;
}

.status-indicator.status-good {
  background: var(--success-color);
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.status-indicator.status-warning {
  background: var(--warning-color);
  box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
}

.status-indicator.status-danger {
  background: var(--error-color);
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
}

.status-text.status-good {
  color: var(--success-color);
}

.status-text.status-warning {
  color: var(--warning-color);
}

.status-text.status-danger {
  color: var(--error-color);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 容器样式 */
.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-xl);
  box-sizing: border-box;
}

/* 区域标题样式 */
.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-light);
}

/* 系统监控区域 */
.system-monitor-section {
  margin-bottom: var(--spacing-2xl);
  background: #ffffff;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.monitor-card {
  transition: all var(--transition-normal);
  border: 1px solid var(--border-light);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.monitor-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.monitor-card:hover::before {
  transform: scaleX(1);
}

.monitor-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-light);
}

.monitor-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
}

.monitor-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
}

/* CPU监控样式 */
.cpu-indicator, .memory-indicator, .disk-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}



@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.progress-ring {
  position: relative;
}

.progress-ring circle {
  transition: stroke-dashoffset 0.5s ease;
}

/* 内存监控样式 */
.memory-details, .disk-details {
  flex: 1;
  margin-left: var(--spacing-md);
}

.memory-bar, .disk-bar {
  width: 100%;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: var(--spacing-xs);
}

.memory-used {
  height: 100%;
  background: linear-gradient(90deg, #909399, #409EFF);
  transition: width 0.5s ease;
}

.disk-used {
  height: 100%;
  background: linear-gradient(90deg, #E6A23C, #F56C6C);
  transition: width 0.5s ease;
}

.memory-text, .disk-text {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 紧凑系统监控样式 */
.compact-monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.compact-monitor-item {
  background: #ffffff;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.compact-monitor-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.monitor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.monitor-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.status-good {
  background: var(--success-color);
  box-shadow: 0 0 6px rgba(16, 185, 129, 0.4);
}

.status-dot.status-warning {
  background: var(--warning-color);
  box-shadow: 0 0 6px rgba(245, 158, 11, 0.4);
}

.status-dot.status-danger {
  background: var(--error-color);
  box-shadow: 0 0 6px rgba(239, 68, 68, 0.4);
}

.monitor-main {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.monitor-value-large {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  min-width: 80px;
}

.mini-progress {
  flex: 1;
  height: 6px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width 0.5s ease;
}

.memory-fill {
  background: linear-gradient(90deg, #909399, #409EFF);
}

.disk-fill {
  background: linear-gradient(90deg, #E6A23C, #F56C6C);
}

.network-fill {
  background: linear-gradient(90deg, #409EFF, #906EFF);
}

.monitor-details {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-sm);
}

.detail-item {
  font-size: 12px;
  color: var(--text-secondary);
  background: var(--gray-100);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  white-space: nowrap;
}

/* 业务统计卡片样式 */
.business-stat-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(10px);
}

.business-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.stat-card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.stat-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background: rgba(64, 158, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.business-stat-card:nth-child(1) .stat-icon-wrapper {
  background: rgba(64, 158, 255, 0.1);
}

.business-stat-card:nth-child(2) .stat-icon-wrapper {
  background: rgba(103, 194, 58, 0.1);
}

.business-stat-card:nth-child(3) .stat-icon-wrapper {
  background: rgba(230, 162, 60, 0.1);
}

.business-stat-card:nth-child(4) .stat-icon-wrapper {
  background: rgba(144, 147, 153, 0.1);
}

.stat-card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
}

.stat-card-content {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}

.stat-main-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-change-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
}

.change-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-secondary);
}

.change-period {
  font-size: 12px;
  color: var(--text-secondary);
  opacity: 0.7;
}

/* 业务统计区域 */
.business-stats-section {
  margin-bottom: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(10px);
}



/* 响应式调整 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

.stat-card {
  transition: all var(--transition-normal);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  overflow: hidden;
  position: relative;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform var(--transition-normal);
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.card-header .el-icon {
  color: var(--primary-color);
  font-size: 20px;
  transition: color var(--transition-normal);
}

.stat-card:hover .card-header .el-icon {
  color: var(--primary-light);
}

.stat-number {
  font-size: 36px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-change {
  font-size: 14px;
  font-weight: 500;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  display: inline-block;
}

.stat-change.positive {
  color: var(--text-secondary);
  background: var(--gray-100);
}

.stat-change.neutral {
  color: var(--text-secondary);
  background: var(--gray-100);
}

/* 数据分析区域样式已移除 */

/* 图表卡片样式已移除 */

/* 用户管理样式 */
.table-toolbar {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 页面占位符样式 */
.page-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.placeholder-content {
  text-align: center;
  color: #909399;
}

.placeholder-icon {
  margin-bottom: 16px;
  color: #c0c4cc;
}

/* 图表样式已移除 */

/* 柱状图样式已移除 */



/* 响应式设计 */
@media (max-width: 768px) {
  /* 图表相关响应式样式已移除 */
}
</style>