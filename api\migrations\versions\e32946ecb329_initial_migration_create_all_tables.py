"""Initial migration - create all tables

Revision ID: e32946ecb329
Revises: 
Create Date: 2025-07-21 14:11:22.502747

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e32946ecb329'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('User',
    sa.<PERSON>umn('user_id', sa.Integer(), autoincrement=True, nullable=False, comment='主键ID'),
    sa.Column('user_code', sa.CHAR(length=7), nullable=False, comment='用户编码'),
    sa.Column('username', sa.String(length=50), nullable=False, comment='用户名'),
    sa.Column('role', sa.Enum('student', 'teacher', 'admin', name='user_role_enum'), nullable=False, comment='用户角色'),
    sa.Column('external_user_id', sa.String(length=100), nullable=True, comment='外部系统用户ID'),
    sa.Column('external_token', sa.Text(), nullable=True, comment='外部系统访问令牌'),
    sa.Column('internal_refresh_token', sa.Text(), nullable=True, comment='内部刷新令牌，用于本地令牌刷新（已废弃）'),
    sa.Column('refresh_token_hash', sa.String(length=64), nullable=True, comment='刷新令牌哈希值，用于新的令牌管理系统'),
    sa.Column('token_expires_at', sa.DateTime(), nullable=True, comment='令牌过期时间'),
    sa.Column('client_fp', sa.String(length=100), nullable=True, comment='客户端指纹'),
    sa.Column('session_id', sa.Text(), nullable=True, comment='外部认证会话ID'),
    sa.Column('disabled', sa.Boolean(), nullable=False, comment='是否禁用'),
    sa.Column('last_login_at', sa.DateTime(), nullable=True, comment='最后登录时间'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.PrimaryKeyConstraint('user_id')
    )
    with op.batch_alter_table('User', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_User_external_user_id'), ['external_user_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_User_user_code'), ['user_code'], unique=True)
        batch_op.create_index(batch_op.f('ix_User_username'), ['username'], unique=False)

    op.create_table('channellist',
    sa.Column('channel_id', sa.Integer(), autoincrement=True, nullable=False, comment='主键ID'),
    sa.Column('channel_name', sa.String(length=255), nullable=False, comment='渠道名称'),
    sa.Column('channel_url', sa.String(length=255), nullable=False, comment='渠道URL'),
    sa.Column('channel_api_key', sa.String(length=255), nullable=False, comment='API密钥'),
    sa.Column('channel_api_type', sa.Enum('ollama', 'openai', 'zhipu', name='api_type_enum'), nullable=False, comment='API类型'),
    sa.Column('channel_api_enabled', sa.Boolean(), nullable=False, comment='是否启用'),
    sa.PrimaryKeyConstraint('channel_id')
    )
    op.create_table('online_tools',
    sa.Column('tools_id', sa.Integer(), autoincrement=True, nullable=False, comment='主键ID'),
    sa.Column('tools_name', sa.String(length=255), nullable=False, comment='工具名称'),
    sa.Column('tools_description', sa.String(length=255), nullable=False, comment='工具描述'),
    sa.Column('tools_url', sa.String(length=255), nullable=False, comment='工具URL'),
    sa.PrimaryKeyConstraint('tools_id')
    )
    op.create_table('conversations',
    sa.Column('conversation_id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('title', sa.String(length=100), nullable=False, comment='对话标题'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['user_id'], ['User.user_id'], ),
    sa.PrimaryKeyConstraint('conversation_id')
    )
    with op.batch_alter_table('conversations', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_conversations_conversation_id'), ['conversation_id'], unique=False)

    op.create_table('files',
    sa.Column('file_id', sa.String(length=36), nullable=False),
    sa.Column('filename', sa.String(length=255), nullable=False, comment='文件名'),
    sa.Column('file_path', sa.String(length=512), nullable=False, comment='文件路径'),
    sa.Column('file_type', sa.String(length=50), nullable=False, comment='文件类型'),
    sa.Column('file_size', sa.Integer(), nullable=False, comment='文件大小(字节)'),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('upload_time', sa.DateTime(), nullable=True, comment='上传时间'),
    sa.Column('status', sa.String(length=20), nullable=True, comment='处理状态'),
    sa.Column('description', sa.Text(), nullable=True, comment='文件描述'),
    sa.ForeignKeyConstraint(['user_id'], ['User.user_id'], ),
    sa.PrimaryKeyConstraint('file_id')
    )
    with op.batch_alter_table('files', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_files_file_id'), ['file_id'], unique=False)

    op.create_table('modellist',
    sa.Column('model_id', sa.Integer(), autoincrement=True, nullable=False, comment='主键ID'),
    sa.Column('channel_id', sa.Integer(), nullable=False, comment='渠道ID'),
    sa.Column('model_name', sa.String(length=255), nullable=False, comment='模型名称'),
    sa.Column('model_enabled', sa.Boolean(), nullable=False, comment='模型是否启用'),
    sa.ForeignKeyConstraint(['channel_id'], ['channellist.channel_id'], ),
    sa.PrimaryKeyConstraint('model_id')
    )
    with op.batch_alter_table('modellist', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_modellist_channel_id'), ['channel_id'], unique=False)

    op.create_table('ChatMessage',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键ID'),
    sa.Column('conversation_id', sa.String(length=36), nullable=False, comment='对话ID'),
    sa.Column('channel_id', sa.Integer(), nullable=False, comment='渠道ID'),
    sa.Column('role', sa.String(length=20), nullable=False, comment='角色(user/assistant)'),
    sa.Column('content', sa.Text(), nullable=False, comment='消息内容'),
    sa.Column('model_name', sa.String(length=50), nullable=False, comment='使用的模型名称'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.ForeignKeyConstraint(['channel_id'], ['channellist.channel_id'], ),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversations.conversation_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('ChatMessage', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_ChatMessage_conversation_id'), ['conversation_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_ChatMessage_id'), ['id'], unique=False)

    op.create_table('conversation_files',
    sa.Column('conversation_id', sa.String(length=36), nullable=False),
    sa.Column('file_id', sa.String(length=36), nullable=False),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversations.conversation_id'], ),
    sa.ForeignKeyConstraint(['file_id'], ['files.file_id'], ),
    sa.PrimaryKeyConstraint('conversation_id', 'file_id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('conversation_files')
    with op.batch_alter_table('ChatMessage', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_ChatMessage_id'))
        batch_op.drop_index(batch_op.f('ix_ChatMessage_conversation_id'))

    op.drop_table('ChatMessage')
    with op.batch_alter_table('modellist', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_modellist_channel_id'))

    op.drop_table('modellist')
    with op.batch_alter_table('files', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_files_file_id'))

    op.drop_table('files')
    with op.batch_alter_table('conversations', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_conversations_conversation_id'))

    op.drop_table('conversations')
    op.drop_table('online_tools')
    op.drop_table('channellist')
    with op.batch_alter_table('User', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_User_username'))
        batch_op.drop_index(batch_op.f('ix_User_user_code'))
        batch_op.drop_index(batch_op.f('ix_User_external_user_id'))

    op.drop_table('User')
    # ### end Alembic commands ###
