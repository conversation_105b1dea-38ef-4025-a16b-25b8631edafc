<template>
  <div class="greeting-container">
    <div class="greeting-block" v-if="showGreeting">
      <h1 class="greeting-title">{{ displayText }}</h1>
    </div>
    <div class="MessageContainer">
      <div class="ChatMessageContainer">
        <ChatMessages :messages="chatStore.currentHistory" />
      </div>
      <div :class="['input-bottom', { 'fixed-bottom': !showGreeting }]">
        <MessageInput />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useUserStore } from '@/stores/userStore'
import { useChatStore } from '@/stores/chatStore'
import { useRoute, useRouter } from 'vue-router'
import ChatMessages from './ChatMessages.vue'
import MessageInput from '@/components/Chat/MessageInput.vue'
import { ElMessage } from 'element-plus'

// 接收路由参数
const props = defineProps({
  id: {
    type: String,
    default: null
  }
})

const userStore = useUserStore()
const chatStore = useChatStore()
const route = useRoute()
const router = useRouter()
const username = computed(() => userStore.userProfile.username || '用户')
const showGreeting = computed(() => !chatStore.currentConversationId && chatStore.currentHistory.length === 0)

// 打字机效果相关的变量
const displayText = ref('')
const isTyping = ref(true)

// 问候语根据现实时间变化
const greetingMessage = computed(() => {
  const hour = new Date().getHours()
  const minute = new Date().getMinutes()
  const currentTime = hour * 60 + minute // 转换为分钟计算

  // 时间段划分（转换为分钟）
  const earlyMorning = { start: 6 * 60, end: 11 * 60 + 30 } // 6:00-11:30
  const noon = { start: 11 * 60 + 30, end: 13 * 60 + 30 } // 11:30-13:30
  const afternoon = { start: 13 * 60 + 30, end: 18 * 60 } // 13:30-18:00
  const evening = { start: 18 * 60, end: 22 * 60 } // 18:00-22:00
  const lateNight = { start: 22 * 60, end: 24 * 60 } // 22:00-24:00
  const beforeDawn = { start: 0, end: 6 * 60 } // 0:00-6:00

  if ((currentTime >= earlyMorning.start) && (currentTime < earlyMorning.end)) {
    return '上午好'
  } else if ((currentTime >= noon.start) && (currentTime < noon.end)) {
    return '中午好'
  } else if ((currentTime >= afternoon.start) && (currentTime < afternoon.end)) {
    return '下午好'
  } else if ((currentTime >= evening.start) && (currentTime < evening.end)) {
    return '晚上好'
  } else if ((currentTime >= lateNight.start) || (currentTime < beforeDawn.end)) {
    return '深夜好'
  } else {
    return '你好' // 默认问候语
  }
})

// 生成随机延迟时间
const getRandomDelay = () => {
  // 基础延迟时间 15ms
  const baseDelay = 15
  // 随机额外延迟 0-20ms
  const randomDelay = Math.random() * 20
  return baseDelay + randomDelay
}

// 打字机效果函数
const typeWriter = () => {
  // 确保在开始打字效果前，显示文本是空的
  displayText.value = ''
  isTyping.value = true
  
  const fullText = `${greetingMessage.value}，${username.value}`
  // console.log('开始打字机效果，文本:', fullText);
  let currentIndex = 0

  const type = () => {
    if (currentIndex < fullText.length) {
      displayText.value = fullText.slice(0, currentIndex + 1)
      currentIndex++
      // 使用随机延迟
      setTimeout(type, getRandomDelay())
    } else {
      isTyping.value = false
      // console.log('打字机效果完成');
    }
  }

  type()
}

// 重置并显示欢迎消息
const resetAndShowGreeting = async () => {
  // 重置状态
  chatStore.setCurrentConversationId(null)
  chatStore.setCurrentConversationHistory([])
  
  // 确保DOM更新后再开始打字效果
  await nextTick()
  typeWriter()
}

// 监听用户名变化
watch(username, () => {
  // 如果当前应该显示欢迎语，则重新开始打字机效果
  if (showGreeting.value) {
    typeWriter()
  }
})

// 监听路由变化，处理切换到新对话的情况
watch(
  () => route.name,
  (newRouteName) => {
    if (newRouteName === 'NewChat') {
      resetAndShowGreeting()
    }
  }
)

// 加载对话历史记录
const loadConversation = async (conversationId) => {
  if (!conversationId) return false;

  try {
    await chatStore.loadConversationHistory(conversationId);
    return true;
  } catch (error) {
    // console.error("加载对话失败:", error);
    ElMessage.error("加载对话失败，请重试");

    // 如果加载失败，重定向到新对话页
    router.push({ name: 'NewChat' });
    return false;
  }
}

// 组件挂载时的处理
onMounted(async () => {
  // console.log('GreetingBlock mounted');

  // 检查 URL 中是否有会话 ID
  const conversationId = props.id || route.params.id;
  // console.log('URL conversationId:', conversationId, 'route.name:', route.name);

  // 如果是 NewChat 路由，强制重置 currentConversationId
  if (route.name === 'NewChat') {
    // console.log('强制重置 currentConversationId 为 null');
    await resetAndShowGreeting();
    return; // 结束函数执行
  }

  if (conversationId) {
    // 如果有会话 ID，尝试加载该会话
    // console.log("正在加载对话:", conversationId);
    const loaded = await loadConversation(conversationId);
    // console.log('对话加载结果:', loaded);

    // 如果加载失败，显示欢迎消息
    if (!loaded) {
      await resetAndShowGreeting();
    }
  } else {
    // 如果没有会话 ID，显示欢迎消息
    await resetAndShowGreeting();
  }
})
</script>

<style scoped>
.greeting-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

.greeting-block {
  margin-bottom: 20px; /* 减小底部间距，使欢迎语和输入框更接近 */
  margin-top: 30vh; /* 调整顶部边距，使整体位置更合理 */
  padding: 20px;
  text-align: center;
  background-color: rgba(240, 240, 255, 0.05);
  border-radius: 12px;
  width: 750px;
}

.input-bottom {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 0; /* 移除顶部间距，让输入框更接近欢迎语 */
}

.fixed-bottom {
  position: fixed;
  left: calc(50vw + 125px);
  transform: translateX(-50%);
  bottom: 30px; /* 恢复原来的底部距离，使发送消息后的输入框在底部 */
  z-index: 10;
  padding-bottom: 0;
  max-width: 750px;
  display: flex;
  justify-content: center;
  padding-top: 30px;
}

.greeting-block[style*='display: none']+.bubble-messages {
  margin-top: 0 !important;
}

.greeting-title {
  font-size: 32px;
  font-weight: bold;
  color: #222;
  letter-spacing: 1px;
  min-height: 48px;
}

.MessageContainer {
  height: calc(100vh - 230px);
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 改为从顶部开始排列 */
  margin-top: 0; /* 移除负边距 */
}

/* .ChatMessageContainer {
  height: calc(100% - 800px);
} */
</style>