"""
JWT令牌管理器
负责生成、验证和管理JWT令牌
"""

import jwt
import uuid
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional
from flask import current_app


class TokenError(Exception):
    """令牌相关错误"""
    pass


class JWTManager:
    """
    内部JWT令牌管理器
    完全独立的令牌生成和验证系统
    """
    
    def __init__(self, app=None):
        self.app = app
        self.secret_key = None
        self.access_token_expires = timedelta(minutes=30)
        self.refresh_token_expires = timedelta(days=7)
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化JWT管理器"""
        self.secret_key = app.config.get('JWT_SECRET_KEY', app.config.get('SECRET_KEY'))
        
        if not self.secret_key:
            raise ValueError("JWT_SECRET_KEY 或 SECRET_KEY 必须配置")
        
        # 从配置读取过期时间
        access_minutes = app.config.get('JWT_ACCESS_TOKEN_EXPIRES', 30)
        refresh_days = app.config.get('JWT_REFRESH_TOKEN_EXPIRES', 7)
        
        self.access_token_expires = timedelta(minutes=access_minutes)
        self.refresh_token_expires = timedelta(days=refresh_days)
        
        app.logger.info(f"JWT管理器初始化完成 - Access Token: {access_minutes}分钟, Refresh Token: {refresh_days}天")
    
    def create_access_token(self, user_id: int, additional_claims: Optional[Dict] = None) -> str:
        """
        生成访问令牌
        
        Args:
            user_id: 用户ID
            additional_claims: 额外的声明信息
            
        Returns:
            str: JWT访问令牌
        """
        now = datetime.utcnow()
        
        payload = {
            'user_id': user_id,
            'type': 'access',
            'exp': now + self.access_token_expires,
            'iat': now,
            'jti': str(uuid.uuid4())
        }
        
        # 添加额外声明
        if additional_claims:
            payload.update(additional_claims)
        
        try:
            token = jwt.encode(payload, self.secret_key, algorithm='HS256')
            # current_app.logger.debug(f"生成访问令牌成功 - 用户ID: {user_id}")
            return token
        except Exception as e:
            # current_app.logger.error(f"生成访问令牌失败: {e}")
            raise TokenError("令牌生成失败")
    
    def create_refresh_token(self, user_id: int) -> str:
        """
        生成刷新令牌
        
        Args:
            user_id: 用户ID
            
        Returns:
            str: JWT刷新令牌
        """
        now = datetime.utcnow()
        
        payload = {
            'user_id': user_id,
            'type': 'refresh',
            'exp': now + self.refresh_token_expires,
            'iat': now,
            'jti': str(uuid.uuid4())
        }
        
        try:
            token = jwt.encode(payload, self.secret_key, algorithm='HS256')
            # logger.debug(f"生成刷新令牌成功 - 用户ID: {user_id}")
            return token
        except Exception as e:
            # logger.error(f"生成刷新令牌失败: {e}")
            raise TokenError("刷新令牌生成失败")
    
    def decode_access_token(self, token: str) -> Dict:
        """
        解码访问令牌
        
        Args:
            token: JWT访问令牌
            
        Returns:
            Dict: 令牌载荷
            
        Raises:
            TokenError: 令牌无效或过期
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])

            if payload.get('type') != 'access':
                raise TokenError("令牌类型错误")

            # 检查令牌是否在黑名单中
            jti = payload.get('jti')
            if jti and self.is_token_blacklisted(jti):
                raise TokenError("令牌已被撤销")

            # logger.debug(f"访问令牌验证成功 - 用户ID: {payload.get('user_id')}")
            return payload

        except jwt.ExpiredSignatureError:
            # logger.warning("访问令牌已过期")
            raise TokenError("访问令牌已过期")
        except jwt.InvalidTokenError as e:
            # logger.warning(f"无效的访问令牌: {e}")
            raise TokenError("无效的访问令牌")
        except Exception as e:
            # logger.error(f"访问令牌解码失败: {e}")
            raise TokenError("令牌解码失败")
    
    def decode_refresh_token(self, token: str) -> Dict:
        """
        解码刷新令牌
        
        Args:
            token: JWT刷新令牌
            
        Returns:
            Dict: 令牌载荷
            
        Raises:
            TokenError: 令牌无效或过期
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])

            if payload.get('type') != 'refresh':
                raise TokenError("令牌类型错误")

            # 检查令牌是否在黑名单中
            jti = payload.get('jti')
            if jti and self.is_token_blacklisted(jti):
                raise TokenError("令牌已被撤销")

            # logger.debug(f"刷新令牌验证成功 - 用户ID: {payload.get('user_id')}")
            return payload

        except jwt.ExpiredSignatureError:
            # logger.warning("刷新令牌已过期")
            raise TokenError("刷新令牌已过期")
        except jwt.InvalidTokenError as e:
            # logger.warning(f"无效的刷新令牌: {e}")
            raise TokenError("无效的刷新令牌")
        except Exception as e:
            # logger.error(f"刷新令牌解码失败: {e}")
            raise TokenError("刷新令牌解码失败")
    
    def is_token_expired(self, token: str) -> bool:
        """
        检查令牌是否过期
        
        Args:
            token: JWT令牌
            
        Returns:
            bool: True表示已过期，False表示未过期
        """
        try:
            jwt.decode(token, self.secret_key, algorithms=['HS256'])
            return False
        except jwt.ExpiredSignatureError:
            return True
        except jwt.InvalidTokenError:
            return True
    
    def get_token_payload(self, token: str) -> Optional[Dict]:
        """
        获取令牌载荷（不验证过期时间）
        
        Args:
            token: JWT令牌
            
        Returns:
            Dict: 令牌载荷，如果令牌无效则返回None
        """
        try:
            payload = jwt.decode(
                token, 
                self.secret_key, 
                algorithms=['HS256'],
                options={"verify_exp": False}
            )
            return payload
        except jwt.InvalidTokenError:
            return None
    
    @staticmethod
    def hash_token(token: str) -> str:
        """
        生成令牌哈希值（用于数据库存储）
        
        Args:
            token: 原始令牌
            
        Returns:
            str: 令牌哈希值
        """
        return hashlib.sha256(token.encode()).hexdigest()
    
    @staticmethod
    def verify_token_hash(token: str, token_hash: str) -> bool:
        """
        验证令牌哈希值
        
        Args:
            token: 原始令牌
            token_hash: 存储的哈希值
            
        Returns:
            bool: 验证结果
        """
        return hashlib.sha256(token.encode()).hexdigest() == token_hash
    
    def revoke_token(self, token: str) -> bool:
        """
        撤销令牌（将JTI添加到黑名单）

        Args:
            token: 要撤销的令牌

        Returns:
            bool: 撤销是否成功
        """
        logger = logging.getLogger(__name__)

        try:
            # 获取令牌载荷
            payload = self.get_token_payload(token)
            if not payload:
                logger.warning("无法获取令牌载荷，撤销失败")
                return False

            jti = payload.get('jti')
            user_id = payload.get('user_id')
            token_type = payload.get('type', 'access')
            exp_timestamp = payload.get('exp')

            if not jti or not user_id or not exp_timestamp:
                logger.warning("令牌载荷缺少必要字段，撤销失败")
                return False

            # 转换过期时间
            expires_at = datetime.fromtimestamp(exp_timestamp)

            # 导入模型（避免循环导入）
            from app.models.token_blacklist import TokenBlacklist
            from app import db

            # 检查是否已经在黑名单中
            if TokenBlacklist.is_blacklisted(jti):
                logger.info(f"令牌已在黑名单中 - JTI: {jti}")
                return True

            # 添加到黑名单
            blacklist_entry = TokenBlacklist.add_to_blacklist(
                jti=jti,
                user_id=user_id,
                expires_at=expires_at,
                token_type=token_type
            )

            db.session.commit()
            logger.info(f"令牌已撤销 - JTI: {jti}, 用户ID: {user_id}, 类型: {token_type}")
            return True

        except Exception as e:
            logger.error(f"撤销令牌失败: {e}")
            # 回滚数据库事务
            try:
                from app import db
                db.session.rollback()
            except:
                pass
            return False

    def is_token_blacklisted(self, jti: str) -> bool:
        """
        检查令牌是否在黑名单中

        Args:
            jti: JWT唯一标识符

        Returns:
            bool: 是否在黑名单中
        """
        try:
            # 导入模型（避免循环导入）
            from app.models.token_blacklist import TokenBlacklist
            return TokenBlacklist.is_blacklisted(jti)
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"检查黑名单失败: {e}")
            # 出错时为了安全起见，假设不在黑名单中
            return False


# 全局JWT管理器实例
jwt_manager = JWTManager()


def init_jwt_manager(app):
    """初始化JWT管理器"""
    jwt_manager.init_app(app)
    return jwt_manager
