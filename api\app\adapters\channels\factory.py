"""渠道工厂类，用于创建不同类型的渠道实例"""
from typing import Dict, Type, Optional
from .base import BaseChannel


class ChannelFactory:
    """渠道工厂，根据类型创建渠道实例"""
    
    # 渠道类型与实现类的映射
    _channel_types: Dict[str, Type[BaseChannel]] = {}
    
    @classmethod
    def register_channel(cls, channel_type: str, channel_class: Type[BaseChannel]):
        """
        注册渠道类型
        
        Args:
            channel_type: 渠道类型标识
            channel_class: 渠道实现类
        """
        cls._channel_types[channel_type] = channel_class
    
    @classmethod
    def create_channel(
        cls, 
        channel_type: str,
        base_url: Optional[str] = None,
        api_key: Optional[str] = None,
        **kwargs
    ) -> BaseChannel:
        """
        创建渠道实例
        
        Args:
            channel_type: 渠道类型标识
            base_url: API基础URL
            api_key: API密钥
            **kwargs: 其他参数
            
        Returns:
            渠道实例
            
        Raises:
            ValueError: 如果渠道类型未注册
        """
        if channel_type not in cls._channel_types:
            # 如果渠道类型未注册，返回一个模拟渠道
            from .mock import MockChannel
            return MockChannel(base_url, api_key, **kwargs)
            
        channel_class = cls._channel_types[channel_type]
        
        try:
            channel = channel_class(base_url, api_key, **kwargs)
            return channel
        except Exception as e:
            raise

    @classmethod
    def get_registered_channel_types(cls) -> list[str]:
        """
        获取所有已注册的渠道类型
        
        Returns:
            渠道类型名称列表
        """
        return list(cls._channel_types.keys()) 