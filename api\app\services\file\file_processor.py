"""文件处理服务"""
import os
import shutil
from sqlalchemy.orm import Session
from app.models.file import File
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from .file_parser import FileParser
from app.utils.json_utils import datetime_to_isoformat

class FileProcessor:
    """文件处理服务"""
    
    ALLOWED_EXTENSIONS = {
        "txt", "pdf", "doc", "docx", "csv", "xls", "xlsx", "md", "json"
    }
    UPLOAD_DIR = "uploads"
    MAX_FILE_SIZE_MB = 50
    MAX_FILE_SIZE = MAX_FILE_SIZE_MB * 1024 * 1024  # 50MB
    
    def __init__(self, db: Session):
        self.db = db
        
        # 确保上传目录存在
        os.makedirs(self.UPLOAD_DIR, exist_ok=True)
        
        # 初始化组件
        self.file_parser = FileParser()
    
    def is_valid_file_type(self, filename: str) -> bool:
        """检查文件类型是否支持"""
        return self.get_file_extension(filename) in self.ALLOWED_EXTENSIONS
    
    def get_file_extension(self, filename: str) -> str:
        """获取文件扩展名"""
        return filename.rsplit(".", 1)[1].lower() if "." in filename else ""
    
    def save_file(self, file, user_id, description: str = None) -> File:
        """保存上传的文件并创建数据库记录"""
        # 确保user_id是整数
        if not isinstance(user_id, int):
            try:
                user_id = int(user_id)
            except (ValueError, TypeError):
                raise ValueError(f"用户ID必须为整数: {user_id}")
                
        # 生成唯一的文件ID
        file_id = str(uuid.uuid4())
        
        # 创建用户文件目录
        user_dir = os.path.join(self.UPLOAD_DIR, str(user_id))
        os.makedirs(user_dir, exist_ok=True)
        
        # 安全处理文件名
        safe_filename = self._sanitize_filename(file.filename)
        file_path = os.path.join(user_dir, f"{file_id}_{safe_filename}")
        
        # 保存文件 - 修复处理不同类型的FileStorage对象
        try:
            # 尝试直接保存文件
            file.save(file_path)
        except (AttributeError, TypeError):
            # 如果没有save方法，尝试其他方法
            try:
                # 尝试使用stream属性
                if hasattr(file, 'stream'):
                    with open(file_path, "wb") as buffer:
                        shutil.copyfileobj(file.stream, buffer)
                # 尝试使用file属性
                elif hasattr(file, 'file'):
                    with open(file_path, "wb") as buffer:
                        shutil.copyfileobj(file.file, buffer)
                # 如果是bytes或file-like对象
                elif hasattr(file, 'read'):
                    with open(file_path, "wb") as buffer:
                        shutil.copyfileobj(file, buffer)
                else:
                    # 最后尝试直接写入
                    with open(file_path, "wb") as buffer:
                        buffer.write(file.read())
            except Exception as e:
                raise ValueError(f"无法保存文件: {str(e)}")
        
        # 获取文件大小
        file_size = os.path.getsize(file_path)
        
        # 获取文件类型
        file_type = self.get_file_extension(safe_filename)
        
        # 创建数据库记录
        db_file = File(
            file_id=file_id,
            filename=safe_filename,
            file_path=file_path,
            file_type=file_type,
            file_size=file_size,
            user_id=user_id,
            upload_time=datetime.utcnow(),
            status="pending",
            description=description
        )
        
        self.db.add(db_file)
        self.db.commit()
        self.db.refresh(db_file)
        
        return db_file
    
    def _sanitize_filename(self, filename: str) -> str:
        """安全处理文件名，防止路径遍历、特殊字符注入等安全问题"""
        import re
        import uuid

        # 首先获取基础文件名，防止路径遍历
        safe_name = os.path.basename(filename)

        # 过滤危险字符：< > : " / \ | ? * 以及控制字符
        safe_name = re.sub(r'[<>:"/\\|?*\x00-\x1f\x7f]', '_', safe_name)

        # 移除开头和结尾的点和空格，防止隐藏文件和特殊名称
        safe_name = safe_name.strip('. ')

        # 处理空文件名或特殊名称
        if not safe_name or safe_name in ['.', '..', 'CON', 'PRN', 'AUX', 'NUL'] or \
           safe_name.upper().startswith(('COM', 'LPT')):
            # 生成安全的默认文件名
            safe_name = f'file_{uuid.uuid4().hex[:8]}'

        # 限制文件名长度，防止过长文件名
        if len(safe_name) > 255:
            # 保留文件扩展名
            name_part, ext_part = os.path.splitext(safe_name)
            max_name_len = 255 - len(ext_part)
            safe_name = name_part[:max_name_len] + ext_part

        return safe_name
    
    def process_file(self, file_id: str) -> None:
        """处理上传的文件：解析为markdown并保存到本地文件"""
        # 获取文件记录
        db_file = self.db.query(File).filter(File.file_id == file_id).first()
        if not db_file:
            return
        
        try:
            # 更新状态为处理中
            db_file.status = "processing"
            self.db.commit()
            
            # 打印进度信息
            print(f"开始处理文件: {db_file.filename} (ID: {file_id})")
            
            # 1. 解析文件内容为markdown
            content = self.file_parser.parse_file(db_file.file_path, db_file.file_type)
            
            # 如果文件内容为空，标记为处理失败
            if not content or len(content.strip()) == 0:
                print(f"文件内容为空: {db_file.filename}")
                db_file.status = "failed"
                db_file.description = "文件内容为空"
                self.db.commit()
                return
            
            print(f"文件解析成功，提取到内容长度: {len(content)} 字符")
            
            # 2. 保存markdown内容到本地文件
            markdown_path = self.save_markdown_content(
                str(db_file.user_id), 
                db_file.file_id, 
                content
            )
            
            # 3. 更新文件状态为已处理
            db_file.status = "processed"
            db_file.description = f"已解析为markdown，内容长度: {len(content)} 字符"
            self.db.commit()
            print(f"文件处理成功并已保存到: {markdown_path}")
            
        except Exception as e:
            # 处理失败，更新状态
            import traceback
            print(f"处理文件失败: {str(e)}")
            traceback.print_exc()
            db_file.status = "failed"
            db_file.description = f"处理失败: {str(e)}"
            self.db.commit()
    
    def get_user_files(self, user_id: str, skip: int = 0, limit: int = 100) -> list:
        """获取用户的文件列表，带分页"""
        return self.db.query(File)\
            .filter(File.user_id == user_id)\
            .order_by(File.upload_time.desc())\
            .offset(skip)\
            .limit(limit)\
            .all()
    
    def count_user_files(self, user_id: str) -> int:
        """统计用户的文件总数"""
        return self.db.query(File).filter(File.user_id == user_id).count()
    
    def reprocess_file(self, file_id: str) -> bool:
        """重新处理文件"""
        db_file = self.db.query(File).filter(File.file_id == file_id).first()
        if not db_file:
            return False
        
        # 删除现有的markdown文件
        self.delete_file_contents(str(db_file.user_id), file_id)
        
        # 重置状态
        db_file.status = "pending"
        db_file.description = "等待重新处理"
        self.db.commit()
        
        return True
    
    def get_files_content(
        self, 
        file_ids: List[str], 
        user_id: Optional[str] = None,
        max_content_length: int = 200000  # 调整为200k字符，支持大上下文模型
    ) -> List[Dict[str, Any]]:
        """
        获取指定文件的markdown内容
        
        Args:
            file_ids: 文件ID列表
            user_id: 用户ID，用于权限验证
            max_content_length: 单个文件的最大内容长度（默认200k字符，支持大上下文模型）
            
        Returns:
            文件内容列表
        """
        results = []
        
        for file_id in file_ids:
            # 获取文件信息
            query = self.db.query(File).filter(File.file_id == file_id)
            if user_id:
                query = query.filter(File.user_id == user_id)
            
            db_file = query.first()
            if not db_file or db_file.status != "processed":
                continue
                
            # 加载markdown内容
            content = self.load_markdown_content(str(db_file.user_id), file_id)
            if not content:
                continue
                
            # 截断过长的内容
            if len(content) > max_content_length:
                content = content[:max_content_length] + "...[内容已截断]"
                
            # 确保datetime被正确序列化
            results.append({
                "file_id": db_file.file_id,
                "filename": db_file.filename,
                "content": content,
                "file_size": len(content),
                "upload_time": datetime_to_isoformat(db_file.upload_time)
            })
        
        return results
    
    def get_markdown_path(self, user_id: str, file_id: str) -> str:
        """获取markdown文件路径"""
        user_dir = os.path.join(self.UPLOAD_DIR, str(user_id))
        return os.path.join(user_dir, f"{file_id}_content.md")
    
    def get_original_filename(self, user_id: str, file_id: str, original_filename: str) -> str:
        """获取原始文件保存路径"""
        user_dir = os.path.join(self.UPLOAD_DIR, str(user_id))
        safe_filename = self._sanitize_filename(original_filename)
        return os.path.join(user_dir, f"{file_id}_{safe_filename}")
    
    def save_markdown_content(self, user_id: str, file_id: str, content: str) -> str:
        """保存markdown内容到文件"""
        markdown_path = self.get_markdown_path(user_id, file_id)
        os.makedirs(os.path.dirname(markdown_path), exist_ok=True)
        
        with open(markdown_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return markdown_path
    
    def load_markdown_content(self, user_id: str, file_id: str) -> str:
        """加载markdown内容"""
        markdown_path = self.get_markdown_path(user_id, file_id)
        if not os.path.exists(markdown_path):
            return ""
        
        try:
            with open(markdown_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"读取markdown文件失败: {e}")
            return ""
    
    def delete_file_contents(self, user_id: str, file_id: str):
        """删除文件的所有内容（原始文件和markdown文件）"""
        # 删除markdown文件
        markdown_path = self.get_markdown_path(user_id, file_id)
        if os.path.exists(markdown_path):
            try:
                os.remove(markdown_path)
            except Exception as e:
                print(f"删除markdown文件失败: {e}")
        
        # 注意：原始文件路径需要从数据库记录中获取，这里暂不删除
        # 如果需要删除原始文件，应该在调用此方法前获取file_path 