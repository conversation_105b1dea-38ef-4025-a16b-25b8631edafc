# Python字节码文件
__pycache__/
*.py[cod]
*$py.class

# 虚拟环境
.venv/
venv/
ENV/
env/

# IDE和编辑器配置
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Flask生成的文件
instance/
.webassets-cache

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 日志文件
*.log
logs/

# 环境变量文件
.env
.flaskenv

# 上传的文件和敏感数据
uploads/*
uploads/
# 构建和分发文件
dist/
build/
*.egg-info/

# 测试覆盖率报告
.coverage
htmlcov/

# 其他临时文件
*.bak
*.tmp
*~
.pytest_cache/
.mypy_cache/

# UV包管理器文件
.venv/
uv.lock

# 项目特定文件
.pytest_cache/