"""
安全模块
提供密码哈希验证等安全功能
注意：JWT功能已迁移到自定义JWT管理器
"""

import secrets
from datetime import timed<PERSON><PERSON>
from typing import Optional, Dict, Any
from werkzeug.security import generate_password_hash, check_password_hash


class SecurityManager:
    """安全管理器"""
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """获取密码哈希值"""
        return generate_password_hash(password)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return check_password_hash(hashed_password, plain_password)
    
    # JWT功能已迁移到自定义JWT管理器 (app.core.jwt_manager)
    
    @staticmethod
    def generate_secure_random_string(length: int = 32) -> str:
        """
        生成安全的随机字符串
        
        Args:
            length: 字符串长度
            
        Returns:
            随机字符串
        """
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def create_api_key(prefix: str = "sk", length: int = 32) -> str:
        """
        创建API密钥
        
        Args:
            prefix: 密钥前缀
            length: 随机部分长度
            
        Returns:
            格式化的API密钥
        """
        random_part = SecurityManager.generate_secure_random_string(length)
        return f"{prefix}-{random_part}"


# 便捷函数
def get_password_hash(password: str) -> str:
    """获取密码哈希值（便捷函数）"""
    return SecurityManager.get_password_hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码（便捷函数）"""
    return SecurityManager.verify_password(plain_password, hashed_password)


# 所有JWT相关函数已迁移到 app.core.jwt_manager 和 app.core.auth_decorators