# 🌐 PUMI Agent Web 前端

> **现代化AI智能体平台前端应用** - 基于Vue 3构建的企业级SPA应用

[![Vue](https://img.shields.io/badge/Vue-3.5+-green.svg)](https://vuejs.org/)
[![Vite](https://img.shields.io/badge/Vite-6.2+-blue.svg)](https://vitejs.dev/)
[![Element Plus](https://img.shields.io/badge/Element%20Plus-2.9+-orange.svg)](https://element-plus.org/)
[![Pinia](https://img.shields.io/badge/Pinia-3.0+-yellow.svg)](https://pinia.vuejs.org/)

## 🌟 核心特性

### 🎨 现代化UI设计
- **响应式布局** - 适配桌面端和移动端
- **Element Plus组件** - 企业级UI组件库
- **统一设计系统** - 一致的视觉风格和交互体验
- **主题定制** - 支持亮色/暗色主题切换
- **动画效果** - 流畅的页面转场和交互动画

### 💬 智能对话体验
- **实时聊天** - 流式响应，即时消息显示
- **多轮对话** - 上下文记忆，连续对话体验
- **富文本支持** - Markdown渲染，代码高亮
- **文件上传** - 拖拽上传，多格式支持
- **对话管理** - 历史记录，对话分类

### 🤖 智能体平台
- **智能体市场** - 浏览、搜索、分类管理
- **智能体详情** - 详细信息展示和配置
- **一键对话** - 快速启动智能体对话
- **个性化推荐** - 基于使用习惯的智能推荐

### 🔐 安全认证
- **JWT认证** - 无状态令牌认证机制
- **自动刷新** - 令牌自动续期，无感知登录
- **权限控制** - 基于角色的页面访问控制
- **安全存储** - 敏感信息加密存储

## 🏗️ 技术架构

```mermaid
graph TB
    subgraph "Presentation Layer"
        A[Vue 3 Components]
        B[Vue Router]
        C[Element Plus UI]
    end

    subgraph "State Management"
        D[Pinia Stores]
        E[Persistent State]
        F[API Cache]
    end

    subgraph "Service Layer"
        G[Axios HTTP Client]
        H[Token Service]
        I[API Services]
    end

    subgraph "Build & Dev Tools"
        J[Vite Build Tool]
        K[ESLint]
        L[Sass Preprocessor]
    end

    A --> D
    B --> A
    C --> A
    D --> G
    E --> D
    F --> D
    G --> I
    H --> G
    I --> G
    J --> A
    K --> A
    L --> A
```

## 📁 项目结构

```
web/
├── 📂 src/                     # 源代码目录
│   ├── 📂 assets/             # 静态资源
│   │   ├── 📂 img/            # 图片资源
│   │   ├── 📂 svg/            # SVG图标
│   │   ├── 📄 base.css        # 基础样式
│   │   └── 📄 main.css        # 主样式文件
│   ├── 📂 components/         # 公共组件
│   │   ├── 📂 Chat/           # 聊天相关组件
│   │   ├── 📂 Common/         # 通用组件
│   │   ├── 📂 Sidebar/        # 侧边栏组件
│   │   ├── 📄 AIChatHeader.vue    # 聊天头部组件
│   │   ├── 📄 Sidebar.vue         # 侧边栏组件
│   │   └── 📄 TokenDebugger.vue   # 令牌调试组件
│   ├── 📂 layouts/            # 布局组件
│   │   └── 📄 AdminLayout.vue     # 管理后台布局
│   ├── 📂 router/             # 路由配置
│   │   └── 📄 index.js            # 路由定义
│   ├── 📂 services/           # API服务
│   │   ├── 📄 axiosConfig.js      # Axios配置
│   │   ├── 📄 tokenRefreshService.js  # 令牌刷新服务
│   │   └── 📄 tokenService.js     # 令牌管理服务
│   ├── 📂 stores/             # Pinia状态管理
│   │   ├── 📄 agentStore.js       # 智能体状态
│   │   ├── 📄 apiConfig.js        # API配置
│   │   ├── 📄 apiStore.js         # API状态
│   │   ├── 📄 authStore.js        # 认证状态
│   │   ├── 📄 chatStore.js        # 聊天状态
│   │   ├── 📄 modelStore.js       # 模型状态
│   │   └── 📄 userStore.js        # 用户状态
│   ├── 📂 utils/              # 工具函数
│   │   ├── 📄 tokenTest.js        # 令牌测试工具
│   │   └── 📄 unifiedStorage.js   # 统一存储工具
│   ├── 📂 views/              # 页面视图
│   │   ├── 📄 AdminDashboard.vue  # 管理后台
│   │   ├── 📄 AgentChat.vue       # 智能体聊天
│   │   ├── 📄 AgentPlaza.vue      # 智能体广场
│   │   ├── 📄 Chat.vue            # 聊天页面
│   │   └── 📄 Login.vue           # 登录页面
│   ├── 📄 App.vue             # 根组件
│   └── 📄 main.js             # 应用入口
├── 📂 public/                 # 公共静态资源
├── 📄 package.json            # 项目配置和依赖
├── 📄 vite.config.js          # Vite构建配置
├── 📄 eslint.config.js        # ESLint配置
├── 📄 jsconfig.json           # JavaScript配置
├── 📄 purgecss.config.js      # PurgeCSS配置
└── 📄 README.md               # 项目文档
```

## 🚀 快速开始

### 📋 环境要求

| 工具 | 版本要求 | 说明 |
|------|----------|------|
| **Node.js** | 16+ | JavaScript运行环境 |
| **npm** | 8+ | 包管理器 |
| **现代浏览器** | - | Chrome 90+, Firefox 88+, Safari 14+ |

### 🔧 安装配置

#### 1. 克隆项目
```bash
git clone <repository-url>
cd pumi-agent/web
```

#### 2. 安装依赖
```bash
# 使用npm安装
npm install

# 或使用yarn安装
yarn install

# 或使用pnpm安装
pnpm install
```

#### 3. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
vim .env  # 或使用其他编辑器
```

#### 4. 启动开发服务器
```bash
npm run dev
```

✅ **前端服务启动成功！**
- 开发服务器: `http://localhost:5173`
- 自动打开浏览器并启用热重载

### 🏭 生产构建
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 分析构建包大小
npm run build && open dist/stats.html
```

## 🛠️ 技术栈详解

### 🔧 核心框架
| 技术 | 版本 | 用途 | 特性 |
|------|------|------|------|
| **Vue 3** | 3.5.13 | 前端框架 | Composition API, 响应式系统 |
| **Vite** | 6.2.4 | 构建工具 | 快速热重载, ES模块 |
| **Vue Router** | 4.5.0 | 路由管理 | 单页应用路由 |
| **Pinia** | 3.0.1 | 状态管理 | 类型安全, 开发工具支持 |

### 🎨 UI组件库
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **Element Plus** | 2.9.11 | UI组件库 | 企业级组件, 主题定制 |
| **@element-plus/icons-vue** | 2.3.1 | 图标库 | 丰富的图标资源 |
| **Sass** | 1.89.1 | CSS预处理器 | 样式编程, 变量支持 |

### 🌐 网络通信
| 技术 | 版本 | 用途 | 特性 |
|------|------|------|------|
| **Axios** | 1.9.0 | HTTP客户端 | 请求拦截, 响应处理 |
| **JWT-Decode** | 4.0.0 | JWT解析 | 令牌解码, 信息提取 |

### 📝 内容处理
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **Marked** | 15.0.12 | Markdown渲染 | 富文本内容展示 |
| **Pinia-Plugin-Persistedstate** | 4.3.0 | 状态持久化 | 本地存储集成 |

### 🔧 开发工具
| 技术 | 版本 | 用途 | 功能 |
|------|------|------|------|
| **ESLint** | 9.29.0 | 代码检查 | 语法检查, 风格统一 |
| **ESLint-Plugin-Vue** | 10.2.0 | Vue规则 | Vue特定检查 |
| **ESLint-Plugin-Unused-Imports** | 4.1.4 | 导入检查 | 未使用导入检测 |
| **Depcheck** | 1.4.7 | 依赖分析 | 依赖使用情况分析 |
| **Terser** | 5.42.0 | 代码压缩 | 生产环境优化 |
| **PurgeCSS** | 7.0.2 | CSS优化 | 未使用样式清理 |
| **Rollup-Plugin-Visualizer** | 6.0.3 | 构建分析 | 包大小可视化 |

## 🎯 核心功能模块

### 🔐 认证模块 (Auth)
```mermaid
graph LR
    A[登录页面] --> B[用户认证]
    B --> C[JWT令牌]
    C --> D[自动刷新]
    D --> E[权限验证]
    E --> F[路由守卫]
```

**核心特性：**
- 🔑 JWT令牌认证
- 🔄 自动令牌刷新
- 🛡️ 路由权限控制
- 💾 登录状态持久化

**关键文件：**
- `stores/authStore.js` - 认证状态管理
- `services/tokenService.js` - 令牌管理服务
- `services/tokenRefreshService.js` - 令牌刷新服务

### 💬 聊天模块 (Chat)
```mermaid
graph TD
    A[聊天界面] --> B[消息发送]
    B --> C[流式响应]
    C --> D[消息渲染]
    D --> E[历史记录]
    E --> F[对话管理]
```

**核心特性：**
- 💬 实时聊天对话
- 📝 Markdown消息渲染
- 📁 文件上传支持
- 💾 对话历史管理
- 🔄 流式响应处理

**关键文件：**
- `views/Chat.vue` - 聊天主页面
- `components/Chat/` - 聊天相关组件
- `stores/chatStore.js` - 聊天状态管理

### 🤖 智能体模块 (Agent)
```mermaid
graph LR
    A[智能体广场] --> B[智能体列表]
    B --> C[智能体详情]
    C --> D[智能体对话]
    D --> E[对话历史]
```

**核心特性：**
- 🏪 智能体市场展示
- 🔍 智能体搜索筛选
- 📋 智能体详情查看
- 💬 智能体专属对话

**关键文件：**
- `views/AgentPlaza.vue` - 智能体广场
- `views/AgentChat.vue` - 智能体聊天
- `stores/agentStore.js` - 智能体状态管理

### 🎨 UI组件系统
```mermaid
graph TB
    A[设计系统] --> B[基础组件]
    A --> C[业务组件]
    A --> D[布局组件]
    B --> E[按钮/表单]
    C --> F[聊天/智能体]
    D --> G[侧边栏/头部]
```

**核心特性：**
- 🎨 统一设计语言
- 🧩 可复用组件库
- 📱 响应式布局
- 🌙 主题切换支持

## ⚙️ 配置说明

### 🔧 环境变量配置 (.env)
```bash
# ===========================================
# 🔗 API服务配置
# ===========================================
VITE_API_BASE_URL=http://localhost:5000
VITE_API_TIMEOUT=30000

# ===========================================
# 🎨 应用配置
# ===========================================
VITE_APP_TITLE=普米智能体工作坊
VITE_APP_DESCRIPTION=企业级AI智能体平台
VITE_APP_VERSION=1.0.0

# ===========================================
# 🔐 认证配置
# ===========================================
VITE_TOKEN_STORAGE_KEY=pumi_access_token
VITE_REFRESH_TOKEN_STORAGE_KEY=pumi_refresh_token
VITE_TOKEN_REFRESH_THRESHOLD=300000  # 5分钟

# ===========================================
# 📁 文件上传配置
# ===========================================
VITE_MAX_FILE_SIZE=16777216  # 16MB
VITE_ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.md,.txt

# ===========================================
# 🎯 功能开关
# ===========================================
VITE_ENABLE_DEBUG_MODE=true
VITE_ENABLE_TOKEN_DEBUG=false
VITE_ENABLE_PERFORMANCE_MONITOR=true
```

### 🏗️ Vite构建配置
```javascript
// vite.config.js
export default defineConfig({
  plugins: [
    vue(),
    // 开发工具
    vueDevtools(),
    // 构建分析
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true
    })
  ],
  build: {
    // 代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          'element-plus': ['element-plus'],
          'vue-vendor': ['vue', 'vue-router', 'pinia']
        }
      }
    },
    // 代码压缩
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
})
```

## 🔧 开发指南

### 📝 代码规范

#### ESLint配置
```javascript
// eslint.config.js
export default [
  {
    files: ['**/*.{js,vue}'],
    plugins: {
      vue: pluginVue,
      'unused-imports': unusedImports
    },
    rules: {
      // Vue特定规则
      'vue/multi-word-component-names': 'off',
      'vue/no-unused-vars': 'error',

      // 导入规则
      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': 'warn'
    }
  }
]
```

#### 组件开发规范
```vue
<!-- 组件模板 -->
<template>
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script setup>
// 导入依赖
import { ref, computed, onMounted } from 'vue'
import { useStore } from '@/stores'

// 定义props
const props = defineProps({
  title: {
    type: String,
    required: true
  }
})

// 定义emits
const emit = defineEmits(['update', 'close'])

// 响应式数据
const isVisible = ref(false)

// 计算属性
const displayTitle = computed(() => {
  return props.title.toUpperCase()
})

// 生命周期
onMounted(() => {
  console.log('Component mounted')
})
</script>

<style scoped lang="scss">
.component-name {
  // 组件样式
}
</style>
```

### 🧪 测试与质量保证

#### 代码检查
```bash
# ESLint检查
npm run lint

# 自动修复
npm run lint:fix

# 依赖检查
npm run depcheck

# 类型检查
npm run type-check
```

#### 性能优化
```bash
# 构建分析
npm run build:analyze

# 包大小分析
npm run build && open dist/stats.html

# CSS优化
npm run build:css-purge
```

### 📊 状态管理最佳实践

#### Pinia Store结构
```javascript
// stores/exampleStore.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useExampleStore = defineStore('example', () => {
  // State
  const items = ref([])
  const loading = ref(false)

  // Getters
  const itemCount = computed(() => items.value.length)

  // Actions
  const fetchItems = async () => {
    loading.value = true
    try {
      const response = await api.getItems()
      items.value = response.data
    } catch (error) {
      console.error('Failed to fetch items:', error)
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    items,
    loading,
    // Getters
    itemCount,
    // Actions
    fetchItems
  }
}, {
  // 持久化配置
  persist: {
    key: 'example-store',
    storage: localStorage,
    paths: ['items']
  }
})
```

## 📚 相关文档

### 🔗 官方文档
- [Vue 3 官方文档](https://vuejs.org/)
- [Vite 官方文档](https://vitejs.dev/)
- [Element Plus 组件库](https://element-plus.org/)
- [Pinia 状态管理](https://pinia.vuejs.org/)

### 📖 学习资源
- [Vue 3 Composition API](https://vuejs.org/guide/extras/composition-api-faq.html)
- [Vue Router 4](https://router.vuejs.org/)
- [Sass 样式指南](https://sass-lang.com/guide)

## 🤝 贡献指南

### 🎯 开发流程
1. Fork 项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/new-feature`)
3. 开发并测试功能
4. 提交代码 (`git commit -m 'feat: add new feature'`)
5. 推送到分支 (`git push origin feature/new-feature`)
6. 创建 Pull Request

### ✅ 提交前检查清单
- [ ] 代码通过ESLint检查
- [ ] 组件功能正常
- [ ] 样式在不同设备上正常显示
- [ ] 没有控制台错误或警告
- [ ] 更新了相关文档

---

<div align="center">

**🌐 PUMI Agent Web 前端**

*现代化AI智能体平台前端应用*

[![⭐ Star](https://img.shields.io/github/stars/your-org/pumi-agent?style=social)](https://github.com/your-org/pumi-agent)
[![📄 License](https://img.shields.io/github/license/your-org/pumi-agent)](https://github.com/your-org/pumi-agent/blob/main/LICENSE)

</div>
