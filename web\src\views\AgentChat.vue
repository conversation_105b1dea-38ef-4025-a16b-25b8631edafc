<template>
  <div class="agent-chat-container">
    <!-- 移除原有的表单状态提示区域，改为集成到输入框 -->

    <!-- 聊天消息区域 -->
    <div class="message-area">
      <ChatMessages :messages="messages" />
    </div>

    <!-- 输入框区域 - 修改为固定在底部 -->
    <div class="input-wrapper">
      <MessageInput
        @send="sendMessage"
        :disabled="isLoading || !isFormCompleted"
        :agentMode="true"
        :placeholder="getInputPlaceholder()"
        :formStatus="getFormStatus()"
        :fileUploadConfig="agentParameters?.file_upload"
        @openForm="showInputForm = true"
      />
    </div>

    <!-- 智能体输入表单弹窗 -->
    <AgentInputForm
      v-model="showInputForm"
      :userInputForm="agentParameters?.user_input_form || []"
      :agentName="agent.name || '智能体'"
      :agentId="agentId"
      @submit="handleFormSubmit"
      @cancel="handleFormCancel"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ChatMessages from '@/components/Chat/ChatMessages.vue';
import MessageInput from '@/components/Chat/MessageInput.vue';
import AgentInputForm from '@/components/Agent/AgentInputForm.vue';
import { useApiStore } from '@/stores/apiStore';
import { useAuthStore } from '@/stores/authStore';
import { ElMessage } from 'element-plus';
// 移除不再需要的图标导入

const route = useRoute();
const router = useRouter();
const apiStore = useApiStore();
const authStore = useAuthStore();
const agentId = computed(() => route.params.agentId);
const messages = ref([]);
const conversationId = ref(''); // 保存会话ID
const isLoading = ref(false); // 加载状态

// 智能体数据对象
const agent = reactive({});

// 智能体参数和表单相关状态
const agentParameters = ref(null); // 智能体参数信息
const showInputForm = ref(false); // 是否显示输入表单
const formData = ref({}); // 表单数据
const isFormCompleted = ref(false); // 表单是否已完成

// 发送消息
const sendMessage = async (text) => {
  if (!text.trim() || isLoading.value) return;

  // 检查表单是否已完成
  if (!isFormCompleted.value) {
    ElMessage.warning('请先完成必要的表单填写');
    return;
  }

  isLoading.value = true;
  
  // 添加用户消息
  const userMessage = {
    id: `user-${Date.now()}`,
    role: 'user',
    content: text
  };
  messages.value.push(userMessage);
  
  // 添加加载中的AI消息
  const aiMessageId = `assistant-${Date.now()}`;
  const loadingMessage = {
    id: aiMessageId,
    role: 'assistant',
    content: '思考中...',
    loading: true
  };
  messages.value.push(loadingMessage);
  
  try {
    // 查找消息索引位置
    const messageIndex = messages.value.findIndex(msg => msg.id === aiMessageId);
    
    // 使用普通的fetch API来处理流式响应，避免使用可能未正确加载的方法
    const response = await fetch('/api/agent/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.access_token}`
      },
      body: JSON.stringify({
        agent_id: agentId.value,
        query: text,
        response_mode: 'streaming',
        conversation_id: conversationId.value,
        inputs: formData.value || {},
        files: []
      })
    });
    
    if (!response.ok) {
      throw new Error(`请求失败: ${response.status}`);
    }
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    let fullResponse = '';
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n');
      
      for (const line of lines) {
        if (line.trim() === '') continue;
        if (!line.startsWith('data: ')) continue;
        
        const json = line.slice(6); // 移除 'data: ' 前缀
        if (json === '[DONE]') continue;
        
        try {
          const data = JSON.parse(json);
          
          // 处理错误情况
          if (data.error) {
            if (messageIndex !== -1) {
              messages.value[messageIndex] = {
                ...messages.value[messageIndex],
                content: `❌ ${data.error || data.message || '智能体响应失败'}`,
                loading: false,
                error: true
              };
            }
            ElMessage.error(data.error || data.message || '智能体响应失败');
            return;
          }
          
          // 正常数据处理
          if (data.conversation_id && !conversationId.value) {
            // 存储会话ID
            conversationId.value = data.conversation_id;
          }
          
          // 累加响应内容 - 处理多种可能的响应字段
          let hasContent = false;
          
          // 处理content字段（普通聊天API格式）
          if (data.content !== undefined) {
            fullResponse += data.content;
            hasContent = true;
          }
          
          // 处理response字段（智能体API格式）
          if (data.response !== undefined) {
            fullResponse += data.response;
            hasContent = true;
          }

          // 处理message字段（某些API可能返回的格式）
          if (!hasContent && data.message && typeof data.message === 'string') {
            fullResponse += data.message;
            hasContent = true;
          }
          
          // 处理text字段（某些API可能返回的格式）
          if (!hasContent && data.text) {
            fullResponse += data.text;
            hasContent = true;
          }
          
          // 如果找到了任何有效内容，更新消息
          if (hasContent) {
            // 更新消息内容
            if (messageIndex !== -1) {
              messages.value[messageIndex] = {
                ...messages.value[messageIndex],
                content: fullResponse,
                loading: false
              };
            }
          } else {
            // 紧急回退：如果没有找到任何标准格式内容，尝试直接使用整个JSON数据
            
            // 转换为友好的文本格式
            let fallbackContent = '';
            try {
              // 尝试提取可能的有用信息
              if (typeof data === 'object') {
                // 尝试各种可能的字段
                const possibleFields = ['text', 'message', 'answer', 'result', 'output', 'generated_text'];
                for (const field of possibleFields) {
                  if (data[field] && typeof data[field] === 'string') {
                    fallbackContent = data[field];
                    break;
                  }
                }
                
                // 如果没找到任何标准字段，使用整个JSON
                if (!fallbackContent) {
                  fallbackContent = JSON.stringify(data, null, 2);
                }
              } else {
                fallbackContent = String(data);
              }
              
              // 更新消息
              if (messageIndex !== -1) {
                messages.value[messageIndex] = {
                  ...messages.value[messageIndex],
                  content: fullResponse || fallbackContent,
                  loading: false,
                  contentType: fullResponse ? 'text' : 'json'
                };
              }
            } catch (e) {
              // 解析响应数据失败
            }
          }
        } catch (e) {
          // 解析响应数据失败
        }
      }
    }
  } catch (error) {
    // 发送消息失败
    
    // 找到并更新加载中的消息为错误状态
    const index = messages.value.findIndex(msg => msg.id === aiMessageId);
    if (index !== -1) {
      messages.value[index] = {
        id: aiMessageId,
        role: 'assistant',
        content: `发送消息失败: ${error.message || '未知错误'}`,
        loading: false,
        error: true
      };
    }
    
    ElMessage.error('发送消息失败，请稍后重试');
  } finally {
    isLoading.value = false;
  }
};

// 加载智能体数据
const loadAgentData = async () => {
  if (!agentId.value) return;

  try {
    // 调用API获取智能体的详细信息
    const agentData = await apiStore.getAgentDetail(agentId.value);

    if (agentData) {
      // 更新本地agent对象
      agent.id = agentData.id;
      agent.name = agentData.name;
      agent.description = agentData.description || '暂无描述';
      agent.icon_type = agentData.icon_type;
      agent.icon = agentData.icon;
      agent.icon_url = agentData.icon_url;
      // opening_statement 在 parameters 接口中获取，这里不设置
      agent.tags = agentData.tags || [];
    }

    // 获取智能体参数信息
    await loadAgentParameters();

    // 在获取参数后检查是否显示开场白
    showOpeningStatementIfNeeded();
  } catch (error) {
    // 获取智能体数据失败
    ElMessage.error('获取智能体信息失败');
  }
};

// 加载智能体参数信息
const loadAgentParameters = async () => {
  if (!agentId.value) return;

  try {
    const parameters = await apiStore.getAgentParameters(agentId.value);
    agentParameters.value = parameters;

    // 设置开场白（从parameters接口获取）
    if (parameters && parameters.opening_statement) {
      agent.opening_statement = parameters.opening_statement;
    }

    // 检查是否有user_input_form需要填写
    if (parameters && parameters.user_input_form && parameters.user_input_form.length > 0) {
      // 每次刷新页面都重新填写表单，不保留缓存
      showInputForm.value = true;
      isFormCompleted.value = false;
    } else {
      // 没有表单要求，可以直接对话
      isFormCompleted.value = true;
    }
  } catch (error) {
    console.error('获取智能体参数失败', error);
    // 参数获取失败不影响基本对话功能
    isFormCompleted.value = true;
  }
};

// 处理表单提交
const handleFormSubmit = (data) => {
  formData.value = data;
  isFormCompleted.value = true;
  showInputForm.value = false;
  // 开场白已经在初始加载时显示，这里不需要再显示
};

// 处理表单取消
const handleFormCancel = () => {
  showInputForm.value = false;
  // 静默取消，不显示提示消息
};

// 获取输入框placeholder
const getInputPlaceholder = () => {
  if (!agentParameters.value?.user_input_form?.length) {
    return '发消息、选择文件上传';
  }

  if (!isFormCompleted.value) {
    return '请先完成表单填写后开始对话';
  }

  return '发消息、选择文件上传';
};

// 获取表单状态信息
const getFormStatus = () => {
  if (!agentParameters.value?.user_input_form?.length) {
    return null; // 没有表单要求
  }

  return {
    completed: isFormCompleted.value,
    hasForm: true
  };
};

// 检查并显示开场白
const showOpeningStatementIfNeeded = () => {
  // 只有在有开场白且当前没有消息时才显示
  if (!agent.opening_statement || messages.value.length > 0) {
    return;
  }

  // 无论是否有表单，都直接显示开场白
  const openingMessage = {
    id: `opening-${Date.now()}`,
    role: 'assistant',
    content: agent.opening_statement
  };
  messages.value.push(openingMessage);
};

onMounted(() => {
  loadAgentData();
});

// 监听路由参数变化
watch(
  () => route.params.agentId,
  (newAgentId, oldAgentId) => {
    if (newAgentId && newAgentId !== oldAgentId) {
      // 清空聊天记录和会话ID
      messages.value = [];
      conversationId.value = '';

      // 重置表单相关状态
      agentParameters.value = null;
      showInputForm.value = false;
      formData.value = {};
      isFormCompleted.value = false;

      // 重新加载智能体数据
      loadAgentData();
    }
  }
);
</script>

<style scoped lang="scss">
.agent-chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative; /* 保持相对定位 */
  padding-bottom: 0; /* 移除可能的额外填充 */

  /* 覆盖父容器的居中样式 */
  justify-self: flex-start;
  align-self: flex-start;
}

// 移除原有的表单状态栏样式，现在集成到输入框中

.message-area {
  flex: 1;
  width: 100%;
  overflow-y: visible;
  display: flex;
  justify-content: center;
  align-items: flex-start; /* 让内容从顶部开始显示 */
  /* 减少底部填充,为固定的输入框留出合适空间 */
  padding-bottom: 60px;
  padding-top: 60px; /* 增加顶部间距，让消息位置更合适 */

  /* 调整ChatMessages组件的样式 */
  :deep(.bubble-messages) {
    margin-top: 0 !important; /* 移除顶部外边距 */
    padding-top: 0 !important; /* 移除顶部内边距 */
    min-height: auto !important; /* 移除最小高度限制 */
    align-self: flex-start !important; /* 让消息容器从顶部开始 */
    margin-bottom: 20px !important; /* 减少底部边距 */
    padding-bottom: 120px !important; /* 为固定输入框留出足够空间 */
  }
}

.input-wrapper {
  position: fixed;
  bottom: 20px; /* 减小与底部的距离 */
  left: calc(50vw + 125px); /* 与普通对话相同的左侧偏移,计算方式考虑了侧边栏的宽度 */
  transform: translateX(-50%); /* 使元素居中 */
  width: auto; /* 改为自适应宽度 */
  max-width: 750px; /* 与普通对话保持相同的最大宽度 */
  display: flex;
  justify-content: center;
  background: transparent; /* 保持透明背景 */
  border-top: none; /* 保持无边框 */
  padding: 0; /* 移除内边距,使用组件自身的内边距 */
  box-shadow: none; /* 保持无阴影 */
  z-index: 100;
}

/* 全局样式覆盖 - 让智能体聊天页面的内容从顶部开始 */
:deep(.main-center) {
  justify-content: flex-start !important;
  align-items: flex-start !important;
  padding-top: 0 !important;
}

@media (max-width: 768px) {
  .input-wrapper {
    padding: 0;
    bottom: 10px; /* 移动设备上减小底部距离 */
    left: 50%; /* 在移动设备上居中 */
    width: calc(100% - 32px); /* 移动设备上控制宽度 */
  }
}
</style> 