<template>
  <div class="streaming-response">
    <div v-if="content" class="response-content" v-html="formattedContent"></div>
    <div v-else class="loading-indicator">
      <span class="dot"></span>
      <span class="dot"></span>
      <span class="dot"></span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { marked } from 'marked'

// 定义props
const props = defineProps({
  content: {
    type: String,
    default: ''
  },
  isLoading: {
    type: Boolean,
    default: false
  }
})

// 处理Markdown格式化
const formattedContent = computed(() => {
  if (!props.content) return ''
  
  try {
    // 使用marked库解析Markdown内容为HTML
    return marked(props.content, { 
      breaks: true,
      gfm: true
    })
  } catch (error) {
    console.error('Markdown解析错误:', error)
    return props.content
  }
})
</script>

<style scoped>
.streaming-response {
  width: 100%;
  padding: 8px 0;
  background: #f0f8ff;
  border-radius: 18px;
  border-top-left-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 112, 243, 0.06);
  color: #2c3e50;
}

.response-content {
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 32px;
  padding: 10px 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;
  letter-spacing: 0.3px;
  font-size: 15px;
}

.response-content :deep(pre) {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 12px 16px;
  overflow-x: auto;
  margin: 8px 0;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.response-content :deep(code) {
  font-family: "SF Mono", "Cascadia Code", "JetBrains Mono", Menlo, Monaco, Consolas, monospace;
  font-size: 0.92em;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 24px;
  padding: 0 16px;
}

.dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  margin-right: 4px;
  background-color: #3b82f6;
  border-radius: 50%;
  animation: dot-pulse 1.5s infinite ease-in-out;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dot-pulse {
  0%, 80%, 100% { 
    transform: scale(0.4);
    opacity: 0.4;
  }
  40% { 
    transform: scale(1);
    opacity: 1;
  }
}
</style> 