<template>
  <el-dialog
    v-model="visible"
    width="480px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    class="agent-form-dialog"
    align-center
  >
    <!-- 自定义标题 -->
    <template #header>
      <div class="dialog-header">
        <div class="agent-info">
          <div class="agent-avatar">
            <el-icon size="24"><User /></el-icon>
          </div>
          <div class="agent-details">
            <h3 class="agent-name">{{ agentName }}</h3>
            <p class="agent-subtitle">请填写以下信息以开始对话</p>
          </div>
        </div>
      </div>
    </template>
    <div class="form-container">
      <div class="form-intro">
        <el-icon class="intro-icon"><InfoFilled /></el-icon>
        <span>为了提供更好的服务，请填写以下必要信息</span>
      </div>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="0"
        label-position="top"
        class="modern-form"
      >
        <!-- 动态渲染表单字段 -->
        <div v-for="(field, index) in userInputForm" :key="index" class="form-field">
          <!-- 文件上传字段 -->
          <div v-if="field.file" class="file-field">
            <div class="field-header">
              <label class="field-label">
                {{ field.file.label }}
                <span v-if="field.file.required" class="required-mark">*</span>
              </label>
              <div class="field-description">
                <span v-if="field.file.allowed_file_extensions?.length > 0">
                  支持格式：{{ field.file.allowed_file_extensions.join(', ') }}
                </span>
                <span v-if="shouldShowFileTypes(field.file.allowed_file_types)">
                  <span v-if="field.file.allowed_file_extensions?.length > 0"> </span>支持类型：{{ getDisplayFileTypes(field.file.allowed_file_types).join(', ') }}
                </span>
                <span v-if="field.file.type === 'file-list' && field.file.max_length">
                  ，最多 {{ field.file.max_length }} 个文件
                </span>
              </div>
            </div>
            <el-form-item
              :prop="field.file.variable"
              :required="field.file.required"
            >
              <el-upload
                v-model:file-list="fileList[field.file.variable]"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :data="{ agent_id: agentId }"
                :accept="getAcceptTypes(field.file.allowed_file_extensions, field.file.allowed_file_types)"
                :limit="field.file.type === 'file-list' ? field.file.max_length : undefined"
                :on-success="(response, file) => handleFileSuccess(response, file, field.file.variable)"
                :on-error="handleFileError"
                :on-exceed="(files, fileList) => handleFileExceed(files, fileList, field.file)"
                :before-upload="(file) => beforeFileUpload(file, field.file)"
                multiple
                class="apple-upload"
              >
                <div class="upload-area">
                  <el-icon class="upload-icon"><UploadFilled /></el-icon>
                  <span class="upload-text">从本地上传</span>
                </div>
              </el-upload>
            </el-form-item>
          </div>

          <!-- 文本输入字段 -->
          <div v-else-if="field.text" class="text-field">
            <div class="field-header">
              <label class="field-label">
                {{ field.text.label }}
                <span v-if="field.text.required" class="required-mark">*</span>
              </label>
            </div>
            <el-form-item
              :prop="field.text.variable"
              :required="field.text.required"
            >
              <el-input
                v-model="formData[field.text.variable]"
                :placeholder="field.text.placeholder || `请输入${field.text.label}`"
                :maxlength="field.text.max_length"
                show-word-limit
                class="modern-input"
              />
            </el-form-item>
          </div>

          <!-- 选择字段 -->
          <div v-else-if="field.select" class="select-field">
            <div class="field-header">
              <label class="field-label">
                {{ field.select.label }}
                <span v-if="field.select.required" class="required-mark">*</span>
              </label>
            </div>
            <el-form-item
              :prop="field.select.variable"
              :required="field.select.required"
            >
              <el-select
                v-model="formData[field.select.variable]"
                :placeholder="`请选择${field.select.label}`"
                style="width: 100%"
                class="modern-select"
              >
                <el-option
                  v-for="option in field.select.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" size="large" class="cancel-btn">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting" size="large" class="submit-btn">
          <el-icon v-if="!submitting"><Check /></el-icon>
          确认并开始对话
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, User, InfoFilled, Close, Check } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/authStore'
import { useApiConfigStore } from '@/stores/apiConfig'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  userInputForm: {
    type: Array,
    default: () => []
  },
  agentName: {
    type: String,
    default: '智能体'
  },
  agentId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'submit', 'cancel'])

const authStore = useAuthStore()
const apiConfig = useApiConfigStore()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref()
const formData = reactive({})
const fileList = reactive({})
const submitting = ref(false)

// 上传配置 - 使用智能体专用上传接口
const uploadUrl = computed(() => `${apiConfig.baseUrl}/agent/upload`)
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${authStore.access_token}`
}))
const userId = computed(() => authStore.userInfo?.id || 'anonymous')
const agentId = computed(() => props.agentId)

// 表单验证规则
const formRules = computed(() => {
  const rules = {}
  props.userInputForm.forEach(field => {
    const fieldConfig = field.file || field.text || field.select
    if (fieldConfig && fieldConfig.required) {
      rules[fieldConfig.variable] = [
        { required: true, message: `请填写${fieldConfig.label}`, trigger: 'blur' }
      ]
    }
  })
  return rules
})

// 初始化表单数据
const initFormData = () => {
  props.userInputForm.forEach(field => {
    const fieldConfig = field.file || field.text || field.select
    if (fieldConfig) {
      if (field.file) {
        // 文件字段初始化为空数组
        formData[fieldConfig.variable] = []
        fileList[fieldConfig.variable] = []
      } else {
        // 其他字段初始化为空字符串
        formData[fieldConfig.variable] = fieldConfig.default || ''
      }
    }
  })
}

// 监听表单配置变化，重新初始化
watch(() => props.userInputForm, initFormData, { immediate: true })

// 判断是否应该显示文件类型
const shouldShowFileTypes = (fileTypes) => {
  if (!fileTypes || fileTypes.length === 0) return false
  // 如果只包含 custom 类型，则不显示
  if (fileTypes.length === 1 && fileTypes[0] === 'custom') return false
  // 如果包含 custom 但还有其他类型，则显示其他类型
  return fileTypes.some(type => type !== 'custom')
}

// 获取要显示的文件类型（过滤掉 custom）
const getDisplayFileTypes = (fileTypes) => {
  if (!fileTypes) return []
  return fileTypes.filter(type => type !== 'custom')
}

// 文件上传相关方法
const getAcceptTypes = (extensions, types) => {
  const acceptList = []

  // 添加文件扩展名
  if (extensions && extensions.length > 0) {
    acceptList.push(...extensions)
  }

  // 添加文件类型（如 image/*）
  if (types && types.length > 0) {
    const typeMap = {
      'image': 'image/*',
      'video': 'video/*',
      'audio': 'audio/*',
      'document': '.pdf,.doc,.docx,.txt,.rtf',
      'spreadsheet': '.xls,.xlsx,.csv',
      'presentation': '.ppt,.pptx'
    }

    types.forEach(type => {
      if (typeMap[type]) {
        acceptList.push(typeMap[type])
      }
    })
  }

  return acceptList.join(',')
}

const handleFileSuccess = (response, file, variable) => {
  console.log('文件上传响应:', response) // 调试日志

  if (response && response.code === 200 && response.data) {
    // 找到对应的字段配置，获取allowed_file_types
    const fieldConfig = props.userInputForm.find(field =>
      field.file && field.file.variable === variable
    )

    // 获取文件类型，优先使用配置中的第一个类型，默认为"custom"
    const fileType = fieldConfig?.file?.allowed_file_types?.[0] || "custom"

    // 根据Dify格式要求，文件字段应该是单个对象
    formData[variable] = {
      type: fileType,
      transfer_method: "local_file",
      url: "",
      upload_file_id: response.data.id
    }

    // 静默上传，不显示成功消息
  } else {
    console.error('文件上传失败，响应:', response)
    ElMessage.error(`${file.name} 上传失败: ${response?.message || '未知错误'}`)
  }
}

const handleFileError = (error, file) => {
  ElMessage.error(`${file.name} 上传失败: ${error.message || '未知错误'}`)
}

const handleFileExceed = (files, fileList, fieldConfig) => {
  if (fieldConfig.type === 'file-list' && fieldConfig.max_length) {
    ElMessage.warning(`最多只能上传 ${fieldConfig.max_length} 个文件`)
  } else {
    ElMessage.warning('文件数量超出限制')
  }
}

const beforeFileUpload = (file, fieldConfig) => {
  // 检查文件扩展名
  if (fieldConfig.allowed_file_extensions && fieldConfig.allowed_file_extensions.length > 0) {
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
    if (!fieldConfig.allowed_file_extensions.includes(fileExtension)) {
      ElMessage.error(`不支持的文件格式，请上传 ${fieldConfig.allowed_file_extensions.join(', ')} 格式的文件`)
      return false
    }
  }

  // 检查文件类型（忽略 custom 类型）
  if (fieldConfig.allowed_file_types && fieldConfig.allowed_file_types.length > 0) {
    const validTypes = fieldConfig.allowed_file_types.filter(type => type !== 'custom')

    if (validTypes.length > 0) {
      const fileType = file.type
      let isValidType = false

      validTypes.forEach(type => {
        switch (type) {
          case 'image':
            if (fileType.startsWith('image/')) isValidType = true
            break
          case 'video':
            if (fileType.startsWith('video/')) isValidType = true
            break
          case 'audio':
            if (fileType.startsWith('audio/')) isValidType = true
            break
          case 'document':
            if (fileType.includes('pdf') || fileType.includes('document') || fileType.includes('text')) isValidType = true
            break
        }
      })

      if (!isValidType) {
        ElMessage.error(`不支持的文件类型，请上传 ${validTypes.join(', ')} 类型的文件`)
        return false
      }
    }
  }

  // 检查文件大小（这里可以根据需要添加）
  return true
}

// 表单提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 不再保存到localStorage，刷新页面后重新填写
    
    // 触发提交事件
    emit('submit', { ...formData })

    // 静默提交，不显示成功消息
    visible.value = false
  } catch (error) {
    ElMessage.error('请完善表单信息')
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
  visible.value = false
}
</script>

<style scoped lang="scss">
// 苹果风格设计 - 更紧凑的弹窗
.agent-form-dialog {
  :deep(.el-dialog) {
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  }

  :deep(.el-dialog__header) {
    padding: 0;
    border-bottom: none;
  }

  :deep(.el-dialog__body) {
    padding: 0 20px 20px;
  }

  :deep(.el-dialog__footer) {
    padding: 0 20px 20px;
    border-top: 1px solid #f0f0f0;
    margin-top: 20px;
    padding-top: 20px;
  }
}

.dialog-header {
  padding: 20px 20px 16px;
  background: #4f7cff;
  border-radius: 20px 20px 0 0;

  .agent-info {
    display: flex;
    align-items: center;
    gap: 16px;

    .agent-avatar {
      width: 48px;
      height: 48px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }

    .agent-details {
      flex: 1;

      .agent-name {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: white;
        line-height: 1.2;
      }

      .agent-subtitle {
        margin: 4px 0 0;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.4;
      }
    }
  }
}

.form-container {
  max-height: 50vh;
  overflow-y: auto;

  .form-intro {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #f0f7ff;
    border-radius: 12px;
    margin-bottom: 20px;
    border: 1px solid #d1e7ff;

    .intro-icon {
      color: #4f7cff;
      font-size: 16px;
    }

    span {
      color: #4a5568;
      font-size: 14px;
    }
  }
}

.modern-form {
  .form-field {
    margin-bottom: 20px;

    .field-header {
      margin-bottom: 8px;

      .field-label {
        font-size: 14px;
        font-weight: 500;
        color: #2d3748;
        display: block;

        .required-mark {
          color: #e53e3e;
          margin-left: 2px;
        }
      }

      .field-description {
        font-size: 12px;
        color: #718096;
        margin-top: 4px;
      }
    }
  }
}

// 苹果风格文件上传 - 大气简洁
.file-field {
  .apple-upload {
    width: 100%;

    :deep(.el-upload) {
      width: 100%;
      display: block;
    }

    .upload-area {
      width: 100%;
      min-width: 300px;
      height: 60px;
      border: 2px dashed #d1d5db;
      border-radius: 12px;
      background: #fafbfc;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #4f7cff;
        background: #f8faff;
      }

      .upload-icon {
        font-size: 20px;
        color: #9ca3af;
      }

      .upload-text {
        font-size: 15px;
        color: #6b7280;
        font-weight: 500;
      }
    }

    &:hover .upload-area {
      .upload-icon {
        color: #4f7cff;
      }

      .upload-text {
        color: #4f7cff;
      }
    }
  }
}

// 输入框样式
.modern-input {
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;

    &:hover {
      border-color: #4f7cff;
    }

    &.is-focus {
      border-color: #4f7cff;
      box-shadow: 0 0 0 3px rgba(79, 124, 255, 0.1);
    }
  }
}

.modern-select {
  :deep(.el-select__wrapper) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;

    &:hover {
      border-color: #4f7cff;
    }

    &.is-focus {
      border-color: #4f7cff;
      box-shadow: 0 0 0 3px rgba(79, 124, 255, 0.1);
    }
  }
}

// 底部按钮样式 - 参考UI中的蓝色按钮
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .cancel-btn {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    color: #6b7280;

    &:hover {
      border-color: #9ca3af;
      color: #374151;
    }
  }

  .submit-btn {
    border-radius: 8px;
    background: #4f7cff;
    border: none;
    font-weight: 500;

    &:hover {
      background: #3b6bff;
    }
  }
}

// 表单项样式重置
:deep(.el-form-item) {
  margin-bottom: 0;

  .el-form-item__label {
    display: none; // 使用自定义标签
  }

  .el-form-item__content {
    line-height: normal;
  }
}
</style>
