"""聊天API请求模型"""
from flask_restx import fields, Namespace

# 创建一个临时命名空间用于定义模型
_temp_ns = Namespace('chat_schemas')

# 创建消息请求模型
message_create_model = _temp_ns.model('MessageCreate', {
    'query': fields.String(required=True, description="用户消息内容"),
    'response_mode': fields.String(
        required=True, 
        default="streaming", 
        description="响应模式，normal为普通响应，streaming为流式响应",
        enum=["normal", "streaming"]
    ),
    'conversation_id': fields.String(
        required=False, 
        description="对话ID，如果为空则创建新对话"
    ),
    'model_name': fields.String(
        required=True, 
        description="模型名称"
    ),
    'enable_thinking': fields.Boolean(
        required=False, 
        default=False, 
        description="是否启用思考过程（仅部分模型支持）"
    ),
    'file_ids': fields.List(
        fields.String,
        required=False,
        description="要与对话关联的文件ID列表，仅在创建新对话或首次关联文件时使用"
    )
})

# 获取对话历史请求模型
get_history_request_model = _temp_ns.model('GetHistoryRequest', {
    'conversation_id': fields.String(required=True, description="对话ID")
})

# 获取可用模型列表请求模型
get_models_request_model = _temp_ns.model('GetModelsRequest', {}) 