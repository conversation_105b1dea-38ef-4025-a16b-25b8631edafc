"""Add icon_type and icon_image_url fields to AIGC tools

Revision ID: 0c52da0764be
Revises: 829e2f029216
Create Date: 2025-07-22 18:31:45.081574

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0c52da0764be'
down_revision = '829e2f029216'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('aigc_tools', schema=None) as batch_op:
        batch_op.add_column(sa.Column('icon_type', sa.String(length=10), nullable=False, comment='图标类型(emoji|image)'))
        batch_op.add_column(sa.Column('icon_image_url', sa.String(length=500), nullable=True, comment='图片图标URL'))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('aigc_tools', schema=None) as batch_op:
        batch_op.drop_column('icon_image_url')
        batch_op.drop_column('icon_type')

    # ### end Alembic commands ###
