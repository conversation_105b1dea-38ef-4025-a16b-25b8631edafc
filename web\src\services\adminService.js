/**
 * 管理员API服务
 * 提供管理员仪表盘相关的API调用
 */

import { useApiConfigStore } from '@/stores/apiConfig'
import { useUserStore } from '@/stores/userStore'
import { ref, readonly } from 'vue'
import axios from 'axios'

// 获取API配置
const apiConfig = useApiConfigStore()

/**
 * 创建带认证的axios实例
 */
function createAuthenticatedRequest() {
  // 从localStorage直接获取token，避免store初始化问题
  const token = localStorage.getItem('access_token')

  if (!token) {
    throw new Error('需要管理员权限，请先登录')
  }

  return axios.create({
    baseURL: apiConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    timeout: 10000
  })
}

/**
 * 处理API响应
 */
function handleResponse(response) {
  if (response.data && response.data.code === 200) {
    return response.data.data
  }
  throw new Error(response.data?.message || '请求失败')
}

/**
 * 处理API错误
 */
function handleError(error) {
  if (error.response) {
    const { status, data } = error.response
    
    if (status === 403) {
      throw new Error('权限不足，需要管理员权限')
    } else if (status === 401) {
      throw new Error('认证失败，请重新登录')
    } else if (data?.message) {
      throw new Error(data.message)
    }
  }
  
  throw new Error(error.message || '网络请求失败')
}

/**
 * 管理员API服务类
 */
export class AdminService {
  
  /**
   * 获取系统监控数据
   */
  static async getSystemMonitor() {
    try {
      const request = createAuthenticatedRequest()
      const response = await request.get(apiConfig.endpoints.admin.system_monitor)
      return handleResponse(response)
    } catch (error) {
      handleError(error)
    }
  }
  
  /**
   * 获取系统健康状态
   */
  static async getSystemHealth() {
    try {
      const request = createAuthenticatedRequest()
      const response = await request.get(apiConfig.endpoints.admin.system_health)
      return handleResponse(response)
    } catch (error) {
      handleError(error)
    }
  }
  
  /**
   * 获取仪表盘概览数据
   */
  static async getDashboardOverview() {
    try {
      const request = createAuthenticatedRequest()
      const response = await request.get(apiConfig.endpoints.admin.dashboard_overview)
      return handleResponse(response)
    } catch (error) {
      handleError(error)
    }
  }
  
  /**
   * 获取图表数据
   * @param {string} timeRange - 时间范围 (today/week/month)
   */
  static async getDashboardCharts(timeRange = 'today') {
    try {
      const request = createAuthenticatedRequest()
      const response = await request.get(apiConfig.endpoints.admin.dashboard_charts, {
        params: { time_range: timeRange }
      })
      return handleResponse(response)
    } catch (error) {
      handleError(error)
    }
  }
  
  /**
   * 获取缓存信息
   */
  static async getCacheInfo() {
    try {
      const request = createAuthenticatedRequest()
      const response = await request.get(apiConfig.endpoints.admin.cache_info)
      return handleResponse(response)
    } catch (error) {
      handleError(error)
    }
  }
  
  /**
   * 清空缓存
   */
  static async clearCache() {
    try {
      const request = createAuthenticatedRequest()
      const response = await request.post(apiConfig.endpoints.admin.cache_clear)
      return handleResponse(response)
    } catch (error) {
      handleError(error)
    }
  }
}

/**
 * 管理员数据获取Hook
 * 提供响应式的数据获取和状态管理
 */
export function useAdminData() {
  const loading = ref(false)
  const error = ref(null)
  
  /**
   * 执行API请求的通用方法
   */
  const executeRequest = async (apiCall) => {
    loading.value = true
    error.value = null
    
    try {
      const result = await apiCall()
      return result
    } catch (err) {
      error.value = err.message
      console.error('Admin API Error:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  return {
    loading: readonly(loading),
    error: readonly(error),
    
    // 系统监控相关
    getSystemMonitor: () => executeRequest(() => AdminService.getSystemMonitor()),
    getSystemHealth: () => executeRequest(() => AdminService.getSystemHealth()),
    
    // 仪表盘数据相关
    getDashboardOverview: () => executeRequest(() => AdminService.getDashboardOverview()),
    getDashboardCharts: (timeRange) => executeRequest(() => AdminService.getDashboardCharts(timeRange)),
    
    // 缓存管理相关
    getCacheInfo: () => executeRequest(() => AdminService.getCacheInfo()),
    clearCache: () => executeRequest(() => AdminService.clearCache())
  }
}

// 导出默认服务
export default AdminService
