/**
 * 模型管理相关的API服务
 */
import axios from 'axios'
import { useApiConfigStore } from '@/stores/apiConfig'

const apiConfig = useApiConfigStore()

/**
 * 获取所有启用的模型列表
 * @returns {Promise} 模型列表
 */
export const getModelList = async () => {
  try {
    const response = await axios.get(apiConfig.getNestUrl('channels', 'get_model_list'))
    return response.data
  } catch (error) {
    console.error('获取模型列表失败:', error)
    throw error
  }
}

/**
 * 获取指定渠道的模型列表
 * @param {number} channelId 渠道ID
 * @returns {Promise} 渠道模型列表
 */
export const getChannelModelList = async (channelId) => {
  try {
    const response = await axios.post(apiConfig.getNestUrl('channels', 'get_channel_model_list'), {
      channel_id: channelId
    })
    return response.data
  } catch (error) {
    console.error('获取渠道模型列表失败:', error)
    throw error
  }
}

/**
 * 切换模型启用/禁用状态
 * @param {number} channelId 渠道ID
 * @param {string} modelName 模型名称
 * @returns {Promise} 操作结果
 */
export const changeModelStatus = async (channelId, modelName) => {
  try {
    const response = await axios.post(apiConfig.getNestUrl('channels', 'change_model_status'), {
      channel_id: channelId,
      model_name: modelName
    })
    return response.data
  } catch (error) {
    console.error('切换模型状态失败:', error)
    throw error
  }
}

/**
 * 添加模型到渠道
 * @param {number} channelId 渠道ID
 * @param {Array<string>} modelNames 模型名称列表
 * @returns {Promise} 添加结果
 */
export const addModels = async (channelId, modelNames) => {
  try {
    const response = await axios.post(apiConfig.getNestUrl('channels', 'add_model'), {
      channel_id: channelId,
      models_name: modelNames
    })
    return response.data
  } catch (error) {
    console.error('添加模型失败:', error)
    throw error
  }
}

/**
 * 获取渠道列表
 * @returns {Promise} 渠道列表
 */
export const getChannelList = async () => {
  try {
    const response = await axios.get(apiConfig.getNestUrl('channels', 'get_channel_list'))
    return response.data
  } catch (error) {
    console.error('获取渠道列表失败:', error)
    throw error
  }
}

/**
 * 获取模型统计信息
 * @returns {Promise} 统计信息
 */
export const getModelStats = async () => {
  try {
    // 获取所有模型
    const models = await getModelList()
    
    // 获取渠道列表
    const channels = await getChannelList()
    
    // 计算统计信息
    const stats = {
      totalModels: models.length,
      enabledModels: models.filter(m => m.model_enabled).length,
      disabledModels: models.filter(m => !m.model_enabled).length,
      totalChannels: channels.length
    }
    
    return stats
  } catch (error) {
    console.error('获取模型统计信息失败:', error)
    throw error
  }
}

/**
 * 获取完整的模型数据（包含渠道信息）
 * @returns {Promise} 完整的模型数据列表
 */
export const getFullModelData = async () => {
  try {
    // 获取所有模型
    const models = await getModelList()
    
    // 获取渠道列表
    const channels = await getChannelList()
    
    // 创建渠道映射
    const channelMap = {}
    channels.forEach(channel => {
      channelMap[channel.channel_id] = channel
    })
    
    // 合并模型和渠道信息
    const fullModelData = models.map(model => ({
      ...model,
      channel_name: channelMap[model.channel_id]?.channel_name || '未知渠道',
      channel_type: channelMap[model.channel_id]?.channel_api_type || 'unknown',
      switching: false // 用于控制切换状态的loading
    }))
    
    return fullModelData
  } catch (error) {
    console.error('获取完整模型数据失败:', error)
    throw error
  }
}
