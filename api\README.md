# 🐍 PUMI Agent API 服务

> **企业级AI智能体平台后端API服务** - 基于Flask构建的高性能、可扩展的RESTful API

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![Flask](https://img.shields.io/badge/Flask-3.0+-red.svg)](https://flask.palletsprojects.com/)
[![SQLAlchemy](https://img.shields.io/badge/SQLAlchemy-2.0+-green.svg)](https://www.sqlalchemy.org/)
[![JWT](https://img.shields.io/badge/JWT-Extended-orange.svg)](https://flask-jwt-extended.readthedocs.io/)

## 🌟 核心特性

### 🔐 企业级认证系统
- **双令牌机制** - Access Token + Refresh Token
- **外部认证集成** - 支持多种外部认证系统
- **权限管理** - 基于角色的访问控制(RBAC)
- **安全审计** - 完整的操作日志和安全事件记录

### 🤖 智能体管理
- **Dify平台集成** - 无缝对接Dify智能体平台
- **多智能体支持** - 统一管理多个AI智能体
- **实时对话** - 支持流式响应和多轮对话
- **智能体市场** - 智能体发现、分类和管理

### 📁 文件处理系统
- **多格式支持** - PDF、Word、Markdown等文档格式
- **智能解析** - 自动提取文档内容和结构
- **文件问答** - 基于文档内容的智能问答
- **版本管理** - 文件版本控制和历史记录

### 💬 对话管理
- **会话持久化** - 对话历史存储和检索
- **上下文管理** - 多轮对话上下文保持
- **消息路由** - 智能消息分发和处理
- **实时通信** - WebSocket支持实时消息推送

## 🏗️ 系统架构

```mermaid
graph TB
    subgraph "API Gateway"
        A[Nginx/Load Balancer]
    end

    subgraph "Flask Application"
        B[Flask App]
        C[Flask-RESTx]
        D[JWT Manager]
        E[CORS Handler]
    end

    subgraph "Business Layer"
        F[Auth Service]
        G[Agent Service]
        H[Chat Service]
        I[File Service]
    end

    subgraph "Data Layer"
        J[SQLAlchemy ORM]
        K[MySQL Database]
        L[Redis Cache]
    end

    subgraph "External Services"
        M[Dify Platform]
        N[External Auth]
        O[File Storage]
    end

    A --> B
    B --> C
    B --> D
    B --> E
    C --> F
    C --> G
    C --> H
    C --> I
    F --> J
    G --> J
    H --> J
    I --> J
    J --> K
    J --> L
    G --> M
    F --> N
    I --> O
```

## 📁 项目结构

```
api/
├── 📂 app/                      # 应用核心代码
│   ├── 📂 core/                # 核心组件
│   │   ├── 📄 auth_decorators.py    # 认证装饰器
│   │   ├── 📄 config.py            # 配置管理
│   │   ├── 📄 database.py          # 数据库配置
│   │   ├── 📄 jwt_manager.py       # JWT令牌管理
│   │   ├── 📄 logging_config.py    # 日志配置
│   │   └── 📄 security.py          # 安全工具
│   ├── 📂 models/              # 数据模型
│   │   ├── 📄 user.py              # 用户模型
│   │   ├── 📄 chat.py              # 对话模型
│   │   ├── 📄 file.py              # 文件模型
│   │   ├── 📄 channel.py           # 渠道模型
│   │   └── 📄 online_tools.py      # 工具模型
│   ├── 📂 routes/              # API路由
│   │   ├── 📄 auth.py              # 认证接口
│   │   ├── 📄 agent.py             # 智能体接口
│   │   ├── 📄 chat.py              # 对话接口
│   │   ├── 📄 file.py              # 文件接口
│   │   ├── 📄 channels.py          # 渠道接口
│   │   └── 📄 tools.py             # 工具接口
│   ├── 📂 services/            # 业务逻辑服务
│   │   ├── 📄 auth_service.py      # 认证服务
│   │   ├── 📄 agent_service.py     # 智能体服务
│   │   ├── 📄 chat_service.py      # 对话服务
│   │   ├── 📄 file_service.py      # 文件服务
│   │   └── 📄 user_service.py      # 用户服务
│   ├── 📂 schemas/             # 数据验证模式
│   │   ├── 📄 auth_schemas.py      # 认证数据模式
│   │   ├── 📄 chat_schemas.py      # 对话数据模式
│   │   └── 📄 file_schemas.py      # 文件数据模式
│   ├── 📂 utils/               # 工具类
│   │   ├── 📄 dependencies.py      # 依赖注入
│   │   ├── 📄 dify_client.py       # Dify客户端
│   │   ├── 📄 errors.py            # 错误处理
│   │   └── 📄 json_utils.py        # JSON工具
│   └── 📄 __init__.py          # 应用工厂
├── 📂 migrations/              # 数据库迁移脚本
├── 📂 logs/                   # 应用日志文件
├── 📂 uploads/                # 文件上传目录
├── 📄 requirements.txt         # Python依赖
├── 📄 run.py                  # 应用启动入口
├── 📄 init_db.py              # 数据库初始化
├── 📄 env.example             # 环境变量模板
└── 📄 README.md               # 项目文档
```

## 🚀 快速开始

### 📋 环境要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| **Python** | 3.8+ | 运行环境 |
| **MySQL** | 8.0+ | 数据库服务 |
| **Redis** | 6.0+ | 缓存服务(可选) |

### 🔧 安装配置

#### 1. 克隆项目
```bash
git clone <repository-url>
cd pumi-agent/api
```

#### 2. 创建虚拟环境
```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/Mac
# .venv\Scripts\activate   # Windows
```

#### 3. 安装依赖
```bash
pip install -r requirements.txt
```

#### 4. 配置环境变量
```bash
# 复制环境变量模板
cp env.example .env

# 编辑配置文件
vim .env  # 或使用其他编辑器
```

#### 5. 初始化数据库
```bash
# 确保MySQL服务已启动
# 创建数据库
mysql -u root -p -e "CREATE DATABASE pumi_agent_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 初始化数据库表
python init_db.py
```

#### 6. 启动服务
```bash
# 开发模式启动
python run.py

# 生产模式启动
gunicorn -w 4 -b 0.0.0.0:5000 run:app
```

✅ **服务启动成功！**
- API服务: `http://localhost:5000`
- API文档: `http://localhost:5000/docs/`
- 健康检查: `http://localhost:5000/health`

## 🛠️ 技术栈详解

### 🔧 核心框架
| 技术 | 版本 | 用途 | 文档链接 |
|------|------|------|----------|
| **Flask** | 3.0.0 | Web框架 | [📖](https://flask.palletsprojects.com/) |
| **Flask-RESTx** | 1.3.0 | API文档和验证 | [📖](https://flask-restx.readthedocs.io/) |
| **SQLAlchemy** | 2.0.23 | ORM数据库操作 | [📖](https://www.sqlalchemy.org/) |
| **Flask-JWT-Extended** | 4.6.0 | JWT认证管理 | [📖](https://flask-jwt-extended.readthedocs.io/) |

### 🗄️ 数据存储
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **MySQL** | 8.0+ | 主数据库 | 存储用户、对话、文件等数据 |
| **Redis** | 6.0+ | 缓存数据库 | 会话缓存、令牌黑名单 |
| **PyMySQL** | 1.1.0 | MySQL驱动 | Python MySQL连接器 |

### 🔐 安全组件
| 技术 | 版本 | 用途 | 特性 |
|------|------|------|------|
| **JWT** | - | 令牌认证 | 无状态认证、自动过期 |
| **CORS** | 4.0.0 | 跨域支持 | 安全的跨域资源共享 |
| **Marshmallow** | 3.20.1 | 数据验证 | 输入验证、序列化 |

### 🌐 外部集成
| 服务 | 用途 | 说明 |
|------|------|------|
| **Dify Platform** | AI智能体 | 智能体管理和对话 |
| **External Auth** | 外部认证 | 用户身份验证 |
| **File Storage** | 文件存储 | 文档上传和管理 |

## 📋 API接口概览

### 🔐 认证接口 (`/auth`)
| 方法 | 端点 | 功能 | 说明 |
|------|------|------|------|
| `POST` | `/auth/login` | 用户登录 | 验证用户凭据，返回JWT令牌 |
| `POST` | `/auth/refresh` | 刷新令牌 | 使用refresh_token获取新的access_token |
| `GET` | `/auth/verify` | 验证令牌 | 验证当前令牌有效性 |
| `POST` | `/auth/logout` | 用户登出 | 撤销用户令牌 |

### 🤖 智能体接口 (`/agent`)
| 方法 | 端点 | 功能 | 说明 |
|------|------|------|------|
| `GET` | `/agent/tags` | 获取标签 | 智能体分类标签列表 |
| `GET` | `/agent/list` | 智能体列表 | 分页获取智能体列表 |
| `GET` | `/agent/{id}` | 智能体详情 | 获取指定智能体详细信息 |
| `POST` | `/agent/chat` | 智能体对话 | 与智能体进行对话交互 |

### 💬 对话接口 (`/chat`)
| 方法 | 端点 | 功能 | 说明 |
|------|------|------|------|
| `GET` | `/chat/conversations` | 对话列表 | 获取用户对话历史 |
| `DELETE` | `/chat/conversations/delete` | 删除对话 | 删除指定对话记录 |
| `GET` | `/chat/history` | 对话历史 | 获取对话消息历史 |
| `GET` | `/chat/models` | 模型列表 | 获取可用AI模型列表 |
| `POST` | `/chat/message` | 发送消息 | 发送聊天消息 |

### 📁 文件接口 (`/file`)
| 方法 | 端点 | 功能 | 说明 |
|------|------|------|------|
| `POST` | `/file/upload` | 文件上传 | 上传文档文件 |
| `DELETE` | `/file/delete` | 文件删除 | 删除指定文件 |

## 🔧 数据模型设计

### 👤 用户模型 (User)
```python
class User(db.Model):
    user_id = db.Column(db.Integer, primary_key=True)
    user_code = db.Column(db.CHAR(7), unique=True, nullable=False)
    username = db.Column(db.String(50), nullable=False)
    role = db.Column(db.Enum('student', 'teacher', 'admin'))
    external_user_id = db.Column(db.String(100))
    refresh_token_hash = db.Column(db.String(64))
    last_login_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime)
```

### 💬 对话模型 (Conversation)
```python
class Conversation(db.Model):
    conversation_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('User.user_id'))
    title = db.Column(db.String(200))
    model_name = db.Column(db.String(100))
    created_at = db.Column(db.DateTime)
    updated_at = db.Column(db.DateTime)
```

### 📝 消息模型 (ChatMessage)
```python
class ChatMessage(db.Model):
    message_id = db.Column(db.Integer, primary_key=True)
    conversation_id = db.Column(db.Integer, db.ForeignKey('Conversation.conversation_id'))
    role = db.Column(db.Enum('user', 'assistant'))
    content = db.Column(db.Text)
    timestamp = db.Column(db.DateTime)
```

### 📁 文件模型 (File)
```python
class File(db.Model):
    file_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('User.user_id'))
    filename = db.Column(db.String(255))
    file_path = db.Column(db.String(500))
    file_size = db.Column(db.BigInteger)
    file_type = db.Column(db.String(50))
    upload_time = db.Column(db.DateTime)
```

## 🔐 认证系统设计

### JWT令牌机制
```mermaid
sequenceDiagram
    participant C as Client
    participant A as API Server
    participant E as External Auth
    participant D as Database

    C->>A: POST /auth/login (username, password)
    A->>E: 验证用户凭据
    E-->>A: 验证结果
    A->>D: 查询/创建用户记录
    A->>A: 生成JWT令牌对
    A-->>C: 返回access_token + refresh_token

    Note over C,A: 后续API请求
    C->>A: API请求 + Authorization Header
    A->>A: 验证access_token
    A-->>C: API响应

    Note over C,A: 令牌刷新
    C->>A: POST /auth/refresh + refresh_token
    A->>D: 验证refresh_token
    A->>A: 生成新access_token
    A-->>C: 返回新access_token
```

### 权限控制
```python
# 角色权限装饰器
@jwt_required()
@require_role(['admin', 'teacher'])
def admin_only_endpoint():
    pass

@jwt_required()
@require_role(['student', 'teacher', 'admin'])
def authenticated_endpoint():
    pass
```

## 🔧 开发指南

### 📝 代码规范
```bash
# 代码格式化
black app/

# 代码检查
flake8 app/

# 类型检查
mypy app/

# 安全检查
bandit -r app/
```

### 🧪 测试
```bash
# 运行所有测试
python -m pytest

# 运行特定测试
python -m pytest tests/test_auth.py

# 生成覆盖率报告
python -m pytest --cov=app --cov-report=html
```

### 📊 性能监控
```bash
# 启用性能分析
FLASK_ENV=development python run.py --profile

# 查看SQL查询
SQLALCHEMY_ECHO=True python run.py

# 内存使用监控
memory_profiler python run.py
```

### 🐛 调试技巧
```python
# 启用调试模式
app.config['DEBUG'] = True

# 添加断点
import pdb; pdb.set_trace()

# 日志调试
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📚 相关文档

- [🔗 Flask官方文档](https://flask.palletsprojects.com/)
- [🔗 SQLAlchemy文档](https://www.sqlalchemy.org/)
- [🔗 Flask-JWT-Extended文档](https://flask-jwt-extended.readthedocs.io/)
- [🔗 Marshmallow文档](https://marshmallow.readthedocs.io/)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/new-feature`)
3. 提交更改 (`git commit -am 'Add new feature'`)
4. 推送到分支 (`git push origin feature/new-feature`)
5. 创建 Pull Request

---

<div align="center">

**🐍 PUMI Agent API 服务**

*企业级AI智能体平台后端API*

[![⭐ Star](https://img.shields.io/github/stars/your-org/pumi-agent?style=social)](https://github.com/your-org/pumi-agent)
[![📄 License](https://img.shields.io/github/license/your-org/pumi-agent)](https://github.com/your-org/pumi-agent/blob/main/LICENSE)

</div>
