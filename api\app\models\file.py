"""
文件相关的数据库模型
包含文件上传和处理信息
"""

from datetime import datetime
import uuid
from app import db

class File(db.Model):
    """文件模型"""
    __tablename__ = "files"
    
    file_id = db.Column(db.String(36), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    filename = db.Column(db.String(255), nullable=False, comment='文件名')
    file_path = db.Column(db.String(512), nullable=False, comment='文件路径')
    file_type = db.Column(db.String(50), nullable=False, comment='文件类型')
    file_size = db.Column(db.Integer, nullable=False, comment='文件大小(字节)')
    user_id = db.Column(db.Integer, db.ForeignKey("User.user_id"), nullable=False, comment='用户ID')
    upload_time = db.Column(db.DateTime, default=datetime.utcnow, comment='上传时间')
    status = db.Column(db.String(20), default="pending", comment='处理状态')  # pending, processed, failed
    description = db.Column(db.Text, nullable=True, comment='文件描述')
    
    # 关联
    user = db.relationship("User", back_populates="files")
    
    def __repr__(self):
        return f'<File {self.filename}>' 