import { createRouter, createWebHistory } from 'vue-router'
import Login from "@/views/Login.vue"
import * as tokenService from '@/services/tokenService'
import { useUserStore } from '@/stores/userStore'
import AgentPlaza from "@/views/AgentPlaza.vue"
import AgentChat from "@/views/AgentChat.vue"

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/chat'
    },
    {
      path:"/login",
      name: 'Login',
      component: Login,
      // 登录页不需要认证
      meta: { requiresAuth: false }
    },
    {
      path: '/chat',
      component: () => import('@/views/Chat.vue'),
      // 聊天页需要认证
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'NewChat',
          component: () => import('@/components/Chat/GreetingBlock.vue'),
        },
        {
          path: ':id',
          name: 'ChatWithId',
          component: () => import('@/components/Chat/GreetingBlock.vue'),
          props: true // 将路由参数作为 props 传递给组件
        },
        {
          path: 'agent-plaza',
          name: 'AgentPlaza',
          component: AgentPlaza,
        },
        {
          path: 'agent/:agentId',
          name: 'AgentChat',
          component: AgentChat,
          props: true // 将路由参数作为 props 传递给组件
        },
        {
          path: 'aigc-tools',
          name: 'AigcTools',
          component: () => import('@/views/AigcTools.vue'),
        }
      ],
    },
    // 后台管理路由
    {
      path: '/admin',
      component: () => import('@/layouts/AdminLayout.vue'),
      meta: { requiresAuth: true, requiresAdmin: true },
      children: [
        {
          path: '',
          redirect: '/admin/dashboard'
        },
        {
          path: 'dashboard',
          name: 'AdminDashboard',
          component: () => import('@/views/AdminDashboard.vue'),
          meta: { title: '仪表盘' }
        },
        {
          path: 'users',
          name: 'UserManagement',
          component: () => import('@/views/AdminDashboard.vue'),
          meta: { title: '用户管理' }
        },
        {
          path: 'agents',
          name: 'AgentManagement',
          component: () => import('@/views/AdminDashboard.vue'),
          meta: { title: '智能体管理' }
        },
        {
          path: 'ai-resources',
          name: 'AIResourceManagement',
          component: () => import('@/views/AdminAIResources.vue'),
          meta: { title: 'AI资源管理' }
        },
        {
          path: 'aigc-tools/tools',
          name: 'AigcToolsManagement',
          component: () => import('@/views/admin/AigcToolsManagement.vue'),
          meta: { title: '工具管理' }
        },
        {
          path: 'aigc-tools/categories',
          name: 'AigcCategoriesManagement',
          component: () => import('@/views/admin/AigcCategoriesManagement.vue'),
          meta: { title: '分类管理' }
        },
        {
          path: 'conversations',
          name: 'ConversationManagement',
          component: () => import('@/views/AdminDashboard.vue'),
          meta: { title: '对话记录' }
        },
        {
          path: 'analytics',
          name: 'Analytics',
          component: () => import('@/views/AdminDashboard.vue'),
          meta: { title: '数据分析' }
        },
        {
          path: 'settings',
          name: 'SystemSettings',
          component: () => import('@/views/AdminDashboard.vue'),
          meta: { title: '系统设置' }
        }
      ]
    }
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 获取访问令牌
  const accessToken = tokenService.getAccessToken();
  const isLoggedIn = accessToken && !tokenService.isTokenExpired(accessToken);
  
  // 如果访问登录页且已登录，重定向到聊天页面
  if (to.path === '/login' && isLoggedIn) {
    next('/chat');
    return;
  }
  
  // 检查路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false);
  
  // 如果路由不需要认证，直接放行
  if (!requiresAuth) {
    next();
    return;
  }
  
  // 如果需要认证但没有令牌或令牌已过期，重定向到登录页
  if (!isLoggedIn) {
    next({
      path: '/login',
      query: { redirect: to.fullPath } // 保存尝试访问的URL，登录后可以重定向回来
    });
    return;
  }
  
  // 检查是否需要管理员权限
  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin);

  if (requiresAdmin) {
    const userStore = useUserStore();
    const userRole = userStore.userProfile.role;

    // 检查用户是否是管理员或教师
    if (userRole !== 'admin' && userRole !== 'teacher') {
      // 权限不足，重定向到聊天页面
      next({
        path: '/chat',
        query: { error: 'permission_denied' }
      });
      return;
    }
  }

  // 如果令牌即将过期，继续导航但在后台触发刷新
  // 这个刷新会由axios请求拦截器处理，这里只记录一个警告
  if (tokenService.isTokenNearExpiry(accessToken)) {
    console.warn('令牌即将过期，即将在后台自动刷新');
  }

  // 令牌有效且权限足够，允许导航
  next();
});

export default router
