"""
缓存工具类
提供简单的内存缓存功能
"""

import time
from functools import wraps
from typing import Any, Dict, Callable
import logging

logger = logging.getLogger(__name__)


class SimpleCache:
    """简单的内存缓存类"""
    
    def __init__(self):
        self.cache: Dict[str, tuple] = {}
    
    def get(self, key: str, func: Callable, ttl: int = 300) -> Any:
        """
        获取缓存或设置新缓存
        
        Args:
            key: 缓存键
            func: 获取数据的函数
            ttl: 缓存时间（秒）
            
        Returns:
            缓存的数据
        """
        now = time.time()
        
        # 检查缓存是否存在且未过期
        if key in self.cache:
            data, timestamp = self.cache[key]
            if now - timestamp < ttl:
                logger.debug(f"缓存命中: {key}")
                return data
        
        # 缓存过期或不存在，重新获取数据
        logger.debug(f"缓存未命中，重新获取数据: {key}")
        data = func()
        self.cache[key] = (data, now)
        return data
    
    def set(self, key: str, data: Any) -> None:
        """设置缓存"""
        self.cache[key] = (data, time.time())
    
    def delete(self, key: str) -> None:
        """删除缓存"""
        self.cache.pop(key, None)
    
    def clear(self) -> None:
        """清空所有缓存"""
        self.cache.clear()
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        now = time.time()
        active_keys = []
        expired_keys = []
        
        for key, (data, timestamp) in self.cache.items():
            if now - timestamp < 600:  # 10分钟内的缓存认为是活跃的
                active_keys.append(key)
            else:
                expired_keys.append(key)
        
        return {
            'total_keys': len(self.cache),
            'active_keys': len(active_keys),
            'expired_keys': len(expired_keys),
            'cache_keys': list(self.cache.keys())
        }


def cache_for(seconds: int):
    """
    缓存装饰器
    
    Args:
        seconds: 缓存时间（秒）
    """
    def decorator(func: Callable) -> Callable:
        cache = {}
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            key = f"{func.__name__}:{str(args)}:{str(sorted(kwargs.items()))}"
            now = time.time()
            
            # 检查缓存
            if key in cache:
                result, timestamp = cache[key]
                if now - timestamp < seconds:
                    logger.debug(f"装饰器缓存命中: {func.__name__}")
                    return result
            
            # 执行函数并缓存结果
            logger.debug(f"装饰器缓存未命中，执行函数: {func.__name__}")
            result = func(*args, **kwargs)
            cache[key] = (result, now)
            
            # 清理过期缓存（简单策略：每100次调用清理一次）
            if len(cache) % 100 == 0:
                expired_keys = [k for k, (_, ts) in cache.items() if now - ts >= seconds * 2]
                for k in expired_keys:
                    cache.pop(k, None)
                if expired_keys:
                    logger.debug(f"清理过期缓存: {len(expired_keys)} 个")
            
            return result
        
        # 添加缓存管理方法
        wrapper.clear_cache = lambda: cache.clear()
        wrapper.cache_info = lambda: {
            'cache_size': len(cache),
            'function_name': func.__name__,
            'ttl_seconds': seconds
        }
        
        return wrapper
    return decorator


# 创建全局缓存实例
admin_cache = SimpleCache()

# 预定义的缓存时间常量
CACHE_TIME_SYSTEM = 30      # 系统监控数据缓存30秒
CACHE_TIME_STATS = 300      # 统计数据缓存5分钟
CACHE_TIME_CHARTS = 600     # 图表数据缓存10分钟
