import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useApiStore } from './apiStore'
import unifiedStorage from '@/utils/unifiedStorage'

/**
 * 模型管理 Store
 * 负责存储和管理 AI 模型列表和选择状态
 */
export const useModelStore = defineStore('model', () => {
  // 模型列表
  const modelList = ref([])
  // 当前选中的模型 - 使用ref + watch的方式替代computed
  const _selectedModelData = ref(unifiedStorage.get('selected_model', null))

  const selectedModel = computed({
    get: () => _selectedModelData.value,
    set: (model) => {
      if (model) {
        const modelData = {
          model_id: model.model_id,
          model_name: model.model_name,
          channel_id: model.channel_id || '',
          channel_name: model.channel_name || '',
          channel_type: model.channel_type || ''
        }
        unifiedStorage.set('selected_model', modelData)
        _selectedModelData.value = modelData
      } else {
        unifiedStorage.remove('selected_model')
        _selectedModelData.value = null
      }
    }
  })
  // 加载状态
  const isLoadingModels = ref(false)

  /**
   * 设置模型列表
   * @param {Array} models - 模型列表数据
   */
  const setModelList = (models) => {
    // 兼容后端返回 { models: [...] }
    if (models && models.models && Array.isArray(models.models)) {
      modelList.value = models.models
    } else if (Array.isArray(models)) {
      modelList.value = models
    } else {
      modelList.value = []
      return
    }

    // 若没有选择模型且有可用模型，则自动选择第一个
    if (!selectedModel.value && modelList.value.length > 0) {
      setSelectedModel(modelList.value[0])
    }
  }

  /**
   * 设置当前选中的模型（兼容旧代码）
   * @param {Object} model - 模型对象
   */
  const setSelectedModel = (model) => {
    selectedModel.value = model
  }

  /**
   * 设置加载状态
   * @param {Boolean} status - 加载状态
   */
  const setLoadingStatus = (status) => {
    isLoadingModels.value = status
  }

  /**
   * 拉取模型列表（接入接口）
   */
  const fetchModelList = async () => {
    const apiStore = useApiStore()
    await apiStore.getModelList()
  }

  return {
    modelList,
    selectedModel,
    isLoadingModels,
    setModelList,
    setSelectedModel,
    setLoadingStatus,
    fetchModelList
  }
}, {
  persist: false // 使用统一存储机制
})