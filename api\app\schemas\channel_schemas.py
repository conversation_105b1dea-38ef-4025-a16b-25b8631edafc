"""
渠道和模型API模型定义
定义渠道和模型相关的API输入输出模型
"""

from flask_restx import fields, Namespace

# 创建一个临时命名空间用于定义模型
_temp_ns = Namespace('schemas')

# 渠道基础模型
channel_base_model = _temp_ns.model('ChannelBase', {
    'channel_name': fields.String(description='渠道名称'),
    'channel_url': fields.String(description='渠道URL'),
    'channel_api_key': fields.String(description='API密钥'),
    'channel_api_type': fields.String(description='API类型'),
    'channel_api_enabled': fields.Boolean(description='是否启用')
})

# 创建渠道请求模型
channel_create_model = _temp_ns.model('ChannelCreate', {
    'channel_name': fields.String(required=True, description='渠道名称'),
    'channel_url': fields.String(required=True, description='渠道URL'),
    'channel_api_key': fields.String(required=True, description='API密钥'),
    'channel_api_type': fields.String(required=True, description='API类型', enum=['ollama', 'openai', 'zhipu'])
})

# 渠道响应模型
channel_response_model = _temp_ns.model('ChannelResponse', {
    'channel_id': fields.Integer(description='渠道ID'),
    'channel_name': fields.String(description='渠道名称'),
    'channel_url': fields.String(description='渠道URL'),
    'channel_api_key': fields.String(description='API密钥'),
    'channel_api_type': fields.String(description='API类型'),
    'channel_api_enabled': fields.Boolean(description='是否启用')
})

# 模型基础模型
model_base_model = _temp_ns.model('ModelBase', {
    'model_name': fields.String(description='模型名称'),
    'model_enabled': fields.Boolean(description='模型是否启用')
})

# 模型响应模型
model_response_model = _temp_ns.model('ModelResponse', {
    'model_id': fields.Integer(description='模型ID'),
    'channel_id': fields.Integer(description='渠道ID'),
    'model_name': fields.String(description='模型名称'),
    'model_enabled': fields.Boolean(description='模型是否启用')
})

# 创建模型请求模型
model_create_model = _temp_ns.model('ModelCreate', {
    'channel_id': fields.Integer(required=True, description='渠道ID'),
    'models_name': fields.List(fields.String, required=True, description='模型名称列表')
})

# 简单的ID请求模型
channel_id_model = _temp_ns.model('ChannelIdRequest', {
    'channel_id': fields.Integer(required=True, description='渠道ID')
})

# 模型操作请求模型
model_operation_model = _temp_ns.model('ModelOperation', {
    'channel_id': fields.Integer(required=True, description='渠道ID'),
    'model_name': fields.String(required=True, description='模型名称')
}) 