"""图标文件处理服务"""
import os
import uuid
from datetime import datetime
from typing import Optional, <PERSON><PERSON>
from sqlalchemy.orm import Session

from .file_processor import FileProcessor

# 尝试导入PIL，如果没有安装则使用基本功能
try:
    from PIL import Image
    HAS_PIL = True
except ImportError:
    HAS_PIL = False
    print("Warning: PIL (Pillow) not installed. Image processing features will be limited.")


class IconProcessor(FileProcessor):
    """图标文件处理服务，专门处理图片图标的上传、验证和存储"""
    
    # 图标专用配置
    ALLOWED_EXTENSIONS = {
        "png", "jpg", "jpeg", "gif", "webp"
    }
    ICON_SUBDIR = "aigc_icons"
    MAX_FILE_SIZE_MB = 2
    MAX_FILE_SIZE = MAX_FILE_SIZE_MB * 1024 * 1024  # 2MB
    
    # 图片尺寸配置
    MAX_WIDTH = 512
    MAX_HEIGHT = 512
    RECOMMENDED_SIZE = (64, 64)
    
    def __init__(self, db: Session):
        """初始化图标处理器"""
        # 不调用父类的__init__，因为我们要使用不同的配置
        self.db = db

        # 确保图标上传目录存在
        icon_upload_dir = self.get_icon_upload_dir()
        os.makedirs(icon_upload_dir, exist_ok=True)

    def get_icon_upload_dir(self) -> str:
        """获取图标上传目录的完整路径"""
        return os.path.join("uploads", self.ICON_SUBDIR)
    
    def save_icon(self, file, tool_id: int, user_id: Optional[int] = None) -> str:
        """
        保存图标文件
        
        Args:
            file: 上传的文件对象
            tool_id: 工具ID
            user_id: 用户ID（可选）
            
        Returns:
            str: 图标文件的相对路径
            
        Raises:
            ValueError: 文件验证失败时抛出异常
        """
        # 验证文件
        self._validate_icon_file(file)
        
        # 生成文件名
        timestamp = int(datetime.now().timestamp())
        file_extension = self.get_file_extension(file.filename)
        filename = f"tool_{tool_id}_{timestamp}.{file_extension}"
        
        # 构建文件路径
        file_path = os.path.join(self.get_icon_upload_dir(), filename)
        
        # 保存文件
        self._save_file_to_disk(file, file_path)
        
        # 处理图片（压缩和优化）
        self._process_image(file_path)
        
        # 返回相对路径（用于存储在数据库中，使用正斜杠用于URL）
        return f"/aigc_icons/{filename}"
    
    def delete_icon(self, icon_url: str) -> bool:
        """
        删除图标文件
        
        Args:
            icon_url: 图标URL路径
            
        Returns:
            bool: 删除是否成功
        """
        if not icon_url or not icon_url.startswith('/aigc_icons/'):
            return False
            
        # 构建实际文件路径
        filename = icon_url.replace('/aigc_icons/', '')
        file_path = os.path.join(self.get_icon_upload_dir(), filename)
        
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
        except Exception as e:
            print(f"删除图标文件失败: {e}")
            
        return False
    
    def get_icon_path(self, icon_url: str) -> Optional[str]:
        """
        获取图标文件的实际路径
        
        Args:
            icon_url: 图标URL路径
            
        Returns:
            Optional[str]: 文件的实际路径，如果文件不存在则返回None
        """
        if not icon_url or not icon_url.startswith('/aigc_icons/'):
            return None
            
        filename = icon_url.replace('/aigc_icons/', '')
        # 构建绝对路径
        file_path = os.path.join(self.get_icon_upload_dir(), filename)
        abs_path = os.path.abspath(file_path)

        return abs_path if os.path.exists(abs_path) else None
    
    def _validate_icon_file(self, file) -> None:
        """验证图标文件"""
        # 检查文件名
        if not file.filename:
            raise ValueError("文件名不能为空")
        
        # 检查文件类型
        if not self.is_valid_file_type(file.filename):
            allowed_types = ", ".join(self.ALLOWED_EXTENSIONS)
            raise ValueError(f"不支持的文件类型，支持的类型: {allowed_types}")
        
        # 检查文件大小
        file.seek(0, 2)  # 移动到文件末尾
        file_size = file.tell()
        file.seek(0)  # 重置文件指针
        
        if file_size > self.MAX_FILE_SIZE:
            raise ValueError(f"文件大小超过限制，最大允许 {self.MAX_FILE_SIZE_MB}MB")
        
        if file_size == 0:
            raise ValueError("文件不能为空")
    
    def _save_file_to_disk(self, file, file_path: str) -> None:
        """将文件保存到磁盘"""
        try:
            # 尝试直接保存文件
            file.save(file_path)
        except (AttributeError, TypeError):
            # 如果没有save方法，尝试其他方法
            try:
                if hasattr(file, 'stream'):
                    with open(file_path, "wb") as buffer:
                        file.stream.seek(0)
                        buffer.write(file.stream.read())
                elif hasattr(file, 'read'):
                    with open(file_path, "wb") as buffer:
                        file.seek(0)
                        buffer.write(file.read())
                else:
                    raise ValueError("无法读取文件内容")
            except Exception as e:
                raise ValueError(f"无法保存文件: {str(e)}")
    
    def _process_image(self, file_path: str) -> None:
        """处理图片：验证、压缩和优化"""
        if not HAS_PIL:
            # 如果没有PIL，只进行基本的文件验证
            self._basic_image_validation(file_path)
            return

        try:
            with Image.open(file_path) as img:
                # 验证是否为有效图片
                img.verify()

            # 重新打开图片进行处理（verify后需要重新打开）
            with Image.open(file_path) as img:
                # 转换为RGB模式（如果需要）
                if img.mode in ('RGBA', 'LA', 'P'):
                    # 保持透明度支持
                    if img.mode == 'P' and 'transparency' in img.info:
                        img = img.convert('RGBA')
                elif img.mode not in ('RGB', 'RGBA'):
                    img = img.convert('RGB')

                # 检查尺寸并压缩（如果需要）
                width, height = img.size
                if width > self.MAX_WIDTH or height > self.MAX_HEIGHT:
                    # 保持宽高比进行缩放
                    img.thumbnail((self.MAX_WIDTH, self.MAX_HEIGHT), Image.Resampling.LANCZOS)

                    # 保存压缩后的图片
                    img.save(file_path, optimize=True, quality=85)

        except Exception as e:
            # 如果图片处理失败，删除文件并抛出异常
            if os.path.exists(file_path):
                os.remove(file_path)
            raise ValueError(f"图片处理失败: {str(e)}")

    def _basic_image_validation(self, file_path: str) -> None:
        """基本的图片文件验证（不依赖PIL）"""
        try:
            # 检查文件是否存在且不为空
            if not os.path.exists(file_path):
                raise ValueError("文件不存在")

            file_size = os.path.getsize(file_path)
            if file_size == 0:
                raise ValueError("文件为空")

            # 简单的文件头验证
            with open(file_path, 'rb') as f:
                header = f.read(16)

            # 检查常见图片格式的文件头
            if not self._is_valid_image_header(header):
                raise ValueError("不是有效的图片文件")

        except Exception as e:
            if os.path.exists(file_path):
                os.remove(file_path)
            raise ValueError(f"图片验证失败: {str(e)}")

    def _is_valid_image_header(self, header: bytes) -> bool:
        """检查文件头是否为有效的图片格式"""
        # PNG: 89 50 4E 47 0D 0A 1A 0A
        if header.startswith(b'\x89PNG\r\n\x1a\n'):
            return True
        # JPEG: FF D8 FF
        if header.startswith(b'\xff\xd8\xff'):
            return True
        # GIF: GIF87a or GIF89a
        if header.startswith(b'GIF87a') or header.startswith(b'GIF89a'):
            return True
        # WebP: RIFF....WEBP
        if header.startswith(b'RIFF') and b'WEBP' in header:
            return True
        return False
    
    def get_image_info(self, file_path: str) -> Optional[Tuple[int, int, str]]:
        """
        获取图片信息

        Args:
            file_path: 图片文件路径

        Returns:
            Optional[Tuple[int, int, str]]: (宽度, 高度, 格式)，失败时返回None
        """
        if not HAS_PIL:
            # 如果没有PIL，返回基本信息
            if os.path.exists(file_path):
                file_ext = self.get_file_extension(file_path)
                return (0, 0, file_ext.upper())  # 返回0x0尺寸和文件扩展名
            return None

        try:
            with Image.open(file_path) as img:
                return (img.width, img.height, img.format)
        except Exception:
            return None
