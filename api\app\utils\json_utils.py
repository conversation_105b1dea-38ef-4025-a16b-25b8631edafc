"""JSON工具函数"""
from datetime import datetime, date
import json

# 直接使用Python标准库的JSONEncoder作为基类
class CustomJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理特殊类型如datetime"""
    def default(self, obj):
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super().default(obj)

def datetime_to_isoformat(dt):
    """将datetime对象转换为ISO 8601格式字符串"""
    if isinstance(dt, datetime):
        return dt.isoformat()
    return dt

def serialize_datetime(obj):
    """递归处理对象中的datetime，将其转换为ISO 8601格式字符串"""
    if isinstance(obj, dict):
        return {k: serialize_datetime(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [serialize_datetime(i) for i in obj]
    elif isinstance(obj, (datetime, date)):
        return obj.isoformat()
    return obj

def json_dumps(obj):
    """将对象转换为JSON字符串，处理datetime等特殊类型"""
    return json.dumps(serialize_datetime(obj)) 