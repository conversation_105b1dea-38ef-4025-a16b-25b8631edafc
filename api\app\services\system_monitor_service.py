"""
系统监控服务
使用psutil获取系统资源使用情况
"""

import psutil
import time
from datetime import datetime, timezone
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)


class SystemMonitorService:
    """系统监控服务类"""
    
    def __init__(self):
        self._last_network_io = None
        self._last_network_time = None
    
    def get_cpu_info(self) -> Dict[str, Any]:
        """获取CPU信息"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            return {
                'usage': round(cpu_percent, 1),
                'cores': cpu_count,
                'frequency': int(cpu_freq.current) if cpu_freq else 0
            }
        except Exception as e:
            logger.error(f"获取CPU信息失败: {e}")
            return {
                'usage': 0.0,
                'cores': 0,
                'frequency': 0
            }
    
    def get_memory_info(self) -> Dict[str, Any]:
        """获取内存信息"""
        try:
            memory = psutil.virtual_memory()
            
            return {
                'usage': round(memory.percent, 1),
                'used_gb': round(memory.used / (1024**3), 1),
                'total_gb': round(memory.total / (1024**3), 1)
            }
        except Exception as e:
            logger.error(f"获取内存信息失败: {e}")
            return {
                'usage': 0.0,
                'used_gb': 0.0,
                'total_gb': 0.0
            }
    
    def get_disk_info(self) -> Dict[str, Any]:
        """获取磁盘信息"""
        try:
            disk = psutil.disk_usage('/')
            
            return {
                'usage': round((disk.used / disk.total) * 100, 1),
                'used_gb': round(disk.used / (1024**3), 1),
                'total_gb': round(disk.total / (1024**3), 1)
            }
        except Exception as e:
            logger.error(f"获取磁盘信息失败: {e}")
            return {
                'usage': 0.0,
                'used_gb': 0.0,
                'total_gb': 0.0
            }
    
    def get_network_info(self) -> Dict[str, Any]:
        """获取网络信息"""
        try:
            # 获取网络IO统计
            net_io = psutil.net_io_counters()
            current_time = time.time()
            
            if self._last_network_io and self._last_network_time:
                # 计算速度
                time_delta = current_time - self._last_network_time
                bytes_sent_delta = net_io.bytes_sent - self._last_network_io.bytes_sent
                bytes_recv_delta = net_io.bytes_recv - self._last_network_io.bytes_recv
                
                upload_speed = bytes_sent_delta / time_delta / (1024 * 1024)  # MB/s
                download_speed = bytes_recv_delta / time_delta / (1024 * 1024)  # MB/s
                total_speed = upload_speed + download_speed
                
                # 更新缓存
                self._last_network_io = net_io
                self._last_network_time = current_time
                
                return {
                    'speed': f"{total_speed:.1f}MB/s",
                    'upload_speed': f"{upload_speed:.1f}MB/s",
                    'download_speed': f"{download_speed:.1f}MB/s"
                }
            else:
                # 首次调用，初始化缓存
                self._last_network_io = net_io
                self._last_network_time = current_time
                
                return {
                    'speed': "0.0MB/s",
                    'upload_speed': "0.0MB/s", 
                    'download_speed': "0.0MB/s"
                }
                
        except Exception as e:
            logger.error(f"获取网络信息失败: {e}")
            return {
                'speed': "0.0MB/s",
                'upload_speed': "0.0MB/s",
                'download_speed': "0.0MB/s"
            }
    
    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            cpu_info = self.get_cpu_info()
            memory_info = self.get_memory_info()
            disk_info = self.get_disk_info()
            
            # 简单的健康状态判断
            cpu_usage = cpu_info['usage']
            memory_usage = memory_info['usage']
            disk_usage = disk_info['usage']
            
            avg_usage = (cpu_usage + memory_usage + disk_usage) / 3
            
            if avg_usage < 60:
                status = "normal"
            elif avg_usage < 85:
                status = "warning"
            else:
                status = "error"
            
            return {
                'status': status,
                'last_update': datetime.now(timezone.utc).isoformat(),
                'services': {
                    'database': 'healthy',  # 这里可以添加实际的数据库健康检查
                    'redis': 'healthy',     # 这里可以添加实际的Redis健康检查
                    'external_auth': 'healthy'  # 这里可以添加实际的外部认证健康检查
                }
            }
        except Exception as e:
            logger.error(f"获取系统健康状态失败: {e}")
            return {
                'status': 'error',
                'last_update': datetime.now(timezone.utc).isoformat(),
                'services': {
                    'database': 'unknown',
                    'redis': 'unknown',
                    'external_auth': 'unknown'
                }
            }
    
    def get_all_monitor_data(self) -> Dict[str, Any]:
        """获取所有监控数据"""
        return {
            'cpu': self.get_cpu_info(),
            'memory': self.get_memory_info(),
            'disk': self.get_disk_info()
            # 移除网络监控，因为在Windows上可能不准确
        }


# 创建全局实例
system_monitor_service = SystemMonitorService()
