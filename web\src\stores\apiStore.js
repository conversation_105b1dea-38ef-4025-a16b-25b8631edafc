import { defineStore } from 'pinia'
import axios from 'axios'
import { useAuthStore } from './authStore'
import { useApiConfigStore } from './apiConfig'
import { useUserStore } from './userStore'
import { useModelStore } from './modelStore'
import { useChatStore } from './chatStore'
import { useAgentStore } from './agentStore'

/**
 * API 请求统一管理 Store
 * 所有后端接口请求必须通过此 Store 进行，禁止在页面或组件中直接使用 axios
 */
export const useApiStore = defineStore('api', {
  actions: {
    /**
     * 用户登录
     * @param {string} username - 用户名，来自页面表单输入
     * @param {string} password - 密码，来自页面表单输入
     * @returns {Promise} - 返回登录接口响应
     * 
     * 全局参数：无
     * 结果存储：authStore.setAuthData(access_token, refresh_token)
     */
    async login(username, password) {
      const apiConfig = useApiConfigStore()
      const authStore = useAuthStore()
      const loginUrl = apiConfig.getUrl('login')

      try {
        const response = await axios.post(loginUrl, {
          username,
          password,
        }, {
          headers: {
            'Content-Type': 'application/json',
          }
        })

        const { access_token, refresh_token, user } = response.data
        // 存储token到authStore
        authStore.setAuthData(access_token, refresh_token)
        authStore.setUserInfo(username)

        // 存储用户信息到userStore
        if (user) {
          const userStore = useUserStore()
          userStore.setUserInfo(user)
        }

        return response.data
      } catch (error) {
        console.error('登录失败', error)
        throw error
      }
    },

    /**
     * 获取用户信息 (已适配新接口 /auth/verify)
     * @returns {Promise} - 返回用户信息
     *
     * 全局参数：令牌由请求拦截器自动添加到Authorization头
     * 结果存储：userStore.setUserInfo(userInfo) - 用户信息保存到 userStore
     */
    async getUserInfo() {
      const apiConfig = useApiConfigStore()
      const userStore = useUserStore()
      const url = apiConfig.getUrl('userInfo')

      try {
        const response = await axios.get(url)

        // 新接口返回格式: { valid: true, user: {...}, token_info: {...} }
        // 提取用户信息
        const userData = response.data.user || response.data

        // 存储用户信息到 userStore
        userStore.setUserInfo(userData)

        return userData
      } catch (error) {
        console.error('获取用户信息失败', error)
        throw error
      }
    },

    // 注意：令牌刷新功能已统一到axiosConfig.js的请求拦截器中
    // 移除了重复的refreshToken方法，避免多套机制的竞态条件

    /**
     * 用户登出 (新增功能)
     * @returns {Promise} - 返回登出结果
     *
     * 全局参数：从 authStore 获取 refresh_token
     * 结果存储：清除所有认证数据
     */
    async logout() {
      const apiConfig = useApiConfigStore()
      const authStore = useAuthStore()
      const userStore = useUserStore()
      const url = apiConfig.getUrl('logout')

      try {
        // 发送登出请求到后端
        await axios.post(url, {
          refresh_token: authStore.refresh_token
        }, {
          // 设置标记，使请求拦截器忽略此请求
          _skipAuthRefresh: true
        })

        console.log('服务器端登出成功')
      } catch (error) {
        console.error('服务器端登出失败', error)
        // 即使服务器端登出失败，也要清除本地数据
      } finally {
        // 无论服务器端登出是否成功，都清除本地数据
        authStore.clearAuthData()
        userStore.clearUserInfo()
      }
    },

    /**
     * 获取模型列表
     * @returns {Promise} - 返回模型列表数据
     * 
     * 全局参数：从 authStore 获取 access_token
     * 结果存储：modelStore.setModelList(models)
     */
    async getModelList() {
      const apiConfig = useApiConfigStore()
      const authStore = useAuthStore()
      const modelStore = useModelStore()
      const url = apiConfig.getNestUrl('chat', 'model_list')

      try {
        modelStore.setLoadingStatus(true)
        const response = await axios.post(url, {}, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authStore.access_token}`
          }
        })

        // 存储模型列表
        modelStore.setModelList(response.data)

        return response.data
      } catch (error) {
        console.error('获取模型列表失败', error)
        throw error
      } finally {
        modelStore.setLoadingStatus(false)
      }
    },

    /**
     * 获取用户所有对话记录
     * @returns {Promise} - 返回对话记录列表
     * 
     * 全局参数：令牌由请求拦截器自动添加
     * 结果存储：chatStore.setConversations(conversations)
     */
    async getConversations() {
      const apiConfig = useApiConfigStore()
      const chatStore = useChatStore()
      const url = apiConfig.getNestUrl('chat', 'conversations')
      
      try {
        chatStore.setLoadingStatus(true)
        const response = await axios.get(url)
        
        // 存储对话记录
        chatStore.setConversations(response.data)
        
        return response.data
      } catch (error) {
        console.error('获取对话记录失败', error)
        throw error
      } finally {
        chatStore.setLoadingStatus(false)
      }
    },

    /**
     * 删除对话记录
     * @param {string} conversationId - 对话记录ID
     * @returns {Promise} - 返回删除结果
     * 
     * 全局参数：令牌由请求拦截器自动添加
     * 结果处理：从 chatStore 中移除该对话
     */
    async deleteConversation(conversationId) {
      const apiConfig = useApiConfigStore()
      const chatStore = useChatStore()
      const url = apiConfig.getNestUrl('chat', 'delete_conversation')
      
      try {
        const response = await axios.delete(url, {
          data: {
            conversation_id: conversationId
          }
        })
        
        // 从 chatStore 中移除该对话
        chatStore.removeConversation(conversationId)
        
        return response.data
      } catch (error) {
        console.error('删除对话失败', error)
        throw error
      }
    },

    /**
     * 获取特定对话的历史记录
     * @param {string} conversationId - 对话记录ID
     * @returns {Promise} - 返回对话历史数据
     * 
     * 全局参数：令牌由请求拦截器自动添加
     * 结果存储：chatStore.setCurrentConversationHistory(history)
     */
    async getConversationHistory(conversationId) {
      const apiConfig = useApiConfigStore()
      const chatStore = useChatStore()
      const url = apiConfig.getNestUrl('chat', 'conversation_history')
      
      try {
        chatStore.setLoadingStatus(true)
        // 修改为 POST 请求，通过请求体传递 conversation_id
        const response = await axios.post(url, {
          conversation_id: conversationId
        }, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        
        return response.data
      } catch (error) {
        console.error('获取对话历史失败:', error)
        throw error
      } finally {
        chatStore.setLoadingStatus(false)
      }
    },

    /**
     * 上传文件
     * @param {File} file - 文件对象
     * @param {string} description - 文件描述
     * @returns {Promise} - 返回上传结果
     * 
     * 全局参数：令牌由请求拦截器自动添加
     */
    async uploadFile(file, description = '') {
      const apiConfig = useApiConfigStore()
      const url = apiConfig.getNestUrl('file', 'upload')
      
      // 创建表单数据
        const formData = new FormData()
        formData.append('file', file)
      if (description) {
        formData.append('description', description)
      }
        
      try {
        const response = await axios.post(url, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        
        return response.data
      } catch (error) {
        console.error('上传文件失败', error)
        throw error
      }
    },

    /**
     * 删除文件
     * @param {string} fileId - 文件ID
     * @returns {Promise} - 返回删除结果
     *
     * 全局参数：令牌由请求拦截器自动添加
     */
    async deleteFile(fileId) {
      const apiConfig = useApiConfigStore()
      const baseUrl = apiConfig.getNestUrl('file', 'delete')
      const url = `${baseUrl}/${fileId}`

      try {
        const response = await axios.delete(url)

        return response.data
      } catch (error) {
        console.error('删除文件失败', error)
        throw error
      }
    },

    /**
     * 发送消息（统一方法，支持流式和非流式）
     * @param {Object} params - { query, response_mode, conversation_id, model_name, enable_thinking, file_ids }
     * @param {Function} onMessage - 流式模式下的回调函数（可选）
     * @returns {Promise} - 非流式模式下返回响应数据，流式模式下无返回值
     * 
     * 全局参数：令牌由请求拦截器自动添加
     * 说明：当提供了onMessage回调时，使用流式模式；否则使用非流式模式
     */
    async sendMessage({ query, response_mode = 'streaming', conversation_id = '', model_name = '', enable_thinking = false, file_ids = [] }, onMessage = null) {
      const apiConfig = useApiConfigStore()
      const authStore = useAuthStore()
      const url = apiConfig.getNestUrl('chat', 'send_message')
      
      // 如果没有提供回调函数，使用非流式模式（axios）
      if (!onMessage) {
        try {
          const response = await axios.post(url, {
            query,
            response_mode,
            conversation_id,
            model_name,
            enable_thinking,
            file_ids
          }, {
            headers: {
              'Content-Type': 'application/json'
            }
          })
          return response.data
        } catch (error) {
          console.error('发送消息失败', error)
          throw error
        }
      }
      
      // 如果提供了回调函数，使用流式模式（fetch）
      try {
        const res = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authStore.access_token}`
          },
          body: JSON.stringify({ query, response_mode, conversation_id, model_name, enable_thinking, file_ids })
        })
        
        if (!res.ok) {
          throw new Error(`HTTP错误，状态码: ${res.status}`)
        }
        
        const reader = res.body.getReader()
        const decoder = new TextDecoder('utf-8')
        let buffer = ''
        
        while (true) {
          const { done, value } = await reader.read()
          if (done) break
          
          buffer += decoder.decode(value, { stream: true })
          let lines = buffer.split('\n')
          buffer = lines.pop() // 剩下的留给下次
          
          for (let line of lines) {
            if (line.startsWith('data: ')) {
              const json = line.slice(6)
              if (json === '[DONE]') return
              try {
                const data = JSON.parse(json)
                onMessage && onMessage(data)
              } catch (e) {
                console.error('解析流式响应失败:', e)
              }
            }
          }
        }
      } catch (error) {
        console.error('流式发送消息失败:', error)
        // 通知UI层出错了
        onMessage && onMessage({ error: true, message: error.message || '消息发送失败' })
        throw error
      }
    },

    /**
     * 获取智能体分类标签
     * @returns {Promise} - 返回分类标签数据
     * 
     * 全局参数：令牌由请求拦截器自动添加
     * 结果存储：agentStore.setCategories(tags)
     */
    async getAgentTags() {
      const apiConfig = useApiConfigStore()
      const agentStore = useAgentStore()
      const url = apiConfig.getNestUrl('agent', 'tags')
      
      try {
        const response = await axios.get(url)
        
        // 始终添加默认的"全部"标签
        const allTag = { id: 'all', name: '全部', active: true };
        
        // 检查返回的数据结构
        if (response.data && response.data.code === 200 && Array.isArray(response.data.data)) {
          // 如果数据不为空，则转换标签数据格式并存储到 agentStore
          if (response.data.data.length > 0) {
            const backendTags = response.data.data.map((tag, index) => ({
              id: tag,
              name: tag,
              active: false  // 默认都不激活，因为"全部"标签已激活
            }));
            
            // 合并"全部"标签和后端标签
            const tags = [allTag, ...backendTags];
            agentStore.setCategories(tags);
            return tags;
          } else {
            // 即使后端返回空数组，也至少添加"全部"标签
            agentStore.setCategories([allTag]);
            return [allTag];
          }
        } else {
          console.error('获取智能体分类标签数据格式错误', response.data);
          // 数据格式错误时也添加默认"全部"标签
          agentStore.setCategories([allTag]);
          return [allTag];
        }
      } catch (error) {
        console.error('获取智能体分类标签失败', error);
        // 请求失败时也添加默认"全部"标签
        agentStore.setCategories([allTag]);
        throw error;
      }
    },

    /**
     * 获取智能体列表
     * @param {Object} params - 查询参数 {page, limit, tag}
     * @returns {Promise} - 返回智能体列表数据，包括分页信息
     * 
     * 全局参数：令牌由请求拦截器自动添加
     * 结果存储：agentStore.setAgents(agents)
     */
    async getAgentList(params = { page: 1, limit: 8, tag: '' }) {
      const apiConfig = useApiConfigStore()
      const agentStore = useAgentStore()
      const url = apiConfig.getNestUrl('agent', 'list')
      
      try {
        agentStore.setLoadingStatus(true)
        const response = await axios.get(url, { params })
        
        // 检查返回的数据结构
        if (response.data && response.data.code === 200 && response.data.data) {
          const responseData = response.data.data;
          
          // 存储智能体列表数据
          if (Array.isArray(responseData.items)) {
            agentStore.setAgents(responseData.items)
          } else {
            agentStore.setAgents([])
          }
          
          // 返回完整的分页数据
          return {
            items: responseData.items || [],
            total: responseData.total || 0,
            page: responseData.page || params.page,
            limit: responseData.limit || params.limit,
            has_more: responseData.has_more || false
          };
        } else {
          console.error('获取智能体列表数据格式错误', response.data)
          agentStore.setAgents([])
          return { items: [], total: 0, page: params.page, limit: params.limit, has_more: false }
        }
      } catch (error) {
        console.error('获取智能体列表失败', error)
        agentStore.setAgents([])
        throw error
      } finally {
        agentStore.setLoadingStatus(false)
      }
    },

    /**
     * 获取智能体详情
     * @param {string} agentId - 智能体ID
     * @returns {Promise} - 返回智能体详情数据
     * 
     * 全局参数：由请求拦截器自动处理
     */
    async getAgentDetail(agentId) {
      const apiConfig = useApiConfigStore()
      // 修复URL路径,api接口需要直接使用agent/{id}而不是agent/detail/{id}
      const url = `${apiConfig.baseUrl}/agent/${agentId}`
      
      try {
        const response = await axios.get(url)
        
        // 检查返回的数据结构
        if (response.data && response.data.code === 200 && response.data.data) {
          return response.data.data
        } else {
          console.error('获取智能体详情数据格式错误', response.data)
          return null
        }
      } catch (error) {
        console.error('获取智能体详情失败', error)
        throw error
      }
    },

    /**
     * 获取智能体参数信息
     * @param {string} agentId - 智能体ID
     * @returns {Promise} - 返回智能体参数信息，包含user_input_form等
     *
     * 全局参数：由请求拦截器自动处理
     */
    async getAgentParameters(agentId) {
      const apiConfig = useApiConfigStore()
      const url = `${apiConfig.baseUrl}/agent/${agentId}/parameters`

      try {
        const response = await axios.get(url)

        // 检查返回的数据结构
        if (response.data && response.data.code === 200) {
          return response.data.data
        } else {
          console.error('获取智能体参数信息数据格式错误', response.data)
          return null
        }
      } catch (error) {
        console.error('获取智能体参数信息失败', error)
        throw error
      }
    },

    /**
     * 发送智能体聊天请求（非流式）
     * @param {string} agent_id - 智能体ID
     * @param {string} query - 用户消息内容
     * @param {string} response_mode - 响应模式，默认为'streaming'
     * @param {string} conversation_id - 会话ID，为空则创建新会话
     * @param {Object} inputs - 其他输入参数
     * @param {Array} files - 文件ID列表
     * @returns {Promise} - 返回聊天响应数据
     * 
     * 全局参数：令牌由请求拦截器自动添加
     */
    async sendAgentChat({ agent_id, query, response_mode = 'streaming', conversation_id = '', inputs = {}, files = [] }) {
      const apiConfig = useApiConfigStore()
      const url = apiConfig.getNestUrl('agent', 'chat')
      try {
        const response = await axios.post(url, {
          agent_id,
          query,
          response_mode,
          conversation_id,
          inputs,
          files
        }, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        return response.data
      } catch (error) {
        console.error('发送智能体消息失败', error)
        throw error
      }
    },

    /**
     * 流式发送智能体聊天请求
     * @param {Object} params - { agent_id, query, response_mode, conversation_id, inputs, files }
     * @param {Function} onMessage - 每收到一片消息的回调
     * 
     * 全局参数：从 authStore 获取 access_token（由于使用fetch API，需要手动添加）
     */
    async sendAgentChatStream({ agent_id, query, response_mode = 'streaming', conversation_id = '', inputs = {}, files = [] }, onMessage) {
      const apiConfig = useApiConfigStore()
      const authStore = useAuthStore()
      const url = apiConfig.getNestUrl('agent', 'chat')
      
      try {
        const res = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authStore.access_token}`
          },
          body: JSON.stringify({ agent_id, query, response_mode, conversation_id, inputs, files })
        })
        
        if (!res.ok) {
          throw new Error(`智能体响应错误，状态码: ${res.status}`)
        }
        
        const reader = res.body.getReader()
        const decoder = new TextDecoder('utf-8')
        let buffer = ''
        
        while (true) {
          const { done, value } = await reader.read()
          if (done) break
          
          buffer += decoder.decode(value, { stream: true })
          let lines = buffer.split('\n')
          buffer = lines.pop() // 剩下的留给下次
          
          for (let line of lines) {
            if (line.startsWith('data: ')) {
              const json = line.slice(6)
              if (json === '[DONE]') return
              
              try {
                const data = JSON.parse(json)
                onMessage && onMessage(data)
              } catch (e) {
                console.error('解析智能体流式响应失败:', e)
              }
            }
          }
        }
      } catch (error) {
        console.error('智能体流式请求失败:', error)
        // 通知UI层出错了
        onMessage && onMessage({ error: true, message: error.message || '智能体响应失败' })
        throw error
      }
    },

    // 后续可根据项目需求添加更多接口
  }
}) 