"""聊天API响应模型"""
from flask_restx import fields, Namespace

# 创建一个临时命名空间用于定义模型
_temp_ns = Namespace('chat_schemas')

# 聊天消息响应模型
chat_message_response_model = _temp_ns.model('ChatMessageResponse', {
    'id': fields.Integer(required=True, description="消息ID"),
    'conversation_id': fields.String(required=True, description="对话ID"),
    'channel_id': fields.Integer(required=True, description="渠道ID"),
    'role': fields.String(required=True, description="角色（user/assistant）"),
    'content': fields.String(required=True, description="消息内容"),
    'model_name': fields.String(required=True, description="模型名称"),
    'created_at': fields.DateTime(required=True, description="创建时间")
})

# 对话响应模型
conversation_response_model = _temp_ns.model('ConversationResponse', {
    'conversation_id': fields.String(required=True, description="对话ID"),
    'title': fields.String(required=True, description="对话标题"),
    'created_at': fields.DateTime(required=True, description="创建时间"),
    'updated_at': fields.DateTime(required=True, description="更新时间")
})

# 对话历史响应模型
history_response_model = _temp_ns.model('HistoryResponse', {
    'messages': fields.List(fields.Nested(chat_message_response_model), description="消息列表")
})

# 模型信息响应模型
model_info_response_model = _temp_ns.model('ModelInfoResponse', {
    'model_name': fields.String(required=True, description="模型名称"),
    'model_id': fields.Integer(required=True, description="模型ID"),
    'channel_id': fields.Integer(required=True, description="渠道ID"),
    'channel_name': fields.String(required=True, description="渠道名称"),
    'channel_type': fields.String(required=True, description="渠道类型")
})

# 模型列表响应模型
model_list_response_model = _temp_ns.model('ModelListResponse', {
    'models': fields.List(fields.Nested(model_info_response_model), description="模型列表")
})

# 消息响应模型（非流式响应）
message_response_model = _temp_ns.model('MessageResponse', {
    'content': fields.String(required=False, description="响应内容"),
    'error': fields.String(required=False, description="错误信息"),
    'status': fields.String(required=True, description="状态：generating/completed/error"),
    'conversation_id': fields.String(required=True, description="对话ID"),
    'title': fields.String(required=True, description="对话标题")
})

# 简单文件响应模型，用于嵌套在其他模型中
file_brief_response_model = _temp_ns.model('FileBriefResponse', {
    'file_id': fields.String(description='文件ID'),
    'filename': fields.String(description='文件名'),
    'file_type': fields.String(description='文件类型'),
    'file_size': fields.Integer(description='文件大小(字节)'),
    'upload_time': fields.DateTime(description='上传时间'),
    'status': fields.String(description='处理状态'),
    'description': fields.String(description='文件描述')
})

# 对话关联的文件列表响应模型
conversation_files_response_model = _temp_ns.model('ConversationFilesResponse', {
    'conversation_id': fields.String(required=True),
    'total': fields.Integer(required=True),
    'files': fields.List(fields.Nested(file_brief_response_model))
}) 