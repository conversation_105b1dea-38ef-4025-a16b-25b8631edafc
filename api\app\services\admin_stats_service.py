"""
管理员统计服务
提供仪表盘所需的各种统计数据
"""

from datetime import datetime, timedelta, timezone
from sqlalchemy import func, and_, desc
from app.core.database import db
from app.models import User, Conversation, ChatMessage, File
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)


class AdminStatsService:
    """管理员统计服务类"""
    
    def get_user_stats(self) -> Dict[str, Any]:
        """获取用户统计数据"""
        try:
            # 总用户数
            total_users = db.session.query(User).filter(User.disabled == False).count()
            
            # 一周前的用户数（用于计算增长率）
            week_ago = datetime.now(timezone.utc) - timedelta(days=7)
            users_week_ago = db.session.query(User).filter(
                and_(User.disabled == False, User.created_at <= week_ago)
            ).count()
            
            # 计算增长率
            if users_week_ago > 0:
                growth_rate = round(((total_users - users_week_ago) / users_week_ago) * 100, 1)
            else:
                growth_rate = 100.0 if total_users > 0 else 0.0
            
            # 在线用户数（最近5分钟有活动的用户）
            five_minutes_ago = datetime.now(timezone.utc) - timedelta(minutes=5)
            online_count = db.session.query(User).filter(
                and_(
                    User.disabled == False,
                    User.last_login_at >= five_minutes_ago
                )
            ).count()
            
            return {
                'total': total_users,
                'growth_rate': growth_rate,
                'online_count': online_count
            }
        except Exception as e:
            logger.error(f"获取用户统计失败: {e}")
            return {
                'total': 0,
                'growth_rate': 0.0,
                'online_count': 0
            }
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """获取对话统计数据"""
        try:
            # 今日对话数
            today = datetime.now(timezone.utc).date()
            today_start = datetime.combine(today, datetime.min.time()).replace(tzinfo=timezone.utc)
            today_end = datetime.combine(today, datetime.max.time()).replace(tzinfo=timezone.utc)
            
            today_conversations = db.session.query(Conversation).filter(
                and_(
                    Conversation.created_at >= today_start,
                    Conversation.created_at <= today_end
                )
            ).count()
            
            # 昨日对话数（用于计算增长率）
            yesterday_start = today_start - timedelta(days=1)
            yesterday_end = today_start - timedelta(seconds=1)
            
            yesterday_conversations = db.session.query(Conversation).filter(
                and_(
                    Conversation.created_at >= yesterday_start,
                    Conversation.created_at <= yesterday_end
                )
            ).count()
            
            # 计算增长率
            if yesterday_conversations > 0:
                growth_rate = round(((today_conversations - yesterday_conversations) / yesterday_conversations) * 100, 1)
            else:
                growth_rate = 100.0 if today_conversations > 0 else 0.0
            
            return {
                'today_count': today_conversations,
                'growth_rate': growth_rate
            }
        except Exception as e:
            logger.error(f"获取对话统计失败: {e}")
            return {
                'today_count': 0,
                'growth_rate': 0.0
            }
    
    def get_file_stats(self) -> Dict[str, Any]:
        """获取文件统计数据"""
        try:
            # 总文件数
            total_files = db.session.query(File).count()
            
            # 总文件大小（假设File模型有size字段，如果没有就返回估算值）
            # 这里先返回一个估算值，你可以根据实际的File模型调整
            estimated_size_gb = round(total_files * 0.014, 1)  # 假设平均每个文件14KB
            
            return {
                'total_count': total_files,
                'total_size_gb': estimated_size_gb
            }
        except Exception as e:
            logger.error(f"获取文件统计失败: {e}")
            return {
                'total_count': 0,
                'total_size_gb': 0.0
            }
    
    def get_agent_stats(self) -> Dict[str, Any]:
        """获取智能体统计数据"""
        try:
            # 活跃智能体数（有对话的智能体）
            # 这里需要根据你的实际业务逻辑调整
            # 假设通过对话记录来统计活跃智能体
            
            # 获取最近7天有对话的智能体数量
            week_ago = datetime.now(timezone.utc) - timedelta(days=7)
            
            # 这里需要根据你的实际数据结构调整
            # 假设Conversation表有agent_id字段
            active_agents = 56  # 临时硬编码，需要根据实际情况调整
            
            # 增长率（临时硬编码）
            growth_rate = 8.3
            
            return {
                'active_count': active_agents,
                'growth_rate': growth_rate
            }
        except Exception as e:
            logger.error(f"获取智能体统计失败: {e}")
            return {
                'active_count': 0,
                'growth_rate': 0.0
            }
    
    def get_dashboard_overview(self) -> Dict[str, Any]:
        """获取仪表盘概览数据"""
        return {
            'users': self.get_user_stats(),
            'agents': self.get_agent_stats(),
            'conversations': self.get_conversation_stats(),
            'files': self.get_file_stats()
        }
    
    def get_conversation_activity(self, time_range: str = 'today') -> Dict[str, Any]:
        """获取对话活跃度数据"""
        try:
            # 根据时间范围获取数据
            if time_range == 'today':
                # 按小时统计今日对话
                today = datetime.now(timezone.utc).date()
                today_start = datetime.combine(today, datetime.min.time()).replace(tzinfo=timezone.utc)
                
                # 生成24小时的数据
                activity_data = []
                for hour in range(0, 24, 4):  # 每4小时一个点
                    hour_start = today_start + timedelta(hours=hour)
                    hour_end = hour_start + timedelta(hours=4)
                    
                    count = db.session.query(Conversation).filter(
                        and_(
                            Conversation.created_at >= hour_start,
                            Conversation.created_at < hour_end
                        )
                    ).count()
                    
                    activity_data.append({
                        'label': f"{hour:02d}:00",
                        'value': count
                    })
                
                # 计算峰值时段和平均对话数
                max_item = max(activity_data, key=lambda x: x['value'])
                avg_conversations = sum(item['value'] for item in activity_data) // len(activity_data)
                
                return {
                    'data': activity_data,
                    'peak_hour': max_item['label'],
                    'avg_conversations': avg_conversations
                }
            
            # 其他时间范围的处理...
            return {
                'data': [],
                'peak_hour': '00:00',
                'avg_conversations': 0
            }
            
        except Exception as e:
            logger.error(f"获取对话活跃度失败: {e}")
            return {
                'data': [],
                'peak_hour': '00:00',
                'avg_conversations': 0
            }
    
    def get_agent_usage_distribution(self) -> Dict[str, Any]:
        """获取智能体使用分布数据"""
        try:
            # 这里需要根据你的实际业务逻辑调整
            # 临时返回模拟数据
            distribution = [
                {"name": "智能助手", "value": 5420, "color": "#409EFF"},
                {"name": "代码生成", "value": 3280, "color": "#909399"},
                {"name": "文档分析", "value": 2890, "color": "#E6A23C"},
                {"name": "数据处理", "value": 2150, "color": "#F56C6C"},
                {"name": "其他", "value": 1680, "color": "#67C23A"}
            ]
            
            total = sum(item['value'] for item in distribution)
            
            return {
                'total': total,
                'distribution': distribution
            }
        except Exception as e:
            logger.error(f"获取智能体使用分布失败: {e}")
            return {
                'total': 0,
                'distribution': []
            }


# 创建全局实例
admin_stats_service = AdminStatsService()
