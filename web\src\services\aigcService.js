/**
 * AIGC工具坊相关的API服务
 */
import axios from 'axios'
import { useApiConfigStore } from '@/stores/apiConfig'

const apiConfig = useApiConfigStore()

/**
 * 获取分类列表
 * @param {boolean} isActive - 是否只获取启用的分类
 * @returns {Promise} 分类列表
 */
export const getCategories = async (isActive = null) => {
  try {
    const params = {}
    if (isActive !== null) {
      params.is_active = isActive
    }

    const response = await axios.get(apiConfig.getNestUrl('aigc', 'categories'), { params })
    return response.data
  } catch (error) {
    console.error('获取分类列表失败:', error)
    throw error
  }
}

/**
 * 获取工具列表
 * @param {Object} options - 查询选项
 * @returns {Promise} 工具列表和分页信息
 */
export const getTools = async (options = {}) => {
  try {
    const {
      categoryId,
      isActive = true,
      isHot,
      search,
      page = 1,
      perPage = 20,
      sortBy = 'sort_order'
    } = options

    const params = {
      page,
      per_page: perPage,
      sort_by: sortBy
    }

    if (categoryId) params.category_id = categoryId
    if (isActive !== null) params.is_active = isActive
    if (isHot !== null) params.is_hot = isHot
    if (search) params.search = search

    const response = await axios.get(apiConfig.getNestUrl('aigc', 'tools'), { params })
    return response.data
  } catch (error) {
    console.error('获取工具列表失败:', error)
    throw error
  }
}

/**
 * 获取工具详情
 * @param {number} toolId - 工具ID
 * @returns {Promise} 工具详情
 */
export const getToolById = async (toolId) => {
  try {
    const url = apiConfig.getNestUrl('aigc', 'tool_detail').replace(':id', toolId)
    const response = await axios.get(url)
    return response.data
  } catch (error) {
    console.error('获取工具详情失败:', error)
    throw error
  }
}

/**
 * 记录工具点击
 * @param {number} toolId - 工具ID
 * @returns {Promise} 点击记录结果
 */
export const recordClick = async (toolId) => {
  try {
    const url = apiConfig.getNestUrl('aigc', 'tool_click').replace(':id', toolId)
    const response = await axios.post(url)
    return response.data
  } catch (error) {
    console.error('记录工具点击失败:', error)
    // 点击记录失败不影响用户体验，静默处理
    return null
  }
}

/**
 * 获取用户收藏列表
 * @param {Object} options - 查询选项
 * @returns {Promise} 收藏列表和分页信息
 */
export const getFavorites = async (options = {}) => {
  try {
    const { page = 1, perPage = 20 } = options

    const params = {
      page,
      per_page: perPage
    }

    const response = await axios.get(apiConfig.getNestUrl('aigc', 'favorites'), { params })
    return response.data
  } catch (error) {
    console.error('获取收藏列表失败:', error)
    throw error
  }
}

/**
 * 切换工具收藏状态
 * @param {number} toolId - 工具ID
 * @returns {Promise} 收藏操作结果
 */
export const toggleFavorite = async (toolId) => {
  try {
    const url = apiConfig.getNestUrl('aigc', 'toggle_favorite').replace(':id', toolId)
    const response = await axios.post(url)
    return response.data
  } catch (error) {
    console.error('切换收藏状态失败:', error)
    throw error
  }
}

/**
 * 获取统计数据
 * @returns {Promise} 统计数据
 */
export const getStats = async () => {
  try {
    const response = await axios.get(apiConfig.getNestUrl('aigc', 'stats'))
    return response.data
  } catch (error) {
    console.error('获取统计数据失败:', error)
    throw error
  }
}

// ========== 管理员功能 ==========

/**
 * 创建分类
 * @param {Object} categoryData - 分类数据
 * @returns {Promise} 创建的分类
 */
export const createCategory = async (categoryData) => {
  try {
    const response = await axios.post(apiConfig.getNestUrl('aigc', 'categories'), categoryData)
    return response.data
  } catch (error) {
    console.error('创建分类失败:', error)
    throw error
  }
}

/**
 * 更新分类
 * @param {number} categoryId - 分类ID
 * @param {Object} categoryData - 分类数据
 * @returns {Promise} 更新的分类
 */
export const updateCategory = async (categoryId, categoryData) => {
  try {
    const url = `${apiConfig.getNestUrl('aigc', 'categories')}/${categoryId}`
    const response = await axios.put(url, categoryData)
    return response.data
  } catch (error) {
    console.error('更新分类失败:', error)
    throw error
  }
}

/**
 * 删除分类
 * @param {number} categoryId - 分类ID
 * @returns {Promise} 删除结果
 */
export const deleteCategory = async (categoryId) => {
  try {
    const url = `${apiConfig.getNestUrl('aigc', 'categories')}/${categoryId}`
    const response = await axios.delete(url)
    return response.data
  } catch (error) {
    console.error('删除分类失败:', error)
    throw error
  }
}

/**
 * 创建工具
 * @param {Object} toolData - 工具数据
 * @returns {Promise} 创建的工具
 */
export const createTool = async (toolData) => {
  try {
    const response = await axios.post(apiConfig.getNestUrl('aigc', 'tools'), toolData)
    return response.data
  } catch (error) {
    console.error('创建工具失败:', error)
    throw error
  }
}

/**
 * 更新工具
 * @param {number} toolId - 工具ID
 * @param {Object} toolData - 工具数据
 * @returns {Promise} 更新的工具
 */
export const updateTool = async (toolId, toolData) => {
  try {
    const url = `${apiConfig.getNestUrl('aigc', 'tools')}/${toolId}`
    const response = await axios.put(url, toolData)
    return response.data
  } catch (error) {
    console.error('更新工具失败:', error)
    throw error
  }
}

/**
 * 删除工具
 * @param {number} toolId - 工具ID
 * @returns {Promise} 删除结果
 */
export const deleteTool = async (toolId) => {
  try {
    const url = `${apiConfig.getNestUrl('aigc', 'tools')}/${toolId}`
    const response = await axios.delete(url)
    return response.data
  } catch (error) {
    console.error('删除工具失败:', error)
    throw error
  }
}

// ========== 图标管理功能 ==========

/**
 * 上传工具图标
 * @param {number} toolId - 工具ID
 * @param {File} file - 图标文件
 * @param {Function} onProgress - 上传进度回调
 * @returns {Promise} 上传结果
 */
export const uploadIcon = async (toolId, file, onProgress = null) => {
  try {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('tool_id', toolId)

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }

    // 如果提供了进度回调，添加上传进度监听
    if (onProgress && typeof onProgress === 'function') {
      config.onUploadProgress = (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(percentCompleted)
      }
    }

    const response = await axios.post(apiConfig.getNestUrl('aigc', 'upload_icon'), formData, config)
    return response.data
  } catch (error) {
    console.error('上传图标失败:', error)
    throw error
  }
}

/**
 * 删除工具图标
 * @param {number} toolId - 工具ID
 * @returns {Promise} 删除结果
 */
export const deleteIcon = async (toolId) => {
  try {
    const url = apiConfig.getNestUrl('aigc', 'tool_icon', { id: toolId })
    const response = await axios.delete(url)
    return response.data
  } catch (error) {
    console.error('删除图标失败:', error)
    throw error
  }
}

/**
 * 获取工具图标URL
 * @param {number} toolId - 工具ID
 * @returns {string} 图标URL
 */
export const getIconUrl = (toolId) => {
  if (!toolId) return ''
  return apiConfig.getNestUrl('aigc', 'tool_icon', { id: toolId })
}

/**
 * 切换图标类型
 * @param {number} toolId - 工具ID
 * @param {string} iconType - 图标类型 ('emoji' | 'image')
 * @param {Object} additionalData - 额外数据（如emoji内容）
 * @returns {Promise} 切换结果
 */
export const switchIconType = async (toolId, iconType, additionalData = {}) => {
  try {
    const updateData = {
      icon_type: iconType,
      ...additionalData
    }

    // 如果切换到emoji类型，清除图片URL
    if (iconType === 'emoji') {
      updateData.icon_image_url = null
    }

    const response = await updateTool(toolId, updateData)
    return response
  } catch (error) {
    console.error('切换图标类型失败:', error)
    throw error
  }
}

/**
 * 验证图标文件
 * @param {File} file - 图标文件
 * @returns {Object} 验证结果
 */
export const validateIconFile = (file) => {
  const result = {
    valid: true,
    errors: []
  }

  // 检查文件类型
  const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp']
  if (!allowedTypes.includes(file.type)) {
    result.valid = false
    result.errors.push('文件类型不支持，请选择 PNG、JPG、GIF 或 WebP 格式的图片')
  }

  // 检查文件大小（2MB限制）
  const maxSize = 2 * 1024 * 1024 // 2MB
  if (file.size > maxSize) {
    result.valid = false
    result.errors.push('文件大小不能超过 2MB')
  }

  // 检查文件名
  if (!file.name || file.name.trim() === '') {
    result.valid = false
    result.errors.push('文件名不能为空')
  }

  return result
}
