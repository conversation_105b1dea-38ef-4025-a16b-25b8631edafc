"""
AIGC工具坊数据模型
包含工具、分类、用户收藏等相关模型
"""

from datetime import datetime, timezone
from app import db


class AigcCategory(db.Model):
    """AIGC工具分类模型"""
    __tablename__ = 'aigc_categories'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 基本信息
    name = db.Column(
        db.String(50), 
        nullable=False, 
        index=True,
        comment='分类名称'
    )
    description = db.Column(
        db.Text, 
        nullable=True,
        comment='分类描述'
    )
    icon = db.Column(
        db.String(10), 
        nullable=True,
        comment='分类图标(emoji)'
    )
    color = db.Column(
        db.String(7), 
        nullable=True, 
        default='#1677ff',
        comment='主题色'
    )
    
    # 排序和状态
    sort_order = db.Column(
        db.Integer, 
        nullable=False, 
        default=0,
        comment='排序权重，数值越大排序越靠前'
    )
    is_active = db.Column(
        db.<PERSON><PERSON>, 
        nullable=False, 
        default=True,
        comment='是否启用'
    )
    
    # 时间戳
    created_at = db.Column(
        db.DateTime, 
        nullable=False, 
        default=lambda: datetime.now(timezone.utc),
        comment='创建时间'
    )
    updated_at = db.Column(
        db.DateTime, 
        nullable=False, 
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        comment='更新时间'
    )
    
    # 关系
    tools = db.relationship('AigcTool', backref='category_obj', lazy='dynamic')
    
    def __repr__(self):
        return f'<AigcCategory {self.name}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'icon': self.icon,
            'color': self.color,
            'sort_order': self.sort_order,
            'is_active': self.is_active,
            'tool_count': self.tools.filter_by(is_active=True).count(),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class AigcTool(db.Model):
    """AIGC工具模型"""
    __tablename__ = 'aigc_tools'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 基本信息
    name = db.Column(
        db.String(100), 
        nullable=False, 
        index=True,
        comment='工具名称'
    )
    description = db.Column(
        db.Text, 
        nullable=False,
        comment='工具描述'
    )
    url = db.Column(
        db.String(500), 
        nullable=False,
        comment='工具链接'
    )
    icon = db.Column(
        db.String(10),
        nullable=True,
        comment='工具图标(emoji)'
    )
    icon_type = db.Column(
        db.String(10),
        nullable=False,
        default='emoji',
        comment='图标类型(emoji|image)'
    )
    icon_image_url = db.Column(
        db.String(500),
        nullable=True,
        comment='图片图标URL'
    )
    
    # 分类和标签
    category_id = db.Column(
        db.Integer, 
        db.ForeignKey('aigc_categories.id'), 
        nullable=False,
        index=True,
        comment='分类ID'
    )
    tags = db.Column(
        db.JSON, 
        nullable=True,
        comment='标签列表'
    )
    
    # 状态和属性
    is_active = db.Column(
        db.Boolean, 
        nullable=False, 
        default=True,
        comment='是否启用'
    )
    is_hot = db.Column(
        db.Boolean, 
        nullable=False, 
        default=False,
        comment='是否热门'
    )
    
    # 统计数据
    click_count = db.Column(
        db.Integer, 
        nullable=False, 
        default=0,
        comment='点击次数'
    )
    favorite_count = db.Column(
        db.Integer, 
        nullable=False, 
        default=0,
        comment='收藏次数'
    )
    
    # 排序权重
    sort_order = db.Column(
        db.Integer, 
        nullable=False, 
        default=0,
        comment='排序权重'
    )
    
    # 时间戳
    created_at = db.Column(
        db.DateTime, 
        nullable=False, 
        default=lambda: datetime.now(timezone.utc),
        comment='创建时间'
    )
    updated_at = db.Column(
        db.DateTime, 
        nullable=False, 
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        comment='更新时间'
    )
    
    # 关系
    favorites = db.relationship('AigcUserFavorite', backref='tool_obj', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<AigcTool {self.name}>'
    
    def to_dict(self, user_id=None):
        """转换为字典"""
        result = {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'url': self.url,
            'icon': self.icon,
            'icon_type': self.icon_type,
            'icon_image_url': self.icon_image_url,
            'category_id': self.category_id,
            'category': self.category_obj.name if self.category_obj else None,
            'tags': self.tags or [],
            'is_active': self.is_active,
            'is_hot': self.is_hot,
            'click_count': self.click_count,
            'favorite_count': self.favorite_count,
            'sort_order': self.sort_order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        # 如果提供了用户ID，检查是否已收藏
        if user_id:
            result['is_favorite'] = self.favorites.filter_by(user_id=user_id).first() is not None
        
        return result


class AigcUserFavorite(db.Model):
    """用户收藏工具模型"""
    __tablename__ = 'aigc_user_favorites'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联信息
    user_id = db.Column(
        db.Integer, 
        db.ForeignKey('User.user_id'), 
        nullable=False,
        index=True,
        comment='用户ID'
    )
    tool_id = db.Column(
        db.Integer, 
        db.ForeignKey('aigc_tools.id'), 
        nullable=False,
        index=True,
        comment='工具ID'
    )
    
    # 时间戳
    created_at = db.Column(
        db.DateTime, 
        nullable=False, 
        default=lambda: datetime.now(timezone.utc),
        comment='收藏时间'
    )
    
    # 唯一约束：一个用户对同一个工具只能收藏一次
    __table_args__ = (
        db.UniqueConstraint('user_id', 'tool_id', name='uk_user_tool_favorite'),
    )
    
    # 关系
    user = db.relationship('User', backref='aigc_favorites')
    
    def __repr__(self):
        return f'<AigcUserFavorite user_id={self.user_id} tool_id={self.tool_id}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'tool_id': self.tool_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class AigcClickLog(db.Model):
    """工具点击日志模型"""
    __tablename__ = 'aigc_click_logs'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联信息
    user_id = db.Column(
        db.Integer, 
        db.ForeignKey('User.user_id'), 
        nullable=True,
        index=True,
        comment='用户ID（可为空，支持匿名点击）'
    )
    tool_id = db.Column(
        db.Integer, 
        db.ForeignKey('aigc_tools.id'), 
        nullable=False,
        index=True,
        comment='工具ID'
    )
    
    # 点击信息
    ip_address = db.Column(
        db.String(45), 
        nullable=True,
        comment='IP地址'
    )
    user_agent = db.Column(
        db.Text, 
        nullable=True,
        comment='用户代理'
    )
    
    # 时间戳
    created_at = db.Column(
        db.DateTime, 
        nullable=False, 
        default=lambda: datetime.now(timezone.utc),
        index=True,
        comment='点击时间'
    )
    
    # 关系
    user = db.relationship('User', backref='aigc_clicks')
    tool = db.relationship('AigcTool', backref='click_logs')
    
    def __repr__(self):
        return f'<AigcClickLog tool_id={self.tool_id} user_id={self.user_id}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'tool_id': self.tool_id,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
