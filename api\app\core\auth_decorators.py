"""
新的认证装饰器
使用新的JWT管理器替换flask_jwt_extended
"""

import functools
import logging
from flask import request, jsonify, g
from sqlalchemy.exc import IntegrityError
from app.core.jwt_manager import jwt_manager, TokenError
from app.services.auth_service import auth_service
from app.utils.errors import AuthError, PermissionDenied, UserBanned

logger = logging.getLogger(__name__)


def jwt_required():
    """
    新的JWT认证装饰器
    替换flask_jwt_extended的@jwt_required()
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # 获取Authorization头
                auth_header = request.headers.get('Authorization')
                if not auth_header or not auth_header.startswith('Bearer '):
                    logger.warning("缺少或无效的Authorization头")
                    return jsonify({
                        'error': 'invalid_token',
                        'error_description': '缺少或无效的Authorization头',
                        'message': '请提供有效的访问令牌'
                    }), 401

                # 提取访问令牌
                access_token = auth_header[7:]  # 移除 "Bearer " 前缀

                # 验证访问令牌
                payload = jwt_manager.decode_access_token(access_token)

                # 将用户信息存储到request context中
                request.current_user_id = payload['user_id']
                request.current_username = payload.get('username')
                request.current_user_role = payload.get('role')
                request.jwt_payload = payload

                logger.debug(f"JWT验证成功 - 用户ID: {payload['user_id']}")

            except TokenError as e:
                logger.warning(f"JWT验证失败: {e}")
                return jsonify({
                    'error': 'invalid_token',
                    'error_description': str(e),
                    'message': '访问令牌无效或已过期'
                }), 401

            except PermissionDenied as e:
                logger.warning(f"权限验证失败: {e}")
                return jsonify({
                    'error': 'permission_denied',
                    'error_description': str(e),
                    'message': '权限不足'
                }), 403

            except UserBanned as e:
                logger.warning(f"用户已被禁用: {e}")
                return jsonify({
                    'error': 'user_banned',
                    'error_description': str(e),
                    'message': '用户已被禁用'
                }), 403

            except Exception as e:
                logger.error(f"JWT验证过程中发生错误: {e}")
                return jsonify({
                    'error': 'server_error',
                    'error_description': '服务器内部错误',
                    'message': '令牌验证失败'
                }), 500

            # JWT验证成功，执行业务逻辑
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def get_jwt_identity():
    """
    获取当前JWT令牌中的用户身份
    替换flask_jwt_extended的get_jwt_identity()
    """
    if hasattr(request, 'current_user_id'):
        return str(request.current_user_id)
    return None


def get_current_user():
    """
    获取当前用户对象
    """
    if hasattr(request, 'current_user_id'):
        return auth_service.get_current_user_by_id(request.current_user_id)
    return None


def admin_required():
    """
    管理员权限装饰器
    需要先通过JWT认证，然后验证用户是否为管理员
    """
    def decorator(f):
        @functools.wraps(f)
        @jwt_required()  # 先进行JWT认证
        def decorated_function(*args, **kwargs):
            try:
                # 获取当前用户角色（JWT认证后会设置到request中）
                user_role = getattr(request, 'current_user_role', None)

                # 检查是否为管理员
                if user_role != 'admin':
                    logger.warning(f"非管理员用户尝试访问管理接口 - 用户ID: {getattr(request, 'current_user_id', 'unknown')}, 角色: {user_role}")
                    return jsonify({
                        'error': 'permission_denied',
                        'error_description': '需要管理员权限',
                        'message': '您没有权限访问此资源'
                    }), 403

                logger.debug(f"管理员权限验证成功 - 用户ID: {getattr(request, 'current_user_id', 'unknown')}")

            except Exception as e:
                logger.error(f"管理员权限验证过程中发生错误: {e}")
                return jsonify({
                    'error': 'server_error',
                    'error_description': '服务器内部错误',
                    'message': '权限验证失败'
                }), 500

            # 权限验证成功，执行业务逻辑
            return f(*args, **kwargs)

        return decorated_function
    return decorator


def get_current_admin():
    """
    获取当前管理员用户信息
    只有在通过admin_required装饰器验证后才能调用
    """
    if hasattr(request, 'current_user_id') and hasattr(request, 'current_user_role'):
        if request.current_user_role == 'admin':
            return {
                'user_id': request.current_user_id,
                'username': getattr(request, 'current_username', None),
                'role': request.current_user_role
            }
    return None


def get_jwt_payload():
    """
    获取当前JWT令牌的完整载荷
    """
    if hasattr(request, 'jwt_payload'):
        return request.jwt_payload
    return None
