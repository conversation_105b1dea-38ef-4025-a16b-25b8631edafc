<template>
  <aside class="sidebar">
    <div class="sidebar-header">
      <div class="avatar-logo">
        <img src="../assets/svg/logo.svg" alt="普米智能体" class="logo-img" draggable="false" />
      <div class="logo-text">
        <h1 class="logo-title">普米智能体</h1>
          <p class="logo-subtitle">让学习与工作更轻松</p>
        </div>
      </div>

      <!-- 使用SidebarMenu组件 -->
      <SidebarMenu :active-menu="activeMenu" :has-user-sent-message="hasUserSentMessage"
        @create-new-chat="$emit('create-new-chat')" @menu-select="handleMenuSelect" />
    </div>

    <!-- 历史对话列表区域 -->
    <div class="sidebar-content">
      <!-- 使用HistoryList组件 -->
      <HistoryList :chat-list="chatStore.conversations" @select-chat="selectChat" />
    </div>
  </aside>
</template>

<script setup>
import { useChatStore } from '@/stores/chatStore';
import SidebarMenu from '@/components/Sidebar/SidebarMenu.vue'
import HistoryList from '@/components/Sidebar/HistoryList.vue'
import { ref, computed, onMounted } from 'vue';
import { useApiStore } from '@/stores/apiStore';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';

const chatStore = useChatStore();
const apiStore = useApiStore();
const router = useRouter();

// 在组件挂载时获取历史对话列表并设置正确的活动菜单
onMounted(async () => {
  try {
    await apiStore.getConversations();
    
    // 根据当前路由设置活动菜单
    const route = router.currentRoute.value;
    if (route.path.includes('/chat/agent-plaza')) {
      activeMenu.value = 'agent-plaza';
    } else if (route.path.includes('/chat')) {
      activeMenu.value = 'chat';
    }
  } catch (error) {
    ElMessage.error('获取历史对话列表失败');
  }
});

// 菜单状态
const activeMenu = ref('chat'); // 默认选中"聊天"菜单

// 判断用户是否发送过消息
const hasUserSentMessage = computed(() => {
  return chatStore.currentHistory.some(msg => msg.role === 'user');
});

// 处理菜单选择事件
const handleMenuSelect = (menu) => {
  activeMenu.value = menu;
};

// 处理选择历史对话的事件
const selectChat = async (id) => {
  try {
    // 更新路由，显示选中的对话 ID
    router.push({ path: `/chat/${id}` });
    
    // 加载对话历史
    await chatStore.loadConversationHistory(id);
  } catch (error) {
    ElMessage.error('加载对话历史失败');
  }
}
</script>

<style scoped lang="scss">
.sidebar {
  width: 280px;
  background: #fafbfc;
  border-right: 1px solid #ececec;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.03);
  height: 100vh;
  /* 确保侧边栏高度为整个视口高度 */
}

.sidebar-header {
  flex-shrink: 0;
  /* 不收缩 */
  display: flex;
  flex-direction: column;
}

.sidebar-content {
  flex: 1;
  /* 填充剩余空间 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* 隐藏溢出内容 */
}

.avatar-logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 28px 15px 16px 31px;
  gap: 12px;
}

.logo-img {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.logo-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.logo-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  white-space: nowrap;
}

.logo-subtitle {
  font-size: 12px;
  color: #666;
  margin: 0;
  white-space: nowrap;
  font-weight: normal;
}
</style>
