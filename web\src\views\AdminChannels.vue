<template>
  <div class="admin-channels">
    <!-- 主要内容 -->
    <div class="main-content">
      <div class="container">
        <!-- 统计卡片 -->
        <div class="stats-row">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ totalChannels }}</div>
                <div class="stat-label">总渠道数</div>
              </div>
            </div>
          </el-card>
          
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon enabled">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ enabledChannels }}</div>
                <div class="stat-label">已启用</div>
              </div>
            </div>
          </el-card>
          
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon disabled">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ disabledChannels }}</div>
                <div class="stat-label">已禁用</div>
              </div>
            </div>
          </el-card>
          
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Setting /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ channelTypes }}</div>
                <div class="stat-label">渠道类型</div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 渠道列表表格 -->
        <el-card class="table-card">
          <template #header>
            <div class="card-header">
              <span>渠道列表</span>
              <div class="header-actions">
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索渠道名称"
                  style="width: 250px"
                  size="small"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                
                <el-select v-model="filterType" placeholder="渠道类型" clearable size="small" style="width: 150px">
                  <el-option label="全部类型" value="" />
                  <el-option label="OpenAI" value="openai" />
                  <el-option label="Ollama" value="ollama" />
                  <el-option label="智谱AI" value="zhipu" />
                </el-select>
                
                <el-select v-model="filterStatus" placeholder="状态" clearable size="small" style="width: 120px">
                  <el-option label="全部状态" value="" />
                  <el-option label="已启用" value="enabled" />
                  <el-option label="已禁用" value="disabled" />
                </el-select>
                
                <el-button type="primary" size="small" @click="showAddChannelDialog = true">
                  <el-icon><Plus /></el-icon>
                  添加渠道
                </el-button>
                
                <el-button size="small" @click="refreshData">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          
          <el-table 
            :data="filteredChannels" 
            v-loading="loading"
            style="width: 100%"
            :header-cell-style="{ background: '#fafbfc', color: '#606266' }"
          >
            <el-table-column prop="channel_name" label="渠道名称" min-width="200">
              <template #default="{ row }">
                <div class="channel-name">
                  <el-icon class="channel-icon"><Connection /></el-icon>
                  {{ row.channel_name }}
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="channel_api_type" label="渠道类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getTypeTagType(row.channel_api_type)" size="small">
                  {{ row.channel_api_type }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="channel_url" label="API地址" min-width="250">
              <template #default="{ row }">
                <span class="api-url">{{ row.channel_url }}</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="channel_api_enabled" label="状态" width="100">
              <template #default="{ row }">
                <el-switch
                  v-model="row.channel_api_enabled"
                  @change="toggleChannelStatus(row)"
                  :loading="row.switching"
                />
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="180" fixed="right">
              <template #default="{ row }">
                <el-button 
                  type="primary" 
                  size="small" 
                  text
                  @click="editChannel(row)"
                >
                  编辑
                </el-button>
                <el-button 
                  type="danger" 
                  size="small" 
                  text
                  @click="deleteChannel(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="filteredChannels.length"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </div>
    </div>

    <!-- 添加/编辑渠道对话框 -->
    <el-dialog 
      v-model="showAddChannelDialog" 
      :title="isEditing ? '编辑渠道' : '添加渠道'" 
      width="600px"
    >
      <el-form :model="channelForm" label-width="120px" :rules="channelRules" ref="channelFormRef">
        <el-form-item label="渠道名称" prop="channel_name">
          <el-input v-model="channelForm.channel_name" placeholder="请输入渠道名称" />
        </el-form-item>
        
        <el-form-item label="渠道类型" prop="channel_api_type">
          <el-select v-model="channelForm.channel_api_type" placeholder="请选择渠道类型" style="width: 100%">
            <el-option label="OpenAI" value="openai" />
            <el-option label="Ollama" value="ollama" />
            <el-option label="智谱AI" value="zhipu" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="API地址" prop="channel_url">
          <el-input v-model="channelForm.channel_url" placeholder="请输入API地址" />
        </el-form-item>
        
        <el-form-item label="API密钥" prop="channel_api_key">
          <el-input 
            v-model="channelForm.channel_api_key" 
            type="password" 
            placeholder="请输入API密钥"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="cancelChannelDialog">取消</el-button>
        <el-button type="primary" @click="saveChannel" :loading="savingChannel">
          {{ isEditing ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Search, 
  Refresh, 
  Connection, 
  Check, 
  Close, 
  Setting 
} from '@element-plus/icons-vue'
import * as channelService from '@/services/channelService'

// 响应式数据
const loading = ref(false)
const channels = ref([])
const showAddChannelDialog = ref(false)
const savingChannel = ref(false)
const isEditing = ref(false)
const channelFormRef = ref()

// 筛选条件
const searchKeyword = ref('')
const filterType = ref('')
const filterStatus = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 渠道表单
const channelForm = ref({
  channel_name: '',
  channel_api_type: '',
  channel_url: '',
  channel_api_key: ''
})

// 表单验证规则
const channelRules = {
  channel_name: [
    { required: true, message: '请输入渠道名称', trigger: 'blur' }
  ],
  channel_api_type: [
    { required: true, message: '请选择渠道类型', trigger: 'change' }
  ],
  channel_url: [
    { required: true, message: '请输入API地址', trigger: 'blur' }
  ],
  channel_api_key: [
    { required: true, message: '请输入API密钥', trigger: 'blur' }
  ]
}

// 计算属性
const totalChannels = computed(() => channels.value.length)
const enabledChannels = computed(() => channels.value.filter(c => c.channel_api_enabled).length)
const disabledChannels = computed(() => channels.value.filter(c => !c.channel_api_enabled).length)
const channelTypes = computed(() => {
  const types = new Set(channels.value.map(c => c.channel_api_type))
  return types.size
})

const filteredChannels = computed(() => {
  let filtered = channels.value

  // 类型筛选
  if (filterType.value) {
    filtered = filtered.filter(c => c.channel_api_type === filterType.value)
  }

  // 状态筛选
  if (filterStatus.value) {
    const enabled = filterStatus.value === 'enabled'
    filtered = filtered.filter(c => c.channel_api_enabled === enabled)
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(c => 
      c.channel_name.toLowerCase().includes(keyword) ||
      c.channel_url.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

// 方法
const getTypeTagType = (type) => {
  const typeMap = {
    'openai': 'success',
    'ollama': 'info',
    'zhipu': 'warning'
  }
  return typeMap[type] || 'info'
}

const refreshData = async () => {
  loading.value = true
  try {
    await loadChannels()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('数据刷新失败:', error)
    ElMessage.error(`数据刷新失败: ${error.message || '未知错误'}`)
  } finally {
    loading.value = false
  }
}

const loadChannels = async () => {
  try {
    const channelData = await channelService.getChannelList()
    channels.value = channelData.map(channel => ({
      ...channel,
      switching: false
    }))
  } catch (error) {
    console.error('加载渠道数据失败:', error)
    throw error
  }
}

const toggleChannelStatus = async (channel) => {
  channel.switching = true
  const originalStatus = channel.channel_api_enabled
  
  try {
    await channelService.changeChannelStatus(channel.channel_id)
    ElMessage.success(`渠道 ${channel.channel_name} 状态已更新`)
  } catch (error) {
    // 恢复原状态
    channel.channel_api_enabled = originalStatus
    console.error('切换渠道状态失败:', error)
    ElMessage.error(`状态更新失败: ${error.response?.data?.message || error.message || '未知错误'}`)
  } finally {
    channel.switching = false
  }
}

const editChannel = (channel) => {
  isEditing.value = true
  channelForm.value = {
    channel_id: channel.channel_id,
    channel_name: channel.channel_name,
    channel_api_type: channel.channel_api_type,
    channel_url: channel.channel_url,
    channel_api_key: channel.channel_api_key
  }
  showAddChannelDialog.value = true
}

const deleteChannel = async (channel) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除渠道 "${channel.channel_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await channelService.deleteChannel(channel.channel_id)
    await refreshData()
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除渠道失败:', error)
      ElMessage.error(`删除失败: ${error.response?.data?.message || error.message || '未知错误'}`)
    }
  }
}

const saveChannel = async () => {
  if (!channelFormRef.value) return
  
  try {
    await channelFormRef.value.validate()
  } catch {
    return
  }

  savingChannel.value = true
  try {
    if (isEditing.value) {
      // 编辑功能暂未实现
      ElMessage.warning('编辑功能暂未实现')
    } else {
      await channelService.createChannel(channelForm.value)
      ElMessage.success('渠道添加成功')
    }
    
    showAddChannelDialog.value = false
    await refreshData()
  } catch (error) {
    console.error('保存渠道失败:', error)
    ElMessage.error(`保存失败: ${error.response?.data?.message || error.message || '未知错误'}`)
  } finally {
    savingChannel.value = false
  }
}

const cancelChannelDialog = () => {
  showAddChannelDialog.value = false
  isEditing.value = false
  channelForm.value = {
    channel_name: '',
    channel_api_type: '',
    channel_url: '',
    channel_api_key: ''
  }
  if (channelFormRef.value) {
    channelFormRef.value.clearValidate()
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.admin-channels {
  padding: 0;
  overflow-y: auto;
  background: var(--bg-secondary);
  min-height: 100vh;
}

/* 容器样式 */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-xl);
  box-sizing: border-box;
}

/* 主要内容样式 */
.main-content {
  flex: 1;
  padding: var(--spacing-lg) 0;
}

/* 统计卡片样式 */
.stats-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  transition: all var(--transition-normal);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
  background: var(--bg-primary);
  overflow: hidden;
  position: relative;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: #409eff;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  gap: var(--spacing-md);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-gradient);
  color: white;
  font-size: 24px;
}

.stat-icon.enabled {
  background: linear-gradient(135deg, #10B981 0%, #34D399 100%);
}

.stat-icon.disabled {
  background: linear-gradient(135deg, #EF4444 0%, #F87171 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 表格卡片样式 */
.table-card {
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
  background: var(--bg-primary);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  flex-wrap: wrap;
}

/* 表格样式 */
.channel-name {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.channel-icon {
  color: #409eff;
}

.api-url {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-lg);
  padding: var(--spacing-lg) 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
  }

  .header-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }
}

@media (max-width: 768px) {
  .stats-row {
    grid-template-columns: 1fr;
  }

  .container {
    padding: 0 var(--spacing-md);
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions .el-input,
  .header-actions .el-select {
    width: 100% !important;
  }
}
</style>
