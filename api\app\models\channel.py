"""
渠道和模型相关的数据库模型
包含AI服务渠道和支持的模型信息
"""

from app import db


class ChannelList(db.Model):
    """AI服务渠道模型"""
    __tablename__ = 'channellist'
    
    channel_id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键ID')
    
    channel_name = db.Column(
        db.String(255), 
        nullable=False,
        comment='渠道名称'
    )
    channel_url = db.Column(
        db.String(255), 
        nullable=False,
        comment='渠道URL'
    )
    channel_api_key = db.Column(
        db.String(255), 
        nullable=False,
        comment='API密钥'
    )
    channel_api_type = db.Column(
        db.Enum('ollama', 'openai', 'zhipu', name='api_type_enum'), 
        nullable=False,
        comment='API类型'
    )
    channel_api_enabled = db.Column(
        db.<PERSON>, 
        nullable=False,
        default=True,
        comment='是否启用'
    )

    # 关联关系
    models = db.relationship('ModelList', back_populates='channel', lazy='dynamic')

    def __repr__(self):
        return f'<ChannelList {self.channel_name}>'


class ModelList(db.Model):
    """AI模型列表"""
    __tablename__ = 'modellist'
    
    model_id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键ID')
    
    channel_id = db.Column(
        db.Integer, 
        db.ForeignKey('channellist.channel_id'),
        nullable=False,
        index=True,
        comment='渠道ID'
    )
    model_name = db.Column(
        db.String(255), 
        nullable=False,
        comment='模型名称'
    )
    model_enabled = db.Column(
        db.Boolean, 
        nullable=False,
        default=True,
        comment='模型是否启用'
    )

    # 关联关系
    channel = db.relationship('ChannelList', back_populates='models')

    def __repr__(self):
        return f'<ModelList {self.model_name}>'
