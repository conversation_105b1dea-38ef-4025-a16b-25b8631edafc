"""基础渠道类，所有渠道实现的抽象基类"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, AsyncGenerator, Optional


class BaseChannel(ABC):
    """渠道基类，定义所有渠道必须实现的方法"""

    def __init__(self, base_url: str = None, api_key: str = None, **kwargs):
        """
        初始化渠道
        
        Args:
            base_url: API基础URL
            api_key: API密钥
            **kwargs: 其他参数
        """
        self.base_url = base_url
        self.api_key = api_key
        self.kwargs = kwargs
    
    @abstractmethod
    async def chat(
        self,
        model_name: str,
        message: str,
        history: List[Dict[str, str]] = None,
        stream: bool = True,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        聊天方法，发送消息并获取回复
        
        Args:
            model_name: 使用的模型名称
            message: 用户消息
            history: 聊天历史记录
            stream: 是否使用流式响应
            **kwargs: 其他参数
            
        Returns:
            异步生成器，产生响应内容
        """
        pass
    
    @abstractmethod
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """
        获取可用模型列表
        
        Returns:
            可用模型列表
        """
        pass
    
    @abstractmethod
    async def generate_title(self, model_name: str, message: str) -> str:
        """
        根据用户的第一条消息生成对话标题
        
        Args:
            model_name: 模型名称
            message: 用户消息
            
        Returns:
            生成的标题
        """
        pass
    
    @abstractmethod
    async def check_connection(self) -> bool:
        """
        检查渠道连接是否正常
        
        Returns:
            连接是否正常
        """
        pass 