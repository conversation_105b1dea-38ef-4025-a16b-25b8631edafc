import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useApiStore } from './apiStore'
import unifiedStorage from '@/utils/unifiedStorage'

/**
 * 聊天对话管理 Store
 * 负责存储和管理聊天对话记录和状态
 */
export const useChatStore = defineStore('chat', () => {
  // 使用统一存储管理对话数据
  const conversations = ref(unifiedStorage.get('conversations', []))
  const currentConversationId = ref(unifiedStorage.get('current_conversation_id', null))
  const currentHistory = ref([])
  // 消息加载状态
  const isLoadingMessages = ref(false)

  /**
   * 设置所有对话记录
   * @param {Array} conversationList - 对话记录列表
   */
  const setConversations = (conversationList) => {
    if (!conversationList) return
    conversations.value = conversationList
    unifiedStorage.set('conversations', conversationList)
  }

  /**
   * 设置当前对话 ID
   * @param {String} conversationId - 对话ID
   * @param {Boolean} clearHistory - 是否清空历史记录，默认为 true
   */
  const setCurrentConversationId = (conversationId, clearHistory = true) => {
    currentConversationId.value = conversationId
    unifiedStorage.set('current_conversation_id', conversationId)
    // 只有在需要时才清空历史记录
    if (clearHistory) {
      currentHistory.value = []
    }
  }

  /**
   * 设置当前对话的历史消息
   * @param {Array} history - 历史消息数组
   */
  const setCurrentConversationHistory = (history) => {
    if (!history) return
    currentHistory.value = history
  }

  /**
   * 添加新消息到当前对话
   * @param {Object} message - 新消息对象
   */
  const addMessage = (message) => {
    if (!message) return
    currentHistory.value.push(message)
  }

  /**
   * 从对话列表中移除一个对话
   * @param {String} conversationId - 要删除的对话ID
   */
  const removeConversation = (conversationId) => {
    if (!conversationId) return
    
    conversations.value = conversations.value.filter(
      conv => conv.id !== conversationId
    )
    
    unifiedStorage.set('conversations', conversations.value)
    
    // 如果删除的是当前对话，清空当前对话
    if (currentConversationId.value === conversationId) {
      currentConversationId.value = null
      currentHistory.value = []
      unifiedStorage.set('current_conversation_id', null)
    }
  }

  /**
   * 设置消息加载状态
   * @param {Boolean} status - 加载状态
   */
  const setLoadingStatus = (status) => {
    isLoadingMessages.value = status
  }

  /**
   * 创建新对话
   * @param {Object} conversation - 新对话信息
   */
  const createConversation = (conversation) => {
    if (!conversation) return
    
    // 添加新对话到列表前端
    conversations.value.unshift(conversation)
    unifiedStorage.set('conversations', conversations.value)
    
    // 设置为当前对话
    setCurrentConversationId(conversation.id)
  }

  /**
   * 加载指定对话的历史记录
   * @param {String} conversationId - 对话ID
   * @returns {Promise} - 加载结果的Promise
   */
  const loadConversationHistory = async (conversationId) => {
    if (!conversationId) return
    
    try {
      setLoadingStatus(true)
      const apiStore = useApiStore()
      
      // 设置当前对话ID，不清空历史记录
      setCurrentConversationId(conversationId, false)
      
      // 调用API获取历史消息
      const response = await apiStore.getConversationHistory(conversationId)
      
      // 处理返回的消息数据
      if (response && response.messages) {
        // 格式化消息数据以适应前端显示
        const formattedMessages = response.messages.map(msg => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
          created_at: msg.created_at
        }))
        
        // 设置当前对话的历史消息
        setCurrentConversationHistory(formattedMessages)
        return formattedMessages
      } else {
        setCurrentConversationHistory([])
        return []
      }
    } catch (error) {
      console.error('加载对话历史失败:', error)
      setCurrentConversationHistory([])
      return []
    } finally {
      setLoadingStatus(false)
    }
  }

  return {
    conversations,
    currentConversationId,
    currentHistory,
    isLoadingMessages,
    setConversations,
    setCurrentConversationId,
    setCurrentConversationHistory,
    addMessage,
    removeConversation,
    setLoadingStatus,
    createConversation,
    loadConversationHistory
  }
}, {
  persist: false // 使用统一存储机制，避免双重存储
}) 