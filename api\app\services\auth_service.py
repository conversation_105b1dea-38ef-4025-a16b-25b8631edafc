"""
简化的认证服务
外部系统只负责验证密码，内部系统负责令牌管理
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Optional
from flask import current_app
from app import db
from app.models.user import User
from app.core.jwt_manager import jwt_manager, TokenError
from app.clients.external_auth_client import external_auth_client
from app.core.logging_config import log_security_event, log_api_call
from app.utils.errors import AuthError
from app.utils.error_handlers import handle_service_errors

logger = logging.getLogger(__name__)


class AuthService:
    """
    简化的认证服务
    
    职责分离：
    - 外部系统：仅验证用户名密码
    - 内部系统：完全负责令牌生成、刷新、会话管理
    """
    
    def __init__(self):
        self.external_auth = external_auth_client
        self.jwt_manager = jwt_manager
    
    @handle_service_errors
    def login(self, username: str, password: str) -> Dict:
        """
        用户登录
        
        流程：
        1. 调用外部系统验证用户名密码
        2. 验证成功后，内部生成JWT令牌
        3. 保存用户信息和会话状态
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Dict: 登录结果
            {
                'access_token': str,
                'refresh_token': str,
                'token_type': str,
                'expires_in': int,
                'user': dict
            }
            
        Raises:
            AuthError: 认证失败
        """
        logger.info(f"用户登录请求: {username}")

        # 1. 调用外部系统验证密码
        auth_result = self.external_auth.validate_credentials(username, password)

        if not auth_result['valid']:
            error_msg = auth_result.get('error', '用户名或密码错误')
            logger.warning(f"用户 {username} 登录失败: {error_msg}")
            log_security_event('login_failed', f'用户 {username} 登录失败', extra_data={'error': error_msg})
            raise AuthError(error_msg)

        # 2. 获取用户信息
        external_user_info = auth_result['user_info']

        # 3. 创建或更新用户记录
        user = self._get_or_create_user(username, external_user_info)

        # 4. 生成内部JWT令牌
        access_token = self.jwt_manager.create_access_token(
            user_id=user.id,
            additional_claims={
                'username': user.username,
                'role': user.role
            }
        )
        refresh_token = self.jwt_manager.create_refresh_token(user_id=user.id)

        # 5. 保存refresh_token哈希到数据库
        user.refresh_token_hash = self.jwt_manager.hash_token(refresh_token)
        user.last_login_at = datetime.now(timezone.utc)
        db.session.commit()

        logger.info(f"用户 {username} 登录成功")
        log_security_event('login_success', f'用户 {username} 登录成功', user_id=user.id)

        return {
            'access_token': access_token,
            'refresh_token': refresh_token,
            'token_type': 'Bearer',
            'expires_in': 1800,  # 30分钟
            'user': user.to_dict()
        }
    
    @handle_service_errors
    def refresh_token(self, refresh_token: str) -> Dict:
        """
        刷新访问令牌
        
        完全内部处理，无需外部系统参与
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            Dict: 刷新结果
            {
                'access_token': str,
                'refresh_token': str,  # 可选：轮转后的新refresh_token
                'token_type': str,
                'expires_in': int
            }
            
        Raises:
            AuthError: 令牌无效或过期
        """
        logger.debug("刷新令牌请求")

        # 1. 验证refresh_token签名和有效期
        try:
            payload = self.jwt_manager.decode_refresh_token(refresh_token)
        except TokenError as e:
            logger.warning(f"刷新令牌验证失败: {e}")
            raise AuthError(str(e))

        user_id = payload['user_id']

        # 2. 从数据库验证refresh_token
        user = User.query.get(user_id)
        if not user:
            logger.warning(f"刷新令牌对应的用户不存在: {user_id}")
            raise AuthError("无效的刷新令牌")

        if not self.jwt_manager.verify_token_hash(refresh_token, user.refresh_token_hash):
            logger.warning(f"用户 {user.username} 的刷新令牌哈希验证失败")
            raise AuthError("无效的刷新令牌")

        # 3. 生成新的access_token
        new_access_token = self.jwt_manager.create_access_token(
            user_id=user_id,
            additional_claims={
                'username': user.username,
                'role': user.role
            }
        )

        # 4. 可选：轮转refresh_token（更安全）
        new_refresh_token = self.jwt_manager.create_refresh_token(user_id=user_id)
        user.refresh_token_hash = self.jwt_manager.hash_token(new_refresh_token)
        db.session.commit()

        logger.info(f"用户 {user.username} 令牌刷新成功")

        return {
            'access_token': new_access_token,
            'refresh_token': new_refresh_token,
            'token_type': 'Bearer',
            'expires_in': 1800
        }
    
    def logout(self, refresh_token: str) -> bool:
        """
        用户登出
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            bool: 登出是否成功
        """
        try:
            logger.debug("用户登出请求")
            
            # 1. 解析refresh_token获取用户信息
            payload = self.jwt_manager.get_token_payload(refresh_token)
            if not payload:
                logger.warning("登出时无法解析刷新令牌")
                return False
            
            user_id = payload.get('user_id')
            if not user_id:
                logger.warning("登出时刷新令牌中缺少用户ID")
                return False
            
            # 2. 清除数据库中的refresh_token
            user = User.query.get(user_id)
            if user:
                user.refresh_token_hash = None
                db.session.commit()
                logger.info(f"用户 {user.username} 登出成功")
            
            return True
            
        except Exception as e:
            logger.error(f"用户登出过程中发生错误: {e}")
            return False
    
    def verify_access_token(self, access_token: str) -> Optional[Dict]:
        """
        验证访问令牌
        
        Args:
            access_token: 访问令牌
            
        Returns:
            Dict: 令牌载荷，如果无效返回None
        """
        try:
            payload = self.jwt_manager.decode_access_token(access_token)
            return payload
        except TokenError:
            return None
    
    def get_current_user(self, access_token: str) -> Optional[User]:
        """
        根据访问令牌获取当前用户

        Args:
            access_token: 访问令牌

        Returns:
            User: 用户对象，如果令牌无效返回None
        """
        try:
            payload = self.verify_access_token(access_token)
            if not payload:
                return None

            user_id = payload.get('user_id')
            if not user_id:
                return None

            return User.query.get(user_id)

        except Exception as e:
            logger.error(f"获取当前用户时发生错误: {e}")
            return None

    def get_current_user_by_id(self, user_id: int) -> Optional[User]:
        """
        根据用户ID获取用户对象

        Args:
            user_id: 用户ID

        Returns:
            User: 用户对象，如果不存在返回None
        """
        try:
            return User.query.get(user_id)
        except Exception as e:
            logger.error(f"根据ID获取用户时发生错误: {e}")
            return None
    
    def _get_or_create_user(self, username: str, external_user_info: Dict) -> User:
        """
        获取或创建用户记录
        
        Args:
            username: 用户名
            external_user_info: 外部系统返回的用户信息
            
        Returns:
            User: 用户对象
        """
        # 查找现有用户
        user = User.query.filter_by(user_code=username).first()
        
        if user:
            # 更新现有用户信息
            user.external_user_id = external_user_info.get('external_user_id', external_user_info.get('id'))
            user.username = external_user_info.get('name', username)

            # 角色优先级策略：本地管理员角色优先级最高，不被外部系统覆盖
            external_role = external_user_info.get('role', 'student')
            if user.role == 'admin':
                # 保护管理员角色，不允许外部系统覆盖
                logger.info(f"保护管理员角色: 用户 {username} 保持 admin 权限，忽略外部角色 {external_role}")
            else:
                # 非管理员用户，允许外部系统同步角色
                user.role = external_role
                logger.debug(f"同步用户角色: {username} -> {external_role}")

            logger.debug(f"更新现有用户: {username}")
        else:
            # 创建新用户
            user = User(
                user_code=username,
                username=external_user_info.get('name', username),
                external_user_id=external_user_info.get('external_user_id', external_user_info.get('id')),
                role=external_user_info.get('role', 'student'),
                created_at=datetime.now(timezone.utc)
            )
            db.session.add(user)
            
            logger.info(f"创建新用户: {username}")
        
        db.session.commit()
        return user


# 全局认证服务实例
auth_service = AuthService()
