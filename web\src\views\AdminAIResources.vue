<template>
  <div class="ai-resources-container">
    <!-- 标签页容器 -->
    <div class="tabs-container">
      <el-tabs 
        v-model="activeTab" 
        type="card" 
        class="ai-resources-tabs"
        @tab-change="handleTabChange"
      >
        <!-- 渠道管理标签 -->
        <el-tab-pane label="渠道管理" name="channels">
          <template #label>
            <div class="tab-label">
              <el-icon><Connection /></el-icon>
              <span>渠道管理</span>
            </div>
          </template>
          
          <div class="tab-content">
            <ChannelManagement 
              ref="channelManagementRef"
              @data-updated="handleChannelDataUpdated"
            />
          </div>
        </el-tab-pane>

        <!-- 模型管理标签 -->
        <el-tab-pane label="模型管理" name="models">
          <template #label>
            <div class="tab-label">
              <el-icon><Cpu /></el-icon>
              <span>模型管理</span>
            </div>
          </template>
          
          <div class="tab-content">
            <ModelManagement 
              ref="modelManagementRef"
              @data-updated="handleModelDataUpdated"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Connection, 
  Cpu, 
  Refresh 
} from '@element-plus/icons-vue'

// 导入子组件
import ChannelManagement from '@/components/admin/ChannelManagement.vue'
import ModelManagement from '@/components/admin/ModelManagement.vue'

// 响应式数据
const activeTab = ref('channels')
const globalLoading = ref(false)

// 组件引用
const channelManagementRef = ref()
const modelManagementRef = ref()

// 统计数据
const channelStats = ref({
  totalChannels: 0,
  enabledChannels: 0,
  disabledChannels: 0,
  channelTypes: 0
})

const modelStats = ref({
  totalModels: 0,
  enabledModels: 0,
  disabledModels: 0,
  channelCount: 0
})

// 标签切换处理
const handleTabChange = (tabName) => {
  console.log('切换到标签:', tabName)
  
  // 可以在这里添加标签切换的特殊逻辑
  if (tabName === 'models') {
    // 切换到模型管理时，确保数据是最新的（不显示提示）
    nextTick(() => {
      if (modelManagementRef.value) {
        modelManagementRef.value.refreshData?.(false)
      }
    })
  } else if (tabName === 'channels') {
    // 切换到渠道管理时，确保数据是最新的（不显示提示）
    nextTick(() => {
      if (channelManagementRef.value) {
        channelManagementRef.value.refreshData?.(false)
      }
    })
  }
}

// 刷新所有数据
const refreshAllData = async () => {
  globalLoading.value = true

  try {
    const promises = []

    // 刷新渠道数据（不显示单独的成功提示）
    if (channelManagementRef.value?.refreshData) {
      promises.push(channelManagementRef.value.refreshData(false))
    }

    // 刷新模型数据（不显示单独的成功提示）
    if (modelManagementRef.value?.refreshData) {
      promises.push(modelManagementRef.value.refreshData(false))
    }

    await Promise.all(promises)
    ElMessage.success('所有数据刷新成功')
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('数据刷新失败，请重试')
  } finally {
    globalLoading.value = false
  }
}

// 渠道数据更新处理
const handleChannelDataUpdated = (stats) => {
  channelStats.value = { ...stats }
}

// 模型数据更新处理
const handleModelDataUpdated = (stats) => {
  modelStats.value = { ...stats }
}

// 组件挂载
onMounted(() => {
  console.log('AI资源管理页面已挂载')
})
</script>

<style scoped>
.ai-resources-container {
  padding: 24px 32px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 标签页容器样式 */
.tabs-container {
  max-width: 1400px;
  margin: 0 auto;
}

/* 标签页样式优化 */
.ai-resources-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

:deep(.el-tabs__header) {
  margin: 0;
  background: #fafbfc;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-tabs__nav) {
  border: none;
}

:deep(.el-tabs__item) {
  border: none !important;
  background: transparent;
  color: #606266;
  font-weight: 500;
  padding: 16px 24px;
  transition: all 0.3s ease;
}

:deep(.el-tabs__item:hover) {
  color: #409eff;
  background: rgba(64, 158, 255, 0.05);
}

:deep(.el-tabs__item.is-active) {
  background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
  color: white;
  border-radius: 8px 8px 0 0;
}

:deep(.el-tabs__content) {
  padding: 0;
}

/* 标签标题样式 */
.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.tab-badge {
  margin-left: 4px;
}

/* 标签内容样式 */
.tab-content {
  padding: 24px;
  min-height: 600px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-resources-container {
    padding: 16px 20px;
  }

  .tab-content {
    padding: 16px;
  }

  :deep(.el-tabs__item) {
    padding: 12px 16px;
    font-size: 13px;
  }
}
</style>
