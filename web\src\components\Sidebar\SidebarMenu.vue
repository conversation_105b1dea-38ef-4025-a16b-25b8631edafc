<template>
  <div class="menu-list">
    <!-- 使用原生HTML替代Element Plus组件 -->
    <div class="menu-item new-chat" @click="createNewChat">
      <div class="menu-icon">
        <img src="../../assets/img/jiahao.png" alt="新对话" width="16" height="16" />
      </div>
      <span class="menu-text">新对话</span>
    </div>

    <div class="menu-item" :class="{ active: activeMenu === 'agent-plaza' }" @click="navigateToAgentPlaza">
      <div class="menu-icon">
        <img src="../../assets/svg/agent-plaza.svg" alt="智能体应用平台" width="16" height="16" />
      </div>
      <span class="menu-text">智能体应用平台</span>
    </div>

    <div class="menu-item" :class="{ active: activeMenu === 'ai-write' }" @click="handleMenuNotReady">
      <div class="menu-icon">
        <img src="../../assets/img/xiezuo.png" alt="智能体开发平台" width="16" height="16" />
      </div>
      <span class="menu-text">智能体开发平台</span>
    </div>

    <div class="menu-item" :class="{ active: activeMenu === 'aigc-tools' }" @click="navigateToAigcTools">
      <div class="menu-icon">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"/>
        </svg>
      </div>
      <span class="menu-text">AIGC工具坊</span>
    </div>

    <!-- 添加分割线 -->
    <div class="menu-divider"></div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { useChatStore } from '@/stores/chatStore'

// 向父组件发送事件
const emit = defineEmits(['menuSelect', 'createNewChat'])

// 接收来自父组件的属性
// const props = defineProps({
//   activeMenu: {
//     type: String,
//     default: ''
//   },
//   hasUserSentMessage: {
//     type: Boolean,
//     default: false
//   }
// })

// // 向父组件发送事件
// const emit = defineEmits(['createNewChat', 'menuSelect'])

// // 创建新对话
// const createNewChat = () => {
//   // 检查用户是否已经发送过消息
//   if (!props.hasUserSentMessage) {
//     ElMessage.warning('请先发送一条消息后再创建新对话')
//     return
//   }
//   emit('createNewChat')
// }

// // 处理未完成的功能菜单点击
// const handleMenuNotReady = (index) => {
//   ElMessage.info('该功能正在开发中')
// }

const router = useRouter()
const route = useRoute()
const chatStore = useChatStore()
const activeMenu = ref('') // 添加 activeMenu 变量，用于跟踪当前活动菜单

// 监听路由变化，自动更新菜单状态
watch(() => route.path, (newPath) => {
  if (newPath === '/chat/agent-plaza') {
    activeMenu.value = 'agent-plaza'
  } else if (newPath === '/chat/aigc-tools') {
    activeMenu.value = 'aigc-tools'
  } else if (newPath === '/chat' || newPath.startsWith('/chat/')) {
    // 如果是聊天页面但不是特殊页面，清空选中状态
    if (!newPath.includes('agent-plaza') && !newPath.includes('aigc-tools')) {
      activeMenu.value = ''
    }
  }
}, { immediate: true })

const createNewChat = () => {
  // 清空当前对话ID和历史记录
  chatStore.setCurrentConversationId(null)
  chatStore.setCurrentConversationHistory([])
  
  // 设置当前活动菜单为聊天
  activeMenu.value = 'chat'
  emit('menuSelect', 'chat')
  
  // 导航到新对话页面
  router.push({
    path: '/chat',
    replace: true  // 使用 replace 而不是 push，这样用户点击返回不会回到上一个对话
  })
}

// 处理未完成的功能菜单点击
const handleMenuNotReady = () => {
  ElMessage.info('该功能正在开发中')
}

// 导航到智能体应用平台
const navigateToAgentPlaza = () => {
  emit('menuSelect', 'agent-plaza')
  router.push('/chat/agent-plaza')
}

// 导航到AIGC工具坊
const navigateToAigcTools = () => {
  emit('menuSelect', 'aigc-tools')
  router.push('/chat/aigc-tools')
}
</script>

<style scoped>
/* 菜单样式 */
.menu-list {
  margin-bottom: 16px;
  padding: 0 16px;
}

.menu-item {
  display: flex;
  align-items: center;
  height: 40px;
  width: 100%;
  padding: 0 15px;
  border-radius: 10px;
  margin: 2px 0;
  box-sizing: border-box;
  cursor: pointer;
  transition: background 0.2s;
  color: #333;
}

.menu-item:hover {
  background: rgba(0, 0, 0, 0.06);
}

.menu-item.active {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  font-weight: 500;
}

.menu-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: #606060;
}

.menu-text {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 10px 16px;
  width: calc(100% - 32px);
}
</style>