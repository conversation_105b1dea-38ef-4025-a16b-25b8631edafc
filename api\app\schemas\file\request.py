"""文件相关的请求模型"""
from flask_restx import fields


def file_upload_request_model(api):
    """文件上传请求模型"""
    return api.model('FileUploadRequest', {
        'description': fields.String(description='文件描述')
    })


def file_process_request_model(api):
    """文件处理请求模型"""
    return api.model('FileProcessRequest', {
        'file_id': fields.String(required=True, description='文件ID'),
    })


def file_reprocess_request_model(api):
    """文件重新处理请求模型"""
    return api.model('FileReprocessRequest', {
        'file_id': fields.String(required=True, description='文件ID'),
    })


def file_content_request_model(api):
    """文件内容请求模型"""
    return api.model('FileContentRequest', {
        'file_ids': fields.List(fields.String, required=True, description='文件ID列表'),
        'max_content_length': fields.Integer(description='最大内容长度', default=200000)
    }) 