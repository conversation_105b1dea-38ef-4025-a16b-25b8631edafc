"""
令牌黑名单模型
用于存储被撤销的JWT令牌，防止被撤销的令牌继续使用
"""

from datetime import datetime, timezone
from app import db


class TokenBlacklist(db.Model):
    """令牌黑名单模型"""
    __tablename__ = 'token_blacklist'
    
    # 主键：JWT的唯一标识符
    jti = db.Column(
        db.String(36), 
        primary_key=True, 
        nullable=False,
        comment='JWT唯一标识符(JTI)'
    )
    
    # 外键：关联用户
    user_id = db.Column(
        db.Integer, 
        db.ForeignKey('User.user_id'), 
        nullable=False,
        index=True,
        comment='用户ID'
    )
    
    # 撤销时间
    revoked_at = db.Column(
        db.DateTime, 
        nullable=False, 
        default=lambda: datetime.now(timezone.utc),
        comment='令牌撤销时间'
    )
    
    # 令牌过期时间（用于定期清理）
    expires_at = db.Column(
        db.DateTime, 
        nullable=False,
        index=True,
        comment='令牌原始过期时间'
    )
    
    # 令牌类型（access或refresh）
    token_type = db.Column(
        db.String(20), 
        nullable=False,
        default='access',
        comment='令牌类型'
    )
    
    # 关联关系
    user = db.relationship('User', backref='blacklisted_tokens')

    def to_dict(self):
        """转换为字典格式"""
        return {
            'jti': self.jti,
            'user_id': self.user_id,
            'revoked_at': self.revoked_at.isoformat() + 'Z' if self.revoked_at else None,
            'expires_at': self.expires_at.isoformat() + 'Z' if self.expires_at else None,
            'token_type': self.token_type
        }

    @classmethod
    def is_blacklisted(cls, jti):
        """检查令牌是否在黑名单中"""
        return cls.query.filter_by(jti=jti).first() is not None

    @classmethod
    def add_to_blacklist(cls, jti, user_id, expires_at, token_type='access'):
        """将令牌添加到黑名单"""
        blacklist_entry = cls(
            jti=jti,
            user_id=user_id,
            expires_at=expires_at,
            token_type=token_type
        )
        db.session.add(blacklist_entry)
        return blacklist_entry

    @classmethod
    def cleanup_expired(cls):
        """清理已过期的黑名单记录"""
        now = datetime.now(timezone.utc)
        expired_count = cls.query.filter(cls.expires_at < now).delete()
        db.session.commit()
        return expired_count

    def __repr__(self):
        return f'<TokenBlacklist {self.jti[:8]}...({self.token_type})>'
