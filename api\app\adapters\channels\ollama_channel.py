from typing import AsyncGenerator, List, Dict, Optional, Any
import json
from httpx import get, AsyncClient, ConnectTimeout
from .base import BaseChannel
from app.utils.errors import (
    ChannelConnectionError,
    ChannelModelError,
    ChannelTimeoutError
)
import asyncio

class OllamaChannel(BaseChannel):
    """
    Ollama渠道实现
    用于连接本地或远程的Ollama服务
    """
    
    def __init__(self, base_url: str = None, api_key: str = None, **kwargs):
        """
        初始化Ollama渠道
        
        Args:
            base_url: Ollama API基础URL，默认为本地API
            api_key: Ollama API密钥（通常不需要）
            **kwargs: 其他参数
        """
        super().__init__(base_url, api_key, **kwargs)
        # 使用模拟实现
    
    async def chat(
        self,
        model_name: str,
        message: str,
        history: List[Dict[str, str]] = None,
        stream: bool = True,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        使用Ollama API发送聊天请求
        
        Args:
            model_name: 模型名称
            message: 用户消息
            history: 历史消息
            stream: 是否使用流式响应
            **kwargs: 其他参数
            
        Returns:
            响应生成器
        """
        # 模拟响应
        await asyncio.sleep(0.5)
        
        response = f"Ollama渠道模拟响应。\n您选择的模型是: {model_name}\n您的消息是: {message}"
        
        if stream:
            # 模拟流式响应
            words = response.split()
            for word in words:
                await asyncio.sleep(0.1)
                yield word + " "
        else:
            # 非流式响应
            yield response
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """
        获取可用模型列表
        
        Returns:
            可用模型列表
        """
        return [
            {
                "model_name": "llama2",
                "model_id": 3,
                "channel_id": 2,
                "channel_name": "Ollama",
                "channel_type": "ollama"
            },
            {
                "model_name": "mistral",
                "model_id": 4,
                "channel_id": 2,
                "channel_name": "Ollama",
                "channel_type": "ollama"
            }
        ]
    
    async def check_connection(self) -> bool:
        """
        检查连接状态
        
        Returns:
            连接状态
        """
        return True
    
    async def generate_title(self, model_name: str, message: str) -> str:
        """
        根据用户的第一条消息生成对话标题
        
        Args:
            model_name: 模型名称
            message: 用户的第一条消息
            
        Returns:
            生成的标题，如果生成失败则返回截断的用户消息作为标题
        """
        prompt = f"""请根据以下用户消息生成一个简短、有意义的对话标题：
要求：
1. 不超过8个字
2. 不要使用任何标点符号
3. 只返回标题，不要其他任何内容

用户消息：
{message}"""
        
        try:
            full_response = ""
            async for chunk in self.chat(
                model_name=model_name,
                message=prompt,
                stream=False
            ):
                full_response += chunk
            
            # 处理并返回标题
            if full_response:
                # 移除所有标点符号并限制长度
                title = full_response.strip()
                title = ''.join(char for char in title if not char in '，。！？、；：""''（）【】《》')
                return title[:8]
        except Exception:
            pass
            
        # 如果生成失败，则使用用户消息的前几个字作为标题
        return message[:8] 