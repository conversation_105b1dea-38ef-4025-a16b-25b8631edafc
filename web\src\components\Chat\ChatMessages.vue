<template>
  <div class="bubble-messages" ref="messagesContainer" v-if="messages.length">
    <div v-for="msg in messages" :key="msg.id" :class="['bubble-row', msg.role]">
      <template v-if="msg.role === 'assistant'">
        <img class="bubble-avatar ai-logo" src="../../assets/svg/logo.svg" alt="AI" draggable="false" />
        <div class="bubble bubble-left">
          <!-- 思考过程部分 -->
          <div v-if="getThinkingContent(msg.content)" class="thinking-section">
            <div class="thinking-header" @click="toggleThinking(msg.id)">
              <div class="thinking-toggle">
                <svg class="thinking-arrow" :class="{ 'thinking-arrow-expanded': isThinkingExpanded(msg.id) }"
                  viewBox="0 0 24 24" width="16" height="16">
                  <path fill="currentColor" d="M8.59 16.59L13.17 12L8.59 7.41L10 6l6 6-6 6-1.41-1.41z" />
                </svg>
                <span class="thinking-title">思考过程</span>
              </div>
            </div>
            <div class="thinking-content" :class="{ 'thinking-content-expanded': isThinkingExpanded(msg.id) }"
              ref="thinkingContent">
              <div class="thinking-inner" v-html="renderMarkdown(getThinkingContent(msg.content))"></div>
            </div>
          </div>

          <!-- 主要回答内容 -->
          <div v-if="!isMessageLoading(msg)" v-html="renderMarkdown(getMainContent(msg.content))"></div>

          <!-- 思考中状态 -->
          <div v-if="isMessageLoading(msg)" class="thinking-indicator">
            <div class="thinking-dots">
              <span class="thinking-dot"></span>
              <span class="thinking-dot"></span>
              <span class="thinking-dot"></span>
            </div>
            <span class="thinking-text">AI正在思考中...</span>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="bubble bubble-right">{{ msg.content }}</div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { watch, nextTick, ref, onMounted, onUnmounted, reactive } from 'vue';
import { marked } from 'marked';

const props = defineProps({
  messages: {
    type: Array,
    default: () => []
  }
});

const messagesContainer = ref(null);
const autoScrollEnabled = ref(true);
const isUserScrolling = ref(false);
let scrollContainer = null;
let scrollListener = null;
let scrollTimeout = null;
let lastScrollTop = 0;

// 思考过程展开状态管理
const thinkingStates = reactive({});

// 初始化思考状态（默认展开）
const initThinkingState = (messageId) => {
  if (!(messageId in thinkingStates)) {
    thinkingStates[messageId] = true; // 默认展开
  }
};

// 切换思考过程展开状态
const toggleThinking = (messageId) => {
  initThinkingState(messageId);
  thinkingStates[messageId] = !thinkingStates[messageId];
};

// 检查思考过程是否展开
const isThinkingExpanded = (messageId) => {
  initThinkingState(messageId);
  return thinkingStates[messageId];
};

// 提取思考过程内容
const getThinkingContent = (content) => {
  if (!content) return '';

  // 首先尝试匹配完整的思考标签对
  const completeMatch = content.match(/<think>([\s\S]*?)<\/think>/);
  if (completeMatch) {
    return completeMatch[1].trim();
  }

  // 如果没有完整的标签对，检查是否有开始标签（流式传输中的情况）
  const startMatch = content.match(/<think>([\s\S]*)/);
  if (startMatch) {
    return startMatch[1].trim();
  }

  return '';
};

// 获取主要内容（移除思考过程）
const getMainContent = (content) => {
  if (!content) return '';

  // 首先移除完整的思考标签对
  let mainContent = content.replace(/<think>[\s\S]*?<\/think>/g, '');

  // 如果还有未闭合的think标签，也要移除
  mainContent = mainContent.replace(/<think>[\s\S]*$/g, '');

  return mainContent.trim();
};

// 判断消息是否正在加载中
const isMessageLoading = (msg) => {
  // 支持两种加载状态：streaming（普通聊天）和 loading（智能体聊天）
  if (msg.streaming && !msg.content.trim()) {
    return true;
  }
  if (msg.loading) {
    return true;
  }
  return false;
};

const renderMarkdown = (content) => {
  if (!content) return '';
  try {
    return marked(content, { breaks: true, gfm: true });
  } catch (error) {
    console.error('Markdown解析错误:', error);
    return content;
  }
};

const getScrollContainer = () => {
  return document.querySelector('.main-center') ||
    messagesContainer.value?.parentElement ||
    document.documentElement;
};

const scrollToBottom = async () => {
  if (!autoScrollEnabled.value || !scrollContainer || isUserScrolling.value) return;

  await nextTick();
  try {
    scrollContainer.scrollTo({
      top: scrollContainer.scrollHeight,
      behavior: 'smooth'
    });
  } catch (e) {
    console.error('滚动出错:', e);
  }
};

const checkAutoScroll = () => {
  if (!scrollContainer) return;

  const threshold = scrollContainer.clientHeight * 0.3;
  const distanceToBottom = scrollContainer.scrollHeight - scrollContainer.scrollTop - scrollContainer.clientHeight;

  autoScrollEnabled.value = distanceToBottom <= threshold;
};

const handleScroll = () => {
  const currentScrollTop = scrollContainer.scrollTop;
  const isScrollingUp = currentScrollTop < lastScrollTop;
  lastScrollTop = currentScrollTop;

  if (isScrollingUp) {
    autoScrollEnabled.value = false;
    isUserScrolling.value = true;
    return;
  }

  checkAutoScroll();

  if (scrollTimeout) clearTimeout(scrollTimeout);
  scrollTimeout = setTimeout(() => {
    isUserScrolling.value = false;
    if (autoScrollEnabled.value) {
      scrollToBottom();
    }
  }, 300);
};

const initScrollListener = () => {
  scrollContainer = getScrollContainer();
  if (!scrollContainer) {
    console.warn('未找到滚动容器');
    return;
  }

  if (scrollListener) {
    scrollContainer.removeEventListener('scroll', scrollListener);
  }

  scrollListener = handleScroll;
  scrollContainer.addEventListener('scroll', scrollListener);
  scrollToBottom();
};

watch(() => props.messages, () => {
  if (!isUserScrolling.value) {
    scrollToBottom();
  }
}, { deep: true });

onMounted(() => {
  setTimeout(initScrollListener, 100);
});

onUnmounted(() => {
  if (scrollContainer && scrollListener) {
    scrollContainer.removeEventListener('scroll', scrollListener);
  }
  if (scrollTimeout) clearTimeout(scrollTimeout);
});
</script>

<style scoped>
/* 全局字体样式 */
:deep(body) {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;
}

.bubble-messages {
  width: 750px;
  min-height: 120px;
  margin-bottom: 80px;
  margin-top: 0;
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding-top: 16px;
  align-self: center;
  padding-bottom: 280px;
  overflow: visible;
}

:deep(.main-center),
:deep(.chat-container),
:deep(.chat-content) {
  height: 100vh;
  overflow-y: auto;
}

.bubble-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0;
}

.bubble-row.assistant {
  justify-content: flex-start;
  align-items: flex-start;
}

.bubble-row.user {
  justify-content: flex-end;
}

.bubble-avatar {
  margin: 0 8px 0 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.ai-logo {
  margin-right: 12px;
  margin-top: 0px;
}

.bubble {
  display: inline-block;
  min-width: 36px;
  max-width: 70%;
  padding: 10px 16px;
  border-radius: 18px;
  font-size: 15px;
  line-height: 1.6;
  word-break: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  vertical-align: bottom;
  letter-spacing: 0.3px;
}

.bubble-left {
  background: #ffffff;
  color: #374151;
  border-radius: 16px;
  border-top-left-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 32px;
  font-size: 15px;
  line-height: 1.7;
  letter-spacing: 0.2px;
  max-width: 95%;
}

.bubble-right {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  color: #ffffff;
  border-radius: 16px;
  border-bottom-right-radius: 4px;
  text-align: left;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 14px 18px;
  max-width: 70%;
  font-size: 15px;
  line-height: 1.6;
  letter-spacing: 0.2px;
  font-weight: 400;
}

/* 思考过程样式 */
.thinking-section {
  margin-bottom: 16px;
  border: 1px solid #e8f4fd;
  border-radius: 12px;
  background: #f8fcff;
  overflow: hidden;
}

.thinking-header {
  padding: 12px 16px;
  background: #f0f8ff;
  border-bottom: 1px solid #e8f4fd;
  cursor: pointer;
  transition: background-color 0.2s ease;
  user-select: none;
}

.thinking-header:hover {
  background: #e8f4fd;
}

.thinking-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-arrow {
  transition: transform 0.3s ease;
  color: #5b9bd5;
  flex-shrink: 0;
}

.thinking-arrow-expanded {
  transform: rotate(90deg);
}

.thinking-title {
  font-size: 14px;
  font-weight: 500;
  color: #5b9bd5;
}

.thinking-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.thinking-content-expanded {
  max-height: 1000px;
  /* 足够大的值来容纳内容 */
}

.thinking-inner {
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: #4a5568;
  background: #f8fcff;
}

/* 思考过程内的 Markdown 样式 */
.thinking-inner :deep(pre) {
  background-color: #f1f8fe;
  border-radius: 8px;
  padding: 12px 16px;
  overflow-x: auto;
  margin: 8px 0;
  border: 1px solid #e8f4fd;
}

.thinking-inner :deep(code) {
  font-family: "SF Mono", "Cascadia Code", "JetBrains Mono", Menlo, Monaco, Consolas, monospace;
  font-size: 0.92em;
  background-color: #f1f8fe;
  padding: 2px 4px;
  border-radius: 4px;
}

.thinking-inner :deep(p) {
  margin: 6px 0;
  line-height: 1.6;
}

.thinking-inner :deep(ul),
.thinking-inner :deep(ol) {
  padding-left: 20px;
  margin: 8px 0;
}

.thinking-inner :deep(h1),
.thinking-inner :deep(h2),
.thinking-inner :deep(h3),
.thinking-inner :deep(h4),
.thinking-inner :deep(h5),
.thinking-inner :deep(h6) {
  margin: 14px 0 8px 0;
  font-weight: 600;
  color: #2d4a5e;
}

/* 主要内容的 Markdown 样式 */
.bubble-left :deep(pre) {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 16px 20px;
  overflow-x: auto;
  margin: 12px 0;
  border: 1px solid #e5e7eb;
  font-size: 14px;
  line-height: 1.5;
}

.bubble-left :deep(code) {
  font-family: "SF Mono", "Cascadia Code", "JetBrains Mono", Menlo, Monaco, Consolas, monospace;
  font-size: 0.9em;
  background-color: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  color: #374151;
}

.bubble-left :deep(p) {
  margin: 8px 0;
  line-height: 1.7;
  color: #374151;
}

.bubble-left :deep(ul),
.bubble-left :deep(ol) {
  padding-left: 24px;
  margin: 12px 0;
  color: #374151;
}

.bubble-left :deep(li) {
  margin: 4px 0;
  line-height: 1.6;
}

.bubble-left :deep(h1),
.bubble-left :deep(h2),
.bubble-left :deep(h3),
.bubble-left :deep(h4),
.bubble-left :deep(h5),
.bubble-left :deep(h6) {
  margin: 16px 0 10px 0;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
}

.bubble-left :deep(a) {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
  border-radius: 2px;
  padding: 1px 2px;
}

.bubble-left :deep(a:hover) {
  color: #1d4ed8;
  background-color: #eff6ff;
  border-bottom: 1px solid #3b82f6;
}

.bubble-left :deep(blockquote) {
  padding: 12px 20px;
  margin: 12px 0;
  border-left: 4px solid #3b82f6;
  background-color: #f8fafc;
  border-radius: 8px;
  color: #4b5563;
  font-style: italic;
  position: relative;
}

.bubble-left :deep(table) {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  margin: 16px 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
  background: #ffffff;
}

.bubble-left :deep(th),
.bubble-left :deep(td) {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #f0f4f8;
  border-right: 1px solid #f0f4f8;
  vertical-align: middle;
  font-size: 14px;
  line-height: 1.5;
}

.bubble-left :deep(th:last-child),
.bubble-left :deep(td:last-child) {
  border-right: none;
}

.bubble-left :deep(th) {
  background: #ffffff;
  color: #374151;
  font-weight: 600;
  font-size: 14px;
  letter-spacing: 0.3px;
  border-bottom: 2px solid #e2e8f0;
  position: relative;
}

.bubble-left :deep(th:first-child) {
  border-top-left-radius: 12px;
}

.bubble-left :deep(th:last-child) {
  border-top-right-radius: 12px;
}

.bubble-left :deep(td) {
  color: #374151;
  transition: background-color 0.2s ease;
}

.bubble-left :deep(tr:nth-child(even) td) {
  background-color: #f8fafc;
}

.bubble-left :deep(tr:hover td) {
  background-color: #f1f5f9;
}

.bubble-left :deep(tr:last-child td:first-child) {
  border-bottom-left-radius: 12px;
}

.bubble-left :deep(tr:last-child td:last-child) {
  border-bottom-right-radius: 12px;
}

.bubble-left :deep(tr:last-child td) {
  border-bottom: none;
}

/* 表格第一列不换行，保持整齐 */
.bubble-left :deep(td:first-child),
.bubble-left :deep(th:first-child) {
  white-space: nowrap;
  min-width: 80px;
}

/* 表格第一列不换行 */
.bubble-left :deep(td:first-child),
.bubble-left :deep(th:first-child) {
  white-space: nowrap;
  min-width: 80px;
}

.bubble-left :deep(img) {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
  margin: 8px 0;
  border-radius: 6px;
  display: block;
}

.bubble-messages-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 思考中状态样式 */
.thinking-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  color: #666;
  font-size: 14px;
}

.thinking-dots {
  display: flex;
  gap: 4px;
}

.thinking-dot {
  width: 6px;
  height: 6px;
  background-color: #409eff;
  border-radius: 50%;
  animation: thinking-pulse 1.4s ease-in-out infinite both;
}

.thinking-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.thinking-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.thinking-dot:nth-child(3) {
  animation-delay: 0s;
}

.thinking-text {
  font-style: italic;
  opacity: 0.8;
}

@keyframes thinking-pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>