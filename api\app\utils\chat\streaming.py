"""
流式响应工具
处理Async Generator到Response的转换
"""
import asyncio
import json
import logging
from typing import AsyncGenerator, Dict, Any
from flask import Response, current_app

async def generate_stream_response(async_generator):
    """
    生成SSE流式响应内容
    
    Args:
        async_generator: 异步生成器
        
    Yields:
        格式化的SSE事件流
    """
    try:
        logger = logging.getLogger('werkzeug')
        logger.info("=== 开始处理流式响应生成 ===")
        logger.error("===== 开始处理流式响应生成 =====")
        item_count = 0
        all_original_items = []  # 收集所有原始项目用于问题排查
        
        # 先发送一个初始化消息，确保流已经开始
        init_msg = f"data: {json.dumps({'status': 'initializing', 'content': '正在初始化...'})}\n\n"
        logger.error(f"===== 发送初始化消息 =====")
        yield init_msg
        
        async for item in async_generator:
            item_count += 1
            logger.info(f"=== 收到流式项目 #{item_count} ===")
            
            # 收集原始项目
            if isinstance(item, dict):
                all_original_items.append(json.dumps(item, ensure_ascii=False))
            else:
                all_original_items.append(str(item))
                
            # 每10个项目记录一次ERROR级别日志
            if item_count % 10 == 1:
                logger.error(f"===== 流式响应进度: 已处理 {item_count} 个项目 =====")
                
            logger.info(f"项目类型: {type(item)}")
            
            if isinstance(item, str):
                logger.info(f"字符串项目原始内容: [{item}]")
                # 尝试解析字符串为JSON
                try:
                    parsed_item = json.loads(item)
                    logger.info(f"字符串成功解析为JSON: {json.dumps(parsed_item, ensure_ascii=False, indent=2)}")
                    if isinstance(parsed_item, dict) and 'status' in parsed_item and '200 OK' in str(parsed_item['status']):
                        logger.warning(f"跳过JSON字符串中的HTTP状态码: [{item}]")
                        logger.error(f"===== 跳过JSON字符串中的HTTP状态码: [{item}] =====")
                        continue
                except json.JSONDecodeError:
                    logger.info("字符串不是有效的JSON格式")
            elif isinstance(item, dict):
                logger.info(f"字典项目原始内容: {json.dumps(item, ensure_ascii=False, indent=2)}")
            else:
                logger.info(f"其他类型项目原始内容: {str(item)}")
            
            # 防止意外的状态码格式被输出
            if isinstance(item, str) and (item.startswith('{"status":') and 'OK' in item):
                # 跳过看起来像HTTP状态的响应
                logger.warning(f"跳过疑似HTTP状态响应: [{item}]")
                logger.error(f"===== 跳过疑似HTTP状态响应: [{item}] =====")
                continue
                
            # 对字典类型检查是否含有疑似HTTP状态码
            if isinstance(item, dict) and 'status' in item:
                status_value = str(item['status'])
                logger.info(f"检查字典状态值: [{status_value}]")
                if '200 OK' in status_value or status_value == '200 OK' or 'OK' in status_value:
                    logger.warning(f"跳过含有HTTP状态码的字典响应: {json.dumps(item)}")
                    logger.error(f"===== 跳过含有HTTP状态码的字典响应: {json.dumps(item)} =====")
                    continue
                
            if isinstance(item, dict):
                # 检查是否是纯状态字典，没有实际内容
                if len(item) == 1 and 'status' in item:
                    logger.warning(f"跳过仅包含状态的字典: {json.dumps(item)}")
                    logger.error(f"===== 跳过仅包含状态的字典: {json.dumps(item)} =====")
                    continue
                
                # 确保字典中没有无效数据
                safe_item = {k: v for k, v in item.items() if k in ['content', 'error', 'status', 'conversation_id', 'title']}
                logger.info(f"过滤后的安全字典项: {json.dumps(safe_item, ensure_ascii=False)}")
                
                # 确保状态不是HTTP状态码
                if 'status' in safe_item and ('OK' in str(safe_item['status']) or '200' in str(safe_item['status'])):
                    logger.warning(f"替换HTTP状态码 '{safe_item['status']}' 为 'generating'")
                    safe_item['status'] = 'generating'
                
                result = f"data: {json.dumps(safe_item)}\n\n"
                logger.info(f"生成字典响应: [{result.strip()}]")
                yield result
            else:
                # 确保非字典数据被正确封装
                if isinstance(item, str):
                    # 跳过可能包含HTTP状态的字符串
                    if '"status"' in item and ('OK' in item or '200' in item):
                        logger.warning(f"跳过可能包含HTTP状态的字符串: [{item}]")
                        logger.error(f"===== 跳过可能包含HTTP状态的字符串: [{item}] =====")
                        continue
                        
                    result = f"data: {json.dumps({'content': item, 'status': 'generating'})}\n\n"
                    logger.info(f"生成字符串响应: [{result.strip()}]")
                    yield result
                else:
                    # 如果不是字符串或字典，转换为字符串
                    result = f"data: {json.dumps({'content': str(item), 'status': 'generating'})}\n\n"
                    logger.info(f"生成其他类型响应: [{result.strip()}]")
                    yield result
        
        # 发送完成消息
        logger.info(f"=== 流式响应完成，共生成 {item_count} 个项目 ===")
        logger.error(f"===== 流式响应完成，共生成 {item_count} 个项目 =====")
        
        # 记录所有原始项目用于排查问题
        logger.error("===== 所有原始响应项目 =====")
        for i, original_item in enumerate(all_original_items):
            logger.error(f"项目 #{i+1}: {original_item}")
        logger.error("===== 原始响应项目记录结束 =====")
        
        completion_msg = f"data: {json.dumps({'status': 'completed'})}\n\n"
        logger.info(f"发送完成消息: [{completion_msg.strip()}]")
        yield completion_msg
        
        done_msg = "data: [DONE]\n\n"
        logger.info(f"发送结束标记: [{done_msg.strip()}]")
        yield done_msg
    except Exception as e:
        logger.error(f"生成流式响应时发生错误: {str(e)}", exc_info=True)
        error_msg = f"data: {json.dumps({'error': str(e), 'status': 'error'})}\n\n"
        logger.info(f"发送错误消息: [{error_msg.strip()}]")
        yield error_msg
        
        done_msg = "data: [DONE]\n\n"
        logger.info(f"发送结束标记: [{done_msg.strip()}]")
        yield done_msg

def create_streaming_response(generator_func):
    """
    创建流式响应
    
    Args:
        generator_func: 生成器函数
        
    Returns:
        Flask Response对象
    """
    logger = logging.getLogger('werkzeug')
    logger.info("创建流式响应，设置headers: text/event-stream")
    logger.error("===== 创建流式响应，设置headers: text/event-stream =====")
    response = Response(
        generator_func,
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no',  # 禁用Nginx缓冲
            'Content-Type': 'text/event-stream; charset=utf-8'
        }
    )
    logger.info(f"流式响应对象创建完成: {response}")
    logger.error(f"===== 流式响应对象创建完成: {response} =====")
    return response 