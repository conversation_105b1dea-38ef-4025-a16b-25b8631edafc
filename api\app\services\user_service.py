"""
用户服务层
包含用户相关的业务逻辑和数据访问方法
"""

from typing import List, Optional
from app import db
from app.models import User


class UserService:
    """用户服务类"""
    
    @staticmethod
    def get_by_id(user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        return User.query.filter_by(user_id=user_id).first()
    
    @staticmethod
    def get_by_username(username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return User.query.filter_by(username=username).first()
    
    @staticmethod
    def get_by_user_code(user_code: str) -> Optional[User]:
        """根据用户编码获取用户"""
        return User.query.filter_by(user_code=user_code).first()
    
    @staticmethod
    def get_active_users() -> List[User]:
        """获取所有活跃用户"""
        return User.query.filter_by(disabled=False).all()
    
    @staticmethod
    def get_users_by_role(role: str) -> List[User]:
        """根据角色获取用户"""
        return User.query.filter(
            User.role == role,
            User.disabled == False
        ).all()
    
    @staticmethod
    def search_users(keyword: str) -> List[User]:
        """搜索用户"""
        return User.query.filter(
            db.or_(
                User.username.like(f'%{keyword}%'),
                User.user_code.like(f'%{keyword}%')
            ),
            User.disabled == False
        ).all()
    
    @staticmethod
    def create_user(username: str, user_code: str, role: str = 'student',
                   external_user_id: str = None) -> User:
        """创建新用户"""
        # 检查用户名是否已存在
        if UserService.get_by_username(username):
            raise ValueError(f"用户名 {username} 已存在")
        
        # 检查用户编码是否已存在
        if UserService.get_by_user_code(user_code):
            raise ValueError(f"用户编码 {user_code} 已存在")
        
        # 创建用户
        user = User(
            username=username,
            user_code=user_code,
            role=role,
            disabled=False,
            external_user_id=external_user_id
        )
        
        # 保存用户
        db.session.add(user)
        db.session.commit()
        
        return user
    
    @staticmethod
    def disable_user(user_id: int) -> bool:
        """禁用用户"""
        user = UserService.get_by_id(user_id)
        if user:
            user.disabled = True
            db.session.commit()
            return True
        return False
    
    @staticmethod
    def enable_user(user_id: int) -> bool:
        """启用用户"""
        user = UserService.get_by_id(user_id)
        if user:
            user.disabled = False
            db.session.commit()
            return True
        return False
    
    @staticmethod
    def update_user_role(user_id: int, new_role: str) -> bool:
        """更新用户角色"""
        if new_role not in ['student', 'teacher', 'admin']:
            raise ValueError("无效的角色")
        
        user = UserService.get_by_id(user_id)
        if user:
            user.role = new_role
            db.session.commit()
            return True
        return False 