# 📖 PUMI Agent API 接口文档

> **企业级AI智能体平台API接口详细说明**

## 🌐 API概览

### 基础信息
- **Base URL**: `http://localhost:5000` (开发环境)
- **API版本**: v1.0.0
- **认证方式**: JWT Bearer <PERSON>ken
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 状态码说明
| 状态码 | 说明 | 描述 |
|--------|------|------|
| 200 | 成功 | 请求成功处理 |
| 400 | 请求错误 | 请求参数有误 |
| 401 | 未授权 | 需要身份验证 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 未找到 | 资源不存在 |
| 500 | 服务器错误 | 内部服务器错误 |

## 🔐 认证接口

### 用户登录
**POST** `/auth/login`

登录获取JWT令牌

**请求参数：**
```json
{
  "username": "string",
  "password": "string",
  "client_fp": "string"  // 可选，客户端指纹
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600,
    "user": {
      "user_id": 1,
      "username": "test_user",
      "role": "student"
    }
  }
}
```

### 刷新令牌
**POST** `/auth/refresh`

使用refresh_token获取新的access_token

**请求头：**
```
Authorization: Bearer <refresh_token>
```

**响应示例：**
```json
{
  "code": 200,
  "message": "令牌刷新成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600
  }
}
```

### 验证令牌
**GET** `/auth/verify`

验证当前令牌有效性并获取用户信息

**请求头：**
```
Authorization: Bearer <access_token>
```

**响应示例：**
```json
{
  "code": 200,
  "message": "令牌有效",
  "data": {
    "user_id": 1,
    "username": "test_user",
    "role": "student",
    "exp": 1640995200
  }
}
```

### 用户登出
**POST** `/auth/logout`

撤销用户令牌

**请求头：**
```
Authorization: Bearer <access_token>
```

**响应示例：**
```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

## 🤖 智能体接口

### 获取智能体标签
**GET** `/agent/tags`

获取智能体分类标签列表

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "tag_id": "productivity",
      "tag_name": "效率工具",
      "description": "提升工作效率的智能体"
    },
    {
      "tag_id": "creative",
      "tag_name": "创意设计",
      "description": "创意和设计相关的智能体"
    }
  ]
}
```

### 获取智能体列表
**GET** `/agent/list`

分页获取智能体列表

**查询参数：**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| page_size | integer | 否 | 每页数量，默认20 |
| tag | string | 否 | 标签筛选 |
| keyword | string | 否 | 关键词搜索 |

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "agents": [
      {
        "agent_id": "agent_001",
        "name": "智能助手",
        "description": "通用智能助手，可以回答各种问题",
        "avatar": "https://example.com/avatar.png",
        "tags": ["productivity", "general"],
        "rating": 4.8,
        "usage_count": 1250
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 100,
      "total_pages": 5
    }
  }
}
```

### 获取智能体详情
**GET** `/agent/{agent_id}`

获取指定智能体的详细信息

**路径参数：**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| agent_id | string | 是 | 智能体ID |

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "agent_id": "agent_001",
    "name": "智能助手",
    "description": "通用智能助手，可以回答各种问题",
    "avatar": "https://example.com/avatar.png",
    "tags": ["productivity", "general"],
    "rating": 4.8,
    "usage_count": 1250,
    "features": [
      "多轮对话",
      "文档分析",
      "代码生成"
    ],
    "model_config": {
      "model": "gpt-3.5-turbo",
      "temperature": 0.7,
      "max_tokens": 2000
    }
  }
}
```

### 智能体对话
**POST** `/agent/chat`

与智能体进行对话

**请求参数：**
```json
{
  "agent_id": "agent_001",
  "query": "你好，请介绍一下自己",
  "conversation_id": "",  // 可选，为空表示新对话
  "inputs": {},  // 可选，额外输入参数
  "files": [],   // 可选，文件列表
  "response_mode": "streaming",  // streaming 或 blocking
  "user": "user_123"  // 可选，用户标识
}
```

**响应示例（流式）：**
```
data: {"event": "message", "data": {"content": "你好！"}}
data: {"event": "message", "data": {"content": "我是智能助手"}}
data: {"event": "message_end", "data": {"conversation_id": "conv_123"}}
```

**响应示例（非流式）：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "answer": "你好！我是智能助手，很高兴为您服务。",
    "conversation_id": "conv_123",
    "message_id": "msg_456"
  }
}
```

## 💬 对话接口

### 获取对话列表
**GET** `/chat/conversations`

获取用户的对话历史列表

**查询参数：**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| page_size | integer | 否 | 每页数量，默认20 |

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "conversations": [
      {
        "conversation_id": 1,
        "title": "关于AI的讨论",
        "model_name": "gpt-3.5-turbo",
        "message_count": 10,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T11:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 50,
      "total_pages": 3
    }
  }
}
```

### 获取对话历史
**GET** `/chat/history`

获取指定对话的消息历史

**查询参数：**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| conversation_id | integer | 是 | 对话ID |
| page | integer | 否 | 页码，默认1 |
| page_size | integer | 否 | 每页数量，默认50 |

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "messages": [
      {
        "message_id": 1,
        "role": "user",
        "content": "你好",
        "timestamp": "2024-01-01T10:00:00Z"
      },
      {
        "message_id": 2,
        "role": "assistant",
        "content": "你好！有什么可以帮助您的吗？",
        "timestamp": "2024-01-01T10:00:05Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 50,
      "total": 10,
      "total_pages": 1
    }
  }
}
```

### 发送消息
**POST** `/chat/message`

发送聊天消息

**请求参数：**
```json
{
  "conversation_id": 1,  // 可选，为空表示新对话
  "message": "请解释一下机器学习",
  "model": "gpt-3.5-turbo",  // 可选
  "stream": true  // 可选，是否流式响应
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "conversation_id": 1,
    "message_id": 3,
    "response": "机器学习是人工智能的一个分支..."
  }
}
```

### 删除对话
**DELETE** `/chat/conversations/delete`

删除指定对话

**请求参数：**
```json
{
  "conversation_id": 1
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "对话删除成功",
  "data": null
}
```

## 📁 文件接口

### 文件上传
**POST** `/file/upload`

上传文档文件

**请求格式：** `multipart/form-data`

**请求参数：**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| file | file | 是 | 上传的文件 |
| description | string | 否 | 文件描述 |

**支持格式：** PDF, DOC, DOCX, MD, TXT

**响应示例：**
```json
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "file_id": 1,
    "filename": "document.pdf",
    "file_size": 1024000,
    "file_type": "application/pdf",
    "upload_time": "2024-01-01T10:00:00Z",
    "file_url": "/uploads/document.pdf"
  }
}
```

### 文件删除
**DELETE** `/file/delete`

删除指定文件

**请求参数：**
```json
{
  "file_id": 1
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "文件删除成功",
  "data": null
}
```

## 🔧 错误处理

### 错误响应格式
```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": {
    "type": "ValidationError",
    "details": "username字段不能为空"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 常见错误码
| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 1001 | 用户名或密码错误 | 检查登录凭据 |
| 1002 | 令牌已过期 | 使用refresh_token刷新 |
| 1003 | 令牌无效 | 重新登录获取令牌 |
| 2001 | 智能体不存在 | 检查智能体ID |
| 2002 | 智能体未启用 | 联系管理员启用 |
| 3001 | 文件格式不支持 | 使用支持的文件格式 |
| 3002 | 文件大小超限 | 压缩文件或分割上传 |

## 📝 使用示例

### JavaScript/Axios示例
```javascript
// 登录
const login = async (username, password) => {
  try {
    const response = await axios.post('/auth/login', {
      username,
      password
    });
    
    const { access_token, refresh_token } = response.data.data;
    localStorage.setItem('access_token', access_token);
    localStorage.setItem('refresh_token', refresh_token);
    
    return response.data;
  } catch (error) {
    console.error('登录失败:', error.response.data);
    throw error;
  }
};

// 发送消息
const sendMessage = async (message, conversationId = null) => {
  try {
    const response = await axios.post('/chat/message', {
      conversation_id: conversationId,
      message: message,
      stream: false
    }, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('发送消息失败:', error.response.data);
    throw error;
  }
};
```

### Python/Requests示例
```python
import requests

# 登录
def login(username, password):
    response = requests.post('http://localhost:5000/auth/login', json={
        'username': username,
        'password': password
    })
    
    if response.status_code == 200:
        data = response.json()['data']
        return data['access_token'], data['refresh_token']
    else:
        raise Exception(f"登录失败: {response.json()['message']}")

# 获取智能体列表
def get_agents(access_token, page=1, page_size=20):
    headers = {'Authorization': f'Bearer {access_token}'}
    response = requests.get(
        'http://localhost:5000/agent/list',
        params={'page': page, 'page_size': page_size},
        headers=headers
    )
    
    if response.status_code == 200:
        return response.json()['data']
    else:
        raise Exception(f"获取智能体列表失败: {response.json()['message']}")
```

---

**📖 API文档版本**: v1.0.0  
**📅 最后更新**: 2024-01-01  
**📧 技术支持**: <EMAIL>
