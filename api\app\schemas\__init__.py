"""
API模型定义包
集中导出所有API模型，便于路由文件导入使用
"""

# 认证相关API模型
from .auth_schemas import (
    login_model,
    token_response_model,
    user_info_model
)

# 工具相关API模型
from .tools_schemas import (
    online_tools_info_model,
    create_online_tool_model,
    update_online_tool_model
)

# 渠道相关API模型
from .channel_schemas import (
    channel_create_model,
    channel_response_model,
    model_create_model,
    model_response_model,
    channel_id_model,
    model_operation_model
)

# 管理员相关API模型
from .admin_schemas import (
    admin_ns,
    system_monitor_model,
    system_health_model,
    dashboard_overview_model,
    charts_data_model,
    success_response_model,
    error_response_model
)

# 智能体相关API模型
from .agent import (
    agent_chat_request_model,
    agent_item_model,
    agent_list_data_model,
    agent_list_response_model,
    agent_detail_response_model,
    agent_tags_response_model,
    agent_chat_response_model,
    agent_conversation_list_params_model,
    agent_conversation_item_model,
    agent_conversation_list_response_model,
    chat_file_item_model,
    file_upload_response_model,
    file_upload_data_model,
    agent_item_in_list_model,
    agent_chat_data_model,
    # 新增参数信息模型
    agent_parameters_response_model,
    agent_parameters_data_model
)

# 统一导出
__all__ = [
    # 认证相关
    'login_model',
    'token_response_model', 
    'user_info_model',
    # 工具相关
    'online_tools_info_model',
    'create_online_tool_model',
    'update_online_tool_model',
    # 渠道相关
    'channel_create_model',
    'channel_response_model',
    'model_create_model',
    'model_response_model',
    'channel_id_model',
    'model_operation_model',
    # 智能体相关
    'agent_chat_request_model',
    'agent_item_model',
    'agent_list_data_model',
    'agent_list_response_model',
    'agent_detail_response_model',
    'agent_tags_response_model',
    'agent_chat_response_model',
    'agent_conversation_list_params_model',
    'agent_conversation_item_model',
    'agent_conversation_list_response_model',
    'chat_file_item_model',
    'file_upload_response_model',
    'file_upload_data_model',
    'agent_item_in_list_model',
    'agent_chat_data_model',
    # 新增参数信息模型
    'agent_parameters_response_model',
    'agent_parameters_data_model'
] 