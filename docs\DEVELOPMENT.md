# 🔧 PUMI Agent 开发规范

> **企业级AI智能体平台开发规范和最佳实践指南**

## 📋 开发流程规范

### 🔄 Git工作流

#### 分支策略
```mermaid
gitgraph
    commit id: "main"
    branch develop
    checkout develop
    commit id: "dev-1"
    branch feature/user-auth
    checkout feature/user-auth
    commit id: "feat-1"
    commit id: "feat-2"
    checkout develop
    merge feature/user-auth
    commit id: "dev-2"
    checkout main
    merge develop
    commit id: "release-1.0"
```

#### 分支命名规范
| 分支类型 | 命名格式 | 示例 | 说明 |
|----------|----------|------|------|
| **主分支** | `main` | `main` | 生产环境代码 |
| **开发分支** | `develop` | `develop` | 开发环境集成 |
| **功能分支** | `feature/功能名` | `feature/user-auth` | 新功能开发 |
| **修复分支** | `fix/问题描述` | `fix/login-error` | Bug修复 |
| **热修复** | `hotfix/紧急修复` | `hotfix/security-patch` | 生产环境紧急修复 |
| **发布分支** | `release/版本号` | `release/v1.0.0` | 发布准备 |

#### 提交信息规范
```bash
# 格式：<类型>(<范围>): <描述>
# 
# 类型：
feat: ✨ 新功能
fix: 🐛 修复bug
docs: 📝 文档更新
style: 💄 代码格式调整
refactor: ♻️ 代码重构
test: ✅ 测试相关
chore: 🔧 构建过程或辅助工具的变动
perf: ⚡ 性能优化
ci: 👷 CI/CD配置
revert: ⏪ 回滚提交

# 示例：
git commit -m "feat(auth): 添加JWT令牌刷新功能"
git commit -m "fix(chat): 修复消息发送失败问题"
git commit -m "docs(api): 更新API接口文档"
```

#### Pull Request规范
```markdown
## 📝 变更描述
简要描述本次变更的内容和目的

## 🎯 变更类型
- [ ] 新功能 (feature)
- [ ] Bug修复 (fix)
- [ ] 文档更新 (docs)
- [ ] 代码重构 (refactor)
- [ ] 性能优化 (perf)
- [ ] 测试相关 (test)

## 🔍 测试清单
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成
- [ ] 代码审查完成

## 📋 相关Issue
关闭 #123

## 📸 截图/演示
如有UI变更，请提供截图或演示视频

## 🔗 相关链接
- 设计稿：[链接]
- 技术文档：[链接]
```

### 🏗️ 开发环境配置

#### 开发工具推荐
| 工具 | 用途 | 配置 |
|------|------|------|
| **VSCode** | 代码编辑器 | 安装推荐插件 |
| **Git** | 版本控制 | 配置用户信息和SSH |
| **Docker** | 容器化开发 | 安装Docker Desktop |
| **Postman** | API测试 | 导入API集合 |

#### VSCode插件配置
```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.flake8",
    "ms-python.black-formatter",
    "vue.volar",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "ms-vscode-remote.remote-containers"
  ]
}
```

#### 开发环境变量
```bash
# .env.development
FLASK_ENV=development
FLASK_DEBUG=True
DATABASE_URL=mysql://root:password@localhost:3306/pumi_agent_dev
REDIS_URL=redis://localhost:6379/0
JWT_SECRET_KEY=dev-secret-key
LOG_LEVEL=DEBUG
```

## 🐍 后端开发规范

### 📁 项目结构规范
```
api/
├── app/
│   ├── core/           # 核心组件
│   ├── models/         # 数据模型
│   ├── routes/         # API路由
│   ├── services/       # 业务逻辑
│   ├── schemas/        # 数据验证
│   └── utils/          # 工具函数
├── tests/              # 测试代码
├── migrations/         # 数据库迁移
└── requirements.txt    # 依赖管理
```

### 🔧 代码规范

#### Python代码风格
```python
# 使用Black格式化
# 使用flake8检查
# 使用mypy类型检查

# 示例：服务类
class UserService:
    """用户服务类"""
    
    def __init__(self, db: SQLAlchemy, jwt_manager: JWTManager):
        self.db = db
        self.jwt_manager = jwt_manager
    
    def create_user(self, user_data: Dict[str, Any]) -> User:
        """
        创建用户
        
        Args:
            user_data: 用户数据字典
            
        Returns:
            User: 创建的用户对象
            
        Raises:
            ValidationError: 数据验证失败
            DatabaseError: 数据库操作失败
        """
        try:
            # 验证数据
            schema = UserCreateSchema()
            validated_data = schema.load(user_data)
            
            # 创建用户
            user = User(**validated_data)
            self.db.session.add(user)
            self.db.session.commit()
            
            logger.info(f"用户创建成功: {user.username}")
            return user
            
        except ValidationError as e:
            logger.error(f"用户数据验证失败: {e}")
            raise
        except Exception as e:
            self.db.session.rollback()
            logger.error(f"用户创建失败: {e}")
            raise DatabaseError("用户创建失败")
```

#### API路由规范
```python
from flask_restx import Namespace, Resource, fields
from app.core.auth_decorators import jwt_required, require_role

# 命名空间定义
auth_ns = Namespace('auth', description='认证相关接口')

# 数据模型定义
login_model = auth_ns.model('Login', {
    'username': fields.String(required=True, description='用户名'),
    'password': fields.String(required=True, description='密码')
})

# 路由实现
@auth_ns.route('/login')
class LoginResource(Resource):
    @auth_ns.expect(login_model)
    @auth_ns.doc('user_login')
    def post(self):
        """用户登录"""
        try:
            data = request.get_json()
            result = auth_service.login(data)
            return {
                'code': 200,
                'message': '登录成功',
                'data': result
            }
        except ValidationError as e:
            return {
                'code': 400,
                'message': '参数错误',
                'error': str(e)
            }, 400
```

#### 数据模型规范
```python
from sqlalchemy import Column, Integer, String, DateTime, Text
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timezone

class User(db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    # 主键
    id = Column(Integer, primary_key=True, comment='用户ID')
    
    # 基本信息
    username = Column(String(50), unique=True, nullable=False, comment='用户名')
    email = Column(String(100), unique=True, nullable=True, comment='邮箱')
    
    # 时间戳
    created_at = Column(
        DateTime, 
        default=lambda: datetime.now(timezone.utc),
        comment='创建时间'
    )
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        comment='更新时间'
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self) -> str:
        return f'<User {self.username}>'
```

### 🧪 测试规范

#### 单元测试
```python
import pytest
from app import create_app, db
from app.models import User

@pytest.fixture
def app():
    """创建测试应用"""
    app = create_app('testing')
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

@pytest.fixture
def client(app):
    """创建测试客户端"""
    return app.test_client()

class TestUserService:
    """用户服务测试"""
    
    def test_create_user_success(self, app):
        """测试用户创建成功"""
        with app.app_context():
            user_data = {
                'username': 'testuser',
                'email': '<EMAIL>'
            }
            
            user = user_service.create_user(user_data)
            
            assert user.username == 'testuser'
            assert user.email == '<EMAIL>'
            assert user.id is not None
    
    def test_create_user_duplicate_username(self, app):
        """测试重复用户名"""
        with app.app_context():
            # 创建第一个用户
            user_data = {'username': 'testuser'}
            user_service.create_user(user_data)
            
            # 尝试创建重复用户名的用户
            with pytest.raises(ValidationError):
                user_service.create_user(user_data)
```

#### API测试
```python
class TestAuthAPI:
    """认证API测试"""
    
    def test_login_success(self, client):
        """测试登录成功"""
        response = client.post('/auth/login', json={
            'username': 'testuser',
            'password': 'testpass'
        })
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['code'] == 200
        assert 'access_token' in data['data']
    
    def test_login_invalid_credentials(self, client):
        """测试登录失败"""
        response = client.post('/auth/login', json={
            'username': 'wronguser',
            'password': 'wrongpass'
        })
        
        assert response.status_code == 401
        data = response.get_json()
        assert data['code'] == 401
```

## 🌐 前端开发规范

### 📁 项目结构规范
```
web/src/
├── assets/         # 静态资源
├── components/     # 公共组件
├── layouts/        # 布局组件
├── views/          # 页面组件
├── stores/         # 状态管理
├── services/       # API服务
├── utils/          # 工具函数
├── router/         # 路由配置
└── styles/         # 样式文件
```

### 🎨 Vue组件规范

#### 组件命名
```javascript
// 组件文件命名：PascalCase
UserProfile.vue
ChatMessage.vue
AgentCard.vue

// 组件注册：PascalCase
import UserProfile from '@/components/UserProfile.vue'

// 模板使用：kebab-case
<user-profile :user="currentUser" />
<chat-message :message="message" />
```

#### 组件结构
```vue
<template>
  <div class="user-profile">
    <!-- 模板内容 -->
    <div class="user-info">
      <h2>{{ user.name }}</h2>
      <p>{{ user.email }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/userStore'

// Props定义
const props = defineProps({
  userId: {
    type: Number,
    required: true
  },
  showEmail: {
    type: Boolean,
    default: true
  }
})

// Emits定义
const emit = defineEmits(['update', 'delete'])

// 响应式数据
const loading = ref(false)
const user = ref(null)

// Store使用
const userStore = useUserStore()

// 计算属性
const displayName = computed(() => {
  return user.value?.name || '未知用户'
})

// 方法定义
const fetchUser = async () => {
  loading.value = true
  try {
    user.value = await userStore.fetchUser(props.userId)
  } catch (error) {
    console.error('获取用户信息失败:', error)
  } finally {
    loading.value = false
  }
}

const handleUpdate = () => {
  emit('update', user.value)
}

// 生命周期
onMounted(() => {
  fetchUser()
})
</script>

<style scoped lang="scss">
.user-profile {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  
  .user-info {
    h2 {
      margin: 0 0 10px 0;
      color: #333;
    }
    
    p {
      margin: 0;
      color: #666;
    }
  }
}
</style>
```

#### Pinia Store规范
```javascript
// stores/userStore.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { userAPI } from '@/services/api'

export const useUserStore = defineStore('user', () => {
  // State
  const currentUser = ref(null)
  const users = ref([])
  const loading = ref(false)
  
  // Getters
  const isLoggedIn = computed(() => !!currentUser.value)
  const userCount = computed(() => users.value.length)
  
  // Actions
  const login = async (credentials) => {
    loading.value = true
    try {
      const response = await userAPI.login(credentials)
      currentUser.value = response.data.user
      
      // 存储令牌
      localStorage.setItem('access_token', response.data.access_token)
      
      return response.data
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const logout = () => {
    currentUser.value = null
    localStorage.removeItem('access_token')
  }
  
  const fetchUsers = async () => {
    loading.value = true
    try {
      const response = await userAPI.getUsers()
      users.value = response.data
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  return {
    // State
    currentUser,
    users,
    loading,
    // Getters
    isLoggedIn,
    userCount,
    // Actions
    login,
    logout,
    fetchUsers
  }
}, {
  // 持久化配置
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: ['currentUser']
  }
})
```

### 🎨 样式规范

#### CSS类命名 (BEM)
```scss
// Block__Element--Modifier
.chat-message {
  padding: 10px;
  
  &__content {
    font-size: 14px;
    
    &--highlighted {
      background-color: yellow;
    }
  }
  
  &__timestamp {
    font-size: 12px;
    color: #999;
  }
  
  &--sent {
    text-align: right;
    background-color: #007bff;
  }
  
  &--received {
    text-align: left;
    background-color: #f1f1f1;
  }
}
```

#### 响应式设计
```scss
// 移动端优先
.component {
  padding: 10px;
  
  // 平板
  @media (min-width: 768px) {
    padding: 20px;
  }
  
  // 桌面端
  @media (min-width: 1024px) {
    padding: 30px;
  }
}
```

## 🔍 代码审查规范

### 审查清单

#### 功能性
- [ ] 功能是否按需求正确实现
- [ ] 边界条件是否处理
- [ ] 错误处理是否完善
- [ ] 性能是否满足要求

#### 代码质量
- [ ] 代码是否易读易懂
- [ ] 命名是否规范
- [ ] 注释是否充分
- [ ] 是否遵循项目规范

#### 安全性
- [ ] 输入验证是否充分
- [ ] 权限控制是否正确
- [ ] 敏感信息是否泄露
- [ ] SQL注入等安全问题

#### 测试
- [ ] 单元测试是否覆盖
- [ ] 集成测试是否通过
- [ ] 手动测试是否完成

### 审查流程
1. **自检** - 提交前自我审查
2. **同行审查** - 团队成员交叉审查
3. **技术负责人审查** - 架构和设计审查
4. **测试验证** - QA团队验证
5. **合并代码** - 审查通过后合并

## 📊 性能优化规范

### 后端性能
```python
# 数据库查询优化
# 使用索引
class User(db.Model):
    username = db.Column(db.String(50), index=True)
    email = db.Column(db.String(100), index=True)

# 避免N+1查询
users = User.query.options(joinedload(User.profile)).all()

# 使用缓存
@cache.memoize(timeout=300)
def get_user_profile(user_id):
    return User.query.get(user_id)
```

### 前端性能
```javascript
// 组件懒加载
const UserProfile = defineAsyncComponent(() => 
  import('@/components/UserProfile.vue')
)

// 图片懒加载
<img v-lazy="imageUrl" alt="description" />

// 防抖处理
import { debounce } from 'lodash-es'

const searchUsers = debounce(async (keyword) => {
  const users = await userAPI.search(keyword)
  userList.value = users
}, 300)
```

## 📝 文档规范

### API文档
- 使用Swagger/OpenAPI规范
- 包含请求/响应示例
- 详细的错误码说明
- 认证方式说明

### 代码注释
```python
def calculate_similarity(text1: str, text2: str) -> float:
    """
    计算两个文本的相似度
    
    Args:
        text1: 第一个文本
        text2: 第二个文本
        
    Returns:
        float: 相似度分数，范围0-1
        
    Raises:
        ValueError: 当输入文本为空时
        
    Example:
        >>> calculate_similarity("hello", "hello world")
        0.8
    """
    pass
```

### README文档
- 项目简介和特性
- 安装和配置说明
- 使用示例
- 贡献指南
- 许可证信息

---

**🔧 开发规范版本**: v1.0.0  
**📅 最后更新**: 2024-01-01  
**📧 技术支持**: <EMAIL>
