"""
聊天服务，处理聊天业务逻辑
"""

from typing import AsyncGenerator, Dict, List, Optional, Tuple, Any
from sqlalchemy.orm import Session
from app.adapters.channels.factory import ChannelFactory
from app.adapters.channels.base import BaseChannel
from app.services.model_router import ModelRouter
from app.models.chat import Conversation, ChatMessage
from app.models.channel import ChannelList
from app.utils.error_handlers import handle_service_errors, handle_async_service_errors
import logging

def format_message_history(messages: List) -> List[Dict[str, str]]:
    """
    格式化消息历史，适配不同模型的消息
    
    Args:
        messages: 消息列表
        
    Returns:
        格式化后的消息列表
    """
    formatted_history = []
    for msg in messages:
        # 跳过特殊消息，如PPT消息
        if hasattr(msg, "model_name") and msg.model_name == "ppt_generator":
            continue
            
        formatted_history.append({
            "role": msg.role,
            "content": msg.content
        })
    return formatted_history

class ChatService:
    """聊天服务，处理聊天逻辑"""

    def __init__(self, db: Session):
        """
        初始化聊天服务
        
        Args:
            db: 数据库会话
        """
        self.db = db
        self.router = ModelRouter(db)
        self.logger = logging.getLogger('werkzeug')
    
    @handle_async_service_errors
    async def process_message(
        self,
        user_id: int,
        model_name: str,
        message: str,
        conversation_id: Optional[str] = None,
        stream: bool = True,
        file_ids: Optional[List[str]] = None,
        **kwargs
    ) -> AsyncGenerator[Dict, None]:
        """
        处理用户消息并生成回复
        
        Args:
            user_id: 用户ID
            model_name: 模型名称
            message: 用户消息
            conversation_id: 对话ID（可选）
            stream: 是否使用流式响应
            file_ids: 要关联的文件ID列表
            **kwargs: 额外参数，如enable_thinking等
            
        Yields:
            包含响应内容、状态等信息的字典
        """
        self.logger.error(f"===== ChatService.process_message 开始: 用户ID={user_id}, 模型={model_name}, 流式={stream} =====")
        
        # 路由到合适的渠道
        result = self.router.get_best_channel_for_model(model_name)
        if not result:
            self.logger.error(f"===== 未找到支持模型 {model_name} 的渠道 =====")
            yield {
                "error": f"未找到支持模型 {model_name} 的渠道",
                "status": "error",
                "conversation_id": None,
                "title": None
            }
            return
            
        channel_id, channel_info = result
        self.logger.error(f"===== 已路由到渠道: {channel_info.channel_name} (ID: {channel_id}, 类型: {channel_info.channel_api_type}) =====")
        
        # 获取或创建对话
        conversation, history = await self._get_or_create_conversation(
            user_id, conversation_id, channel_id, model_name, message, file_ids
        )
        
        # 获取文件上下文
        enhanced_message = message
        context = ""
        if conversation.files:
            context = await self._get_context_from_files(conversation.conversation_id, message)
            
        if context:
            self.logger.info(f"已添加文件上下文，上下文长度: {len(context)} 字符")
            enhanced_message = f"""请基于以下文件内容回答问题:
            
{context}

用户问题: {message}"""
        
        # 保存用户消息
        chat_message = ChatMessage(
            conversation_id=conversation.conversation_id,
            channel_id=channel_id,
            role="user",
            content=message,
            model_name=model_name
        )
        self.db.add(chat_message)
        self.db.commit()
        self.db.refresh(chat_message)
        
        # 创建渠道实例
        self.logger.error(f"===== 开始创建渠道实例: 类型={channel_info.channel_api_type} =====")
        try:
            channel = ChannelFactory.create_channel(
                channel_type=channel_info.channel_api_type,
                base_url=channel_info.channel_url,
                api_key=channel_info.channel_api_key
            )
            self.logger.error(f"===== 渠道实例创建成功: {channel.__class__.__name__} =====")
        except Exception as e:
            self.logger.error(f"===== 渠道实例创建失败: {str(e)} =====", exc_info=True)
            yield {
                "error": f"创建渠道实例失败: {str(e)}",
                "status": "error",
                "conversation_id": conversation.conversation_id,
                "title": conversation.title
            }
            return
        
        # 调用渠道的聊天功能，传递增强后的消息
        full_response = ""
        try:
            self.logger.error(f"===== 开始调用渠道聊天功能: 模型={model_name}, 流式={stream} =====")
            # 增加一个调试信息
            self.logger.error(f"===== 渠道信息: type={channel.__class__.__name__}, base_url={channel.base_url} =====")
            
            # 记录调用的参数
            params_log = {
                "model_name": model_name,
                "message_length": len(enhanced_message),
                "history_length": len(history) if history else 0,
                "stream": stream,
                "kwargs": str(kwargs)
            }
            self.logger.error(f"===== 渠道调用参数: {params_log} =====")
            
            call_count = 0
            async for chunk in channel.chat(
                model_name=model_name,
                message=enhanced_message,
                history=history,
                stream=stream,
                **kwargs
            ):
                call_count += 1
                if call_count == 1:
                    self.logger.error(f"===== 渠道首次返回内容: [{chunk}] =====")
                    
                if chunk.startswith("Error:"):
                    self.logger.error(f"===== 渠道返回错误: {chunk} =====")
                    yield {
                        "error": chunk,
                        "status": "error",
                        "conversation_id": conversation.conversation_id,
                        "title": conversation.title
                    }
                else:
                    full_response += chunk
                    if call_count % 10 == 1:
                        self.logger.error(f"===== 渠道返回进度: 已收到 {call_count} 个响应块 =====")
                    yield {
                        "content": chunk,
                        "status": "generating",
                        "conversation_id": conversation.conversation_id,
                        "title": conversation.title
                    }
            self.logger.error(f"===== 渠道聊天功能调用完成，共收到 {call_count} 个响应块，响应长度: {len(full_response)} 字符 =====")
        except Exception as e:
            # 处理渠道异常
            from app.utils.errors import (
                ChannelConnectionError, 
                ChannelAuthError,
                ChannelModelError,
                ChannelRateLimitError,
                ChannelTimeoutError
            )
            
            self.logger.error(f"===== 渠道聊天功能调用异常: {str(e)} =====", exc_info=True)
            error_message = "处理消息时发生错误"
            error_details = str(e)
            
            # 根据异常类型设置适当的错误消息
            if isinstance(e, ChannelTimeoutError):
                error_message = f"{channel_info.channel_api_type}渠道请求超时"
                error_details = "网络连接超时，请检查网络或稍后重试"
            elif isinstance(e, ChannelConnectionError):
                error_message = f"{channel_info.channel_api_type}渠道连接失败"
                # 提取更具体的错误信息
                if "details" in str(e):
                    error_details = str(e).split("details='")[-1].split("'")[0] if "details='" in str(e) else "请检查网络连接和渠道配置"
                else:
                    error_details = "无法连接到API服务器，请检查网络或稍后重试"
            elif isinstance(e, ChannelAuthError):
                error_message = f"{channel_info.channel_api_type}渠道认证失败"
                error_details = "API密钥无效或已过期，请检查渠道配置"
            elif isinstance(e, ChannelModelError):
                error_message = f"{channel_info.channel_api_type}渠道模型错误"
                error_details = f"模型 {model_name} 不可用，请选择其他模型"
            elif isinstance(e, ChannelRateLimitError):
                error_message = f"{channel_info.channel_api_type}渠道请求超限"
                error_details = "API调用频率超限，请稍后重试或检查账户配额"
            
            yield {
                "error": f"{error_message}: {error_details}",
                "status": "error",
                "conversation_id": conversation.conversation_id,
                "title": conversation.title
            }
            return
        finally:
            # 保存AI响应
            if full_response:
                assistant_message = ChatMessage(
                    conversation_id=conversation.conversation_id,
                    channel_id=channel_id,
                    role="assistant",
                    content=full_response,
                    model_name=model_name
                )
                self.db.add(assistant_message)
                self.db.commit()
                self.logger.info(f"已保存AI响应，长度: {len(full_response)} 字符")
            
            # 发送一个完成标记，但不包含完整内容
            # 这样客户端能知道流已结束，但不会收到重复的合并内容
                yield {
                "content": "",  # 不发送内容，避免重复
                "status": "completed",  # 状态标记为完成
                    "conversation_id": conversation.conversation_id,
                    "title": conversation.title
                }
    
    async def _get_context_from_files(
        self, 
        conversation_id: str, 
        query: str, 
        max_context_length: int = 200000  # 调整为200k字符，约占128k token的60-70%
    ) -> str:
        """
        从对话关联的文件中获取markdown内容作为上下文
        
        Args:
            conversation_id: 对话ID
            query: 用户问题（暂未使用，为了保持接口兼容）
            max_context_length: 最大上下文长度（默认200k字符，适用于128k token的大上下文模型）
            
        Returns:
            包含文件内容的上下文字符串
        """
        # 动态导入，避免循环依赖
        try:
            from app.services.file.file_processor import FileProcessor
        except ImportError as e:
            print(f"导入FileProcessor失败: {e}")
            return ""
        from app.models.chat import Conversation
        
        conversation = self.db.query(Conversation).filter(
            Conversation.conversation_id == conversation_id
        ).first()
        
        if not conversation or not conversation.files:
            return ""
            
        # 获取已处理的文件ID列表
        file_ids = [file.file_id for file in conversation.files if file.status == "processed"]
        if not file_ids:
            return ""
            
        # 获取文件内容
        file_processor = FileProcessor(self.db)
        files_content = file_processor.get_files_content(
            file_ids=file_ids,
            user_id=str(conversation.user_id),
            max_content_length=max_context_length // len(file_ids) if len(file_ids) > 1 else max_context_length
        )
        
        # 构建上下文
        context_parts = []
        total_length = 0
        
        for file_info in files_content:
            content = file_info.get("content", "")
            filename = file_info.get("filename", "未知文件")
            
            # 如果添加这个文件会超出最大长度，则截断
            if total_length + len(content) > max_context_length:
                remaining_length = max_context_length - total_length
                if remaining_length > 1000:  # 调整为至少保留1000字符，对大文档更有意义
                    content = content[:remaining_length] + "...[内容已截断]"
                    context_parts.append(f"[文件: {filename}]\n{content}")
                break
            
            # 添加到上下文
            context_parts.append(f"[文件: {filename}]\n{content}")
            total_length += len(content) + len(filename) + 10  # +10 for formatting
            
            if total_length >= max_context_length:
                break
        
        if not context_parts:
            return ""
        
        return "\n\n".join(context_parts)
    
    async def _get_or_create_conversation(
        self, 
        user_id: int, 
        conversation_id: Optional[str],
        channel_id: int,
        model_name: str,
        message: str,
        file_ids: Optional[List[str]] = None
    ) -> Tuple[Any, List]:
        """
        获取现有对话或创建新对话
        
        Args:
            user_id: 用户ID
            conversation_id: 对话ID
            channel_id: 渠道ID
            model_name: 模型名称
            message: 用户消息
            file_ids: 要关联的文件ID列表
            
        Returns:
            (conversation, history) 元组
        """
        # 确保用户ID是整数类型
        if not isinstance(user_id, int):
            try:
                user_id = int(user_id)
            except (ValueError, TypeError):
                self.logger.error(f"用户ID格式错误: {user_id}, 类型: {type(user_id)}")
                raise ValueError("用户ID必须为整数")
                
        # 获取渠道信息
        channel_info = self.db.query(ChannelList).filter(ChannelList.channel_id == channel_id).first()
        if not channel_info:
            from app.utils.errors import ResourceNotFound
            raise ResourceNotFound('渠道')
        
        # 初始化为空列表
        history = []
        
        if conversation_id:
            # 使用现有对话
            conversation = self.db.query(Conversation).filter(
                Conversation.conversation_id == conversation_id
            ).first()
            
            if not conversation:
                from app.utils.errors import ResourceNotFound
                raise ResourceNotFound('对话')
                
            # 如果指定了文件ID且对话没有关联文件，则添加关联
            if file_ids and not conversation.files:
                from app.models.file import File as FileModel
                files = self.db.query(FileModel).filter(
                    FileModel.file_id.in_(file_ids),
                    FileModel.user_id == user_id
                ).all()
                
                self.logger.info(f"找到{len(files)}个文件与用户ID {user_id} 匹配")
                conversation.files.extend(files)
                self.db.commit()
                
            # 获取历史消息
            history_messages = self.db.query(ChatMessage).filter(
                ChatMessage.conversation_id == conversation.conversation_id
            ).order_by(ChatMessage.created_at.asc()).all()
            
            # 新增调试日志
            self.logger.error(f"===== 历史消息数量: {len(history_messages)} 条 =====")
            for idx, msg in enumerate(history_messages):
                self.logger.error(f"历史消息#{idx+1} | role: {msg.role} | content: {msg.content[:50]}...")
            
            history = format_message_history(history_messages)
            self.logger.error(f"===== 传递给模型的历史消息数量: {len(history)} 条 =====")
            for idx, msg in enumerate(history):
                self.logger.error(f"模型历史#{idx+1} | role: {msg['role']} | content: {msg['content'][:50]}...")
        else:
            # 创建渠道实例生成标题
            channel = ChannelFactory.create_channel(
                channel_type=channel_info.channel_api_type,
                base_url=channel_info.channel_url,
                api_key=channel_info.channel_api_key
            )
            
            # 使用AI生成对话标题
            try:
                title = await channel.generate_title(model_name, message)
            except Exception:
                # 如果生成失败，使用消息前缀作为标题
                title = message[:8]
                
            # 创建新对话
            conversation = Conversation(user_id=user_id, title=title)
            self.db.add(conversation)
            self.db.commit()
            self.db.refresh(conversation)
            
            # 如果指定了文件ID，则建立对话-文件关联
            if file_ids:
                from app.models.file import File as FileModel
                files = self.db.query(FileModel).filter(
                    FileModel.file_id.in_(file_ids),
                    FileModel.user_id == user_id
                ).all()
                
                self.logger.info(f"为新对话找到{len(files)}个文件与用户ID {user_id} 匹配")
                if not files:
                    self.logger.warning(f"未找到匹配的文件，用户ID: {user_id}, 文件IDs: {file_ids}")
                
                conversation.files.extend(files)
                self.db.commit()
            
        return conversation, history 