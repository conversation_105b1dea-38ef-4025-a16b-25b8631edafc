<template>
  <div class="admin-models">




    <!-- 主要内容 -->
    <div class="main-content">
      <div class="container">
        <!-- 统计卡片 -->
        <div class="stats-row">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Cpu /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ totalModels }}</div>
                <div class="stat-label">总模型数</div>
              </div>
            </div>
          </el-card>
          
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon enabled">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ enabledModels }}</div>
                <div class="stat-label">已启用</div>
              </div>
            </div>
          </el-card>
          
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon disabled">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ disabledModels }}</div>
                <div class="stat-label">已禁用</div>
              </div>
            </div>
          </el-card>
          
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ totalChannels }}</div>
                <div class="stat-label">渠道数量</div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 模型列表表格 -->
        <el-card class="table-card">
          <template #header>
            <div class="card-header">
              <span>模型列表</span>
              <div class="header-actions">
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索模型名称"
                  style="width: 250px"
                  size="small"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>

                <el-select v-model="filterChannel" placeholder="选择渠道" clearable size="small" style="width: 150px">
                  <el-option label="全部渠道" value="" />
                  <el-option
                    v-for="channel in channels"
                    :key="channel.channel_id"
                    :label="channel.channel_name"
                    :value="channel.channel_id"
                  />
                </el-select>

                <el-select v-model="filterStatus" placeholder="状态" clearable size="small" style="width: 120px">
                  <el-option label="全部状态" value="" />
                  <el-option label="已启用" value="enabled" />
                  <el-option label="已禁用" value="disabled" />
                </el-select>

                <el-button type="primary" size="small" @click="showAddModelDialog = true">
                  <el-icon><Plus /></el-icon>
                  添加模型
                </el-button>

                <el-button size="small" @click="refreshData">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          
          <el-table 
            :data="filteredModels" 
            v-loading="loading"
            style="width: 100%"
            :header-cell-style="{ background: '#fafbfc', color: '#606266' }"
          >
            <el-table-column prop="model_name" label="模型名称" min-width="200">
              <template #default="{ row }">
                <div class="model-name">
                  <el-icon class="model-icon"><Cpu /></el-icon>
                  {{ row.model_name }}
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="channel_name" label="所属渠道" min-width="150">
              <template #default="{ row }">
                <el-tag :type="getChannelTagType(row.channel_type)" size="small">
                  {{ row.channel_name }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="channel_type" label="渠道类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getTypeTagType(row.channel_type)" size="small">
                  {{ row.channel_type }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="model_enabled" label="状态" width="100">
              <template #default="{ row }">
                <el-switch
                  v-model="row.model_enabled"
                  @change="toggleModelStatus(row)"
                  :loading="row.switching"
                />
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button 
                  type="danger" 
                  size="small" 
                  text
                  @click="deleteModel(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="filteredModels.length"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </div>
    </div>

    <!-- 添加模型对话框 -->
    <el-dialog v-model="showAddModelDialog" title="添加模型" width="500px">
      <el-form :model="addModelForm" label-width="100px">
        <el-form-item label="选择渠道" required>
          <el-select v-model="addModelForm.channel_id" placeholder="请选择渠道" style="width: 100%">
            <el-option 
              v-for="channel in channels" 
              :key="channel.channel_id"
              :label="`${channel.channel_name} (${channel.channel_type})`" 
              :value="channel.channel_id" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="模型名称" required>
          <el-input
            v-model="addModelForm.model_names"
            type="textarea"
            :rows="4"
            placeholder="请输入模型名称，多个模型用换行分隔&#10;例如：&#10;gpt-3.5-turbo&#10;gpt-4&#10;claude-3-sonnet"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddModelDialog = false">取消</el-button>
        <el-button type="primary" @click="addModels" :loading="addingModel">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Refresh,
  Cpu,
  Check,
  Close,
  Connection
} from '@element-plus/icons-vue'
import * as modelService from '@/services/modelService'

// 响应式数据
const loading = ref(false)
const models = ref([])
const channels = ref([])
const showAddModelDialog = ref(false)
const addingModel = ref(false)

// 筛选条件
const filterChannel = ref('')
const filterStatus = ref('')
const searchKeyword = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 添加模型表单
const addModelForm = ref({
  channel_id: '',
  model_names: ''
})

// 计算属性
const totalModels = computed(() => models.value.length)
const enabledModels = computed(() => models.value.filter(m => m.model_enabled).length)
const disabledModels = computed(() => models.value.filter(m => !m.model_enabled).length)
const totalChannels = computed(() => channels.value.length)

const filteredModels = computed(() => {
  let filtered = models.value

  // 渠道筛选
  if (filterChannel.value) {
    filtered = filtered.filter(m => m.channel_id === filterChannel.value)
  }

  // 状态筛选
  if (filterStatus.value) {
    const enabled = filterStatus.value === 'enabled'
    filtered = filtered.filter(m => m.model_enabled === enabled)
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(m => 
      m.model_name.toLowerCase().includes(keyword) ||
      m.channel_name.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

// 方法
const getChannelTagType = (type) => {
  const typeMap = {
    'openai': 'success',
    'ollama': 'info',
    'zhipu': 'warning'
  }
  return typeMap[type] || 'info'
}

const getTypeTagType = (type) => {
  const typeMap = {
    'openai': 'success',
    'ollama': 'info', 
    'zhipu': 'warning'
  }
  return typeMap[type] || 'info'
}

const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadModels(),
      loadChannels()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('数据刷新失败:', error)
    ElMessage.error(`数据刷新失败: ${error.message || '未知错误'}`)
  } finally {
    loading.value = false
  }
}

const loadModels = async () => {
  try {
    const fullModelData = await modelService.getFullModelData()
    models.value = fullModelData
  } catch (error) {
    console.error('加载模型数据失败:', error)
    throw error
  }
}

const loadChannels = async () => {
  try {
    const channelData = await modelService.getChannelList()
    channels.value = channelData
  } catch (error) {
    console.error('加载渠道数据失败:', error)
    throw error
  }
}

const toggleModelStatus = async (model) => {
  model.switching = true
  const originalStatus = model.model_enabled

  try {
    await modelService.changeModelStatus(model.channel_id, model.model_name)
    ElMessage.success(`模型 ${model.model_name} 状态已更新`)
  } catch (error) {
    // 恢复原状态
    model.model_enabled = originalStatus
    console.error('切换模型状态失败:', error)
    ElMessage.error(`状态更新失败: ${error.response?.data?.message || error.message || '未知错误'}`)
  } finally {
    model.switching = false
  }
}

const deleteModel = async (model) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模型 "${model.model_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 注意：后端可能没有提供删除接口，这里暂时提示功能未实现
    ElMessage.warning('删除功能暂未实现，请联系管理员')

    // 如果后端提供了删除接口，可以使用以下代码：
    // await modelService.deleteModel(model.channel_id, model.model_name)
    // await refreshData()
    // ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模型失败:', error)
      ElMessage.error(`删除失败: ${error.response?.data?.message || error.message || '未知错误'}`)
    }
  }
}

const addModels = async () => {
  if (!addModelForm.value.channel_id || !addModelForm.value.model_names.trim()) {
    ElMessage.warning('请填写完整信息')
    return
  }

  addingModel.value = true
  try {
    const modelNames = addModelForm.value.model_names
      .split('\n')
      .map(name => name.trim())
      .filter(name => name)

    if (modelNames.length === 0) {
      ElMessage.warning('请输入至少一个模型名称')
      return
    }

    await modelService.addModels(addModelForm.value.channel_id, modelNames)

    ElMessage.success(`成功添加 ${modelNames.length} 个模型`)
    showAddModelDialog.value = false
    addModelForm.value = { channel_id: '', model_names: '' }
    await refreshData()
  } catch (error) {
    console.error('添加模型失败:', error)
    ElMessage.error(`添加失败: ${error.response?.data?.message || error.message || '未知错误'}`)
  } finally {
    addingModel.value = false
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.admin-models {
  padding: 0;
  overflow-y: auto;
  background: var(--bg-secondary);
  min-height: 100vh;
}



/* 容器样式 */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-xl);
  box-sizing: border-box;
}

/* 主要内容样式 */
.main-content {
  flex: 1;
}

/* 统计卡片样式 */
.stats-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  transition: all var(--transition-normal);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
  background: var(--bg-primary);
  overflow: hidden;
  position: relative;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: #409eff;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  gap: var(--spacing-md);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-gradient);
  color: white;
  font-size: 24px;
}

.stat-icon.enabled {
  background: linear-gradient(135deg, #10B981 0%, #34D399 100%);
}

.stat-icon.disabled {
  background: linear-gradient(135deg, #EF4444 0%, #F87171 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 表格卡片样式 */
.table-card {
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
  background: var(--bg-primary);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  flex-wrap: wrap;
}

/* 表格样式 */
.model-name {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.model-icon {
  color: #409eff;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-lg);
  padding: var(--spacing-lg) 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
  }

  .header-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }
}

@media (max-width: 768px) {
  .stats-row {
    grid-template-columns: 1fr;
  }

  .container {
    padding: 0 var(--spacing-md);
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions .el-input,
  .header-actions .el-select {
    width: 100% !important;
  }
}
</style>
