"""
在线工具模型
包含在线工具的信息
"""

from app import db


class OnlineTools(db.Model):
    """在线工具模型"""
    __tablename__ = "online_tools"
    
    tools_id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键ID')
    
    tools_name = db.Column(
        db.String(255), 
        nullable=False,
        comment='工具名称'
    )
    tools_description = db.Column(
        db.String(255), 
        nullable=False,
        comment='工具描述'
    )
    tools_url = db.Column(
        db.String(255), 
        nullable=False,
        comment='工具URL'
    )

    def __repr__(self):
        return f'<OnlineTools {self.tools_name}>'
