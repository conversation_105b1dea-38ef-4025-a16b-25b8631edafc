/**
 * Axios配置和拦截器
 * 统一处理请求头中的令牌添加、401错误自动刷新令牌和请求重试
 *
 * 注意：这是项目中唯一的令牌刷新机制，已移除其他重复的刷新逻辑：
 * - 移除了apiStore.js中的refreshToken方法
 * - 简化了tokenRefreshService.js为状态查询服务
 * - 所有令牌刷新都通过此拦截器统一处理，避免竞态条件
 */
import axios from 'axios';
import router from '@/router';
import * as tokenService from './tokenService';
import { useApiConfigStore } from '@/stores/apiConfig';

// 创建一个独立的axios实例，用于刷新令牌，避免循环依赖
const refreshAxios = axios.create({
  baseURL: '' // 确保与主应用axios使用相同的基础URL
});

// 用于跟踪令牌是否正在刷新
let isRefreshing = false;
// 存储等待令牌刷新的请求队列
let requestsQueue = [];

/**
 * 处理等待队列中的请求
 * @param {string|null} token - 新的访问令牌，如果为null表示刷新失败
 * @param {boolean} isError - 是否因为错误而处理队列
 */
const processQueue = (token, isError = false) => {
  requestsQueue.forEach(({ resolve, reject }) => {
    if (isError) {
      reject(new Error('令牌刷新失败'));
    } else {
      resolve(token);
    }
  });
  // 处理完毕，清空队列
  requestsQueue = [];
};

/**
 * 刷新令牌并处理相关状态
 * @returns {Promise<string>} 新的访问令牌
 */
const refreshTokenAndUpdateState = async () => {
  try {
    const refreshToken = tokenService.getRefreshToken();
    if (!refreshToken) {
      throw new Error('没有可用的刷新令牌');
    }
    
    // 获取API配置
    const apiConfig = useApiConfigStore();
    const refreshUrl = apiConfig.getUrl('refresh_token');
    
    console.log('正在刷新令牌，URL:', refreshUrl);
    
    // 使用独立的axios实例直接调用刷新接口
    const response = await refreshAxios.post(refreshUrl, {
      refresh_token: refreshToken
    });
    
    console.log('令牌刷新成功:', response.data);
    
    const { access_token, refresh_token } = response.data;
    
    // 更新tokenService中的令牌
    tokenService.setTokens(access_token, refresh_token);
    
    // 刷新成功，返回新令牌
    return access_token;
  } catch (error) {
    // 刷新失败，清除用户状态并重定向到登录页
    console.error('刷新令牌失败:', error);
    
    // 如果是网络错误或服务器错误，给出更具体的错误信息
    if (error.response) {
      console.error('服务器响应错误:', error.response.status, error.response.data);
    } else if (error.request) {
      console.error('网络错误，无法连接到服务器');
    } else {
      console.error('请求配置错误:', error.message);
    }
    
    tokenService.clearTokens();
    router.push('/login');
    throw error;
  } finally {
    // 无论成功失败，刷新过程都已结束
    isRefreshing = false;
  }
};

// 请求拦截器
axios.interceptors.request.use(
  async (config) => {
    // 跳过带有_skipAuthRefresh标记的请求（如refreshToken自身）
    if (config._skipAuthRefresh) {
      return config;
    }
    
    // 获取API配置
    const apiConfig = useApiConfigStore();
    
    // 需要跳过添加令牌的接口（如登录、刷新令牌）
    const skipAuthUrls = [apiConfig.endpoints.login, apiConfig.endpoints.refresh_token];
    if (skipAuthUrls.some(url => config.url && config.url.includes(url))) {
      return config;
    }
    
    let accessToken = tokenService.getAccessToken();
    
    // 检查令牌是否有效
    if (!accessToken) {
      console.warn('没有可用的访问令牌');
      return config;
    }
    
    // 如果令牌已过期，尝试使用刷新令牌
    if (tokenService.isTokenExpired(accessToken)) {
      console.warn('访问令牌已过期，尝试刷新...');
      
      // 如果已经在刷新，加入等待队列
      if (isRefreshing) {
        try {
          accessToken = await new Promise((resolve, reject) => {
            requestsQueue.push({ resolve, reject });
          });
        } catch (error) {
          console.error('等待令牌刷新失败', error);
          return config;
        }
      } else {
        // 开始刷新流程
        isRefreshing = true;
        try {
          accessToken = await refreshTokenAndUpdateState();
        } catch (error) {
          console.error('令牌刷新失败', error);
          return config;
        }
      }
    }
    // 检查令牌是否即将过期，如果是则主动刷新
    else if (tokenService.isTokenNearExpiry(accessToken)) {
      console.log('令牌即将过期，准备刷新...');
      
      // 如果已经在刷新，加入等待队列
      if (isRefreshing) {
        try {
          accessToken = await new Promise((resolve, reject) => {
            requestsQueue.push({ resolve, reject });
          });
        } catch (error) {
          console.error('等待令牌刷新失败', error);
          return config;
        }
      } else {
        // 开始刷新流程
        isRefreshing = true;
        try {
          accessToken = await refreshTokenAndUpdateState();
        } catch (error) {
          console.error('主动刷新令牌失败', error);
          // 继续使用旧令牌，让后续的401响应处理程序处理
          return config;
        }
      }
    }
    
    // 添加令牌到请求头
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
axios.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    // 如果没有响应，则可能是网络错误
    if (!error.response) {
      console.error('网络错误:', error);
      return Promise.reject(error);
    }
    
    const originalRequest = error.config;
    
    console.error('请求失败:', error.response.status, error.response.statusText);
    
    // 防止无限递归，标记请求已经尝试过刷新令牌
    if (error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      console.log('收到401错误，开始刷新令牌流程...');
      
      // 如果已经在刷新令牌，加入等待队列
      if (isRefreshing) {
        try {
          const token = await new Promise((resolve, reject) => {
            requestsQueue.push({ resolve, reject });
          });
          
          if (!token) {
            console.error('令牌刷新失败，令牌为null');
            return Promise.reject(error);
          }
          
          // 使用新令牌更新请求并重试
          originalRequest.headers.Authorization = `Bearer ${token}`;
          console.log('使用新令牌重试请求');
          return axios(originalRequest);
        } catch (refreshError) {
          console.error('等待令牌刷新失败', refreshError);
          return Promise.reject(refreshError);
        }
      }
      
      // 开始刷新令牌流程
      isRefreshing = true;
      
      try {
        const newToken = await refreshTokenAndUpdateState();
        
        if (!newToken) {
          console.error('令牌刷新失败，返回的令牌为null');
          processQueue(null, true);
          return Promise.reject(error);
        }
        
        // 使用新令牌，并通知所有等待的请求
        processQueue(newToken);
        
        // 更新当前请求的令牌并重试
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        console.log('令牌刷新成功，重试原始请求');
        return axios(originalRequest);
      } catch (refreshError) {
        console.error('令牌刷新失败，清除所有等待的请求', refreshError);
        // 刷新失败，拒绝所有等待的请求
        processQueue(null, true);
        return Promise.reject(refreshError);
      }
    }
    
    // 处理其他错误状态码
    if (error.response.status === 403) {
      console.warn('权限不足:', error.response.data);
    } else if (error.response.status === 500) {
      console.error('服务器内部错误:', error.response.data);
    }
    
    return Promise.reject(error);
  }
); 