<template>
  <div class="categories-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">分类管理</h2>
        <p class="page-description">管理AIGC工具的分类信息和排序</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加分类
        </el-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><FolderOpened /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ categories.length }}</div>
          <div class="stat-label">总分类数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon active">
          <el-icon><Check /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ activeCategories }}</div>
          <div class="stat-label">启用分类</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon tools">
          <el-icon><Tools /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ totalTools }}</div>
          <div class="stat-label">关联工具</div>
        </div>
      </div>
    </div>

    <!-- 分类列表 -->
    <div class="categories-container">
      <div class="container-header">
        <h3>分类列表</h3>
        <div class="header-actions">
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <div ref="categoriesGrid" class="categories-grid">
        <div
          v-for="(category, index) in categories"
          :key="category.id"
          class="category-card"
          :class="{ inactive: !category.isActive }"
        >
          <div class="category-header">
            <div class="category-icon" :style="{ backgroundColor: category.color + '20', color: category.color }">
              <span>{{ category.icon }}</span>
            </div>
            <div class="category-info">
              <h4 class="category-name">{{ category.name }}</h4>
              <p class="category-description">{{ category.description || '暂无描述' }}</p>
            </div>
            <div class="category-actions">
              <el-dropdown @command="handleCommand">
                <el-button size="small" text>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`edit-${category.id}`">编辑</el-dropdown-item>
                    <el-dropdown-item :command="`toggle-${category.id}`">
                      {{ category.isActive ? '禁用' : '启用' }}
                    </el-dropdown-item>
                    <el-dropdown-item :command="`delete-${category.id}`" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          
          <div class="category-stats">
            <div class="stat-item">
              <span class="stat-label">工具数量</span>
              <span class="stat-value">{{ category.toolCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">排序权重</span>
              <span class="stat-value">{{ category.sortOrder || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">状态</span>
              <el-tag :type="category.isActive ? 'success' : 'info'" size="small">
                {{ category.isActive ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </div>

          <div class="category-footer">
            <div class="drag-handle">
              <el-icon><Rank /></el-icon>
              <span>拖拽排序</span>
            </div>
            <div class="category-time">
              创建于 {{ formatDate(category.createdAt) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑分类对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingCategory ? '编辑分类' : '添加分类'"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="categoryFormRef"
        :model="categoryForm"
        :rules="categoryFormRules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类描述" prop="description">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>
        <el-form-item label="分类图标" prop="icon">
          <el-input v-model="categoryForm.icon" placeholder="请输入emoji图标" style="width: 200px" />
          <span class="icon-preview">{{ categoryForm.icon }}</span>
        </el-form-item>
        <el-form-item label="主题色" prop="color">
          <el-color-picker v-model="categoryForm.color" />
          <span class="color-preview" :style="{ backgroundColor: categoryForm.color }"></span>
        </el-form-item>
        <el-form-item label="排序权重" prop="sortOrder">
          <el-input-number v-model="categoryForm.sortOrder" :min="0" :max="999" />
          <span class="sort-tip">数值越大排序越靠前</span>
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="categoryForm.isActive" active-text="启用" inactive-text="禁用" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCategory" :loading="saving">
          {{ editingCategory ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  FolderOpened,
  Check,
  Tools,
  Refresh,
  MoreFilled,
  Rank
} from '@element-plus/icons-vue'
import Sortable from 'sortablejs'
import {
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  getStats
} from '@/services/aigcService'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showAddDialog = ref(false)
const editingCategory = ref(null)
const categoriesGrid = ref(null)

// 分类数据
const categories = ref([])
const stats = ref({
  total_tools: 0,
  active_tools: 0,
  total_categories: 0,
  active_categories: 0
})

// 表单数据
const categoryForm = ref({
  name: '',
  description: '',
  icon: '',
  color: '#1677ff',
  sortOrder: 0,
  isActive: true
})

// 表单验证规则
const categoryFormRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ],
  icon: [
    { required: true, message: '请输入分类图标', trigger: 'blur' }
  ],
  color: [
    { required: true, message: '请选择主题色', trigger: 'change' }
  ]
}

// 数据加载函数
const loadCategories = async () => {
  try {
    loading.value = true
    const data = await getCategories()
    categories.value = data.map(cat => ({
      id: cat.id,
      name: cat.name,
      description: cat.description,
      icon: cat.icon,
      color: cat.color,
      sortOrder: cat.sort_order,
      isActive: cat.is_active,
      toolCount: cat.tool_count || 0,
      createdAt: cat.created_at
    }))

    // 数据加载完成后重新初始化拖拽
    await nextTick()
    await initDragSort()
  } catch (error) {
    console.error('加载分类失败:', error)
    ElMessage.error('加载分类失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const data = await getStats()
    stats.value = data
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 初始化拖拽排序
const initDragSort = async () => {
  await nextTick()
  if (categoriesGrid.value) {
    Sortable.create(categoriesGrid.value, {
      animation: 150,
      ghostClass: 'sortable-ghost',
      chosenClass: 'sortable-chosen',
      dragClass: 'sortable-drag',
      handle: '.drag-handle',
      onEnd: async (evt) => {
        const { oldIndex, newIndex } = evt
        if (oldIndex !== newIndex) {
          // 更新本地数组顺序
          const movedItem = categories.value.splice(oldIndex, 1)[0]
          categories.value.splice(newIndex, 0, movedItem)

          // 更新排序值并调用API
          await updateCategoriesOrder()
        }
      }
    })
  }
}

// 更新分类排序
const updateCategoriesOrder = async () => {
  try {
    // 根据新的顺序更新sort_order
    const updates = categories.value.map((category, index) => ({
      id: category.id,
      sort_order: (categories.value.length - index) * 10 // 倒序，数值越大越靠前
    }))

    // 批量更新排序
    for (const update of updates) {
      await updateCategory(update.id, { sort_order: update.sort_order })
    }

    ElMessage.success('排序更新成功')
  } catch (error) {
    console.error('更新排序失败:', error)
    ElMessage.error('排序更新失败')
    // 重新加载数据恢复原始顺序
    await loadCategories()
  }
}

// 计算属性
const activeCategories = computed(() => {
  return stats.value.active_categories || 0
})

const totalTools = computed(() => {
  return stats.value.total_tools || 0
})

// 方法
const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const handleCommand = (command) => {
  const [action, id] = command.split('-')
  const category = categories.value.find(cat => cat.id === parseInt(id))

  if (!category) {
    console.error('找不到分类:', id, categories.value)
    return
  }
  
  switch (action) {
    case 'edit':
      editCategory(category)
      break
    case 'toggle':
      toggleCategoryStatus(category)
      break
    case 'delete':
      deleteCategoryItem(category)
      break
  }
}

const editCategory = (category) => {
  editingCategory.value = category
  categoryForm.value = { ...category }
  showAddDialog.value = true
}

const toggleCategoryStatus = async (category) => {
  try {
    const newStatus = !category.isActive
    await updateCategory(category.id, { is_active: newStatus })
    category.isActive = newStatus
    ElMessage.success(`${category.name} 已${newStatus ? '启用' : '禁用'}`)
    await loadStats()
  } catch (error) {
    console.error('切换状态失败:', error)
    ElMessage.error('操作失败')
  }
}

const deleteCategoryItem = async (category) => {
  if (category.toolCount > 0) {
    ElMessage.warning('该分类下还有工具，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除分类 "${category.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteCategory(category.id)
    ElMessage.success('删除成功')
    await loadCategories()
    await loadStats()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 1000)
}

const resetForm = () => {
  categoryForm.value = {
    name: '',
    description: '',
    icon: '',
    color: '#1677ff',
    sortOrder: 0,
    isActive: true
  }
  editingCategory.value = null
}

const saveCategory = async () => {
  try {
    saving.value = true

    const formData = {
      name: categoryForm.value.name,
      description: categoryForm.value.description,
      icon: categoryForm.value.icon,
      color: categoryForm.value.color,
      sort_order: categoryForm.value.sortOrder,
      is_active: categoryForm.value.isActive
    }

    if (editingCategory.value) {
      // 更新分类
      await updateCategory(editingCategory.value.id, formData)
      ElMessage.success('更新成功')
    } else {
      // 创建分类
      await createCategory(formData)
      ElMessage.success('添加成功')
    }

    showAddDialog.value = false
    resetForm()
    await loadCategories()
    await loadStats()

  } catch (error) {
    console.error('保存分类失败:', error)
    ElMessage.error(editingCategory.value ? '更新失败' : '添加失败')
  } finally {
    saving.value = false
  }
}

// 生命周期
onMounted(async () => {
  await loadCategories()
  await loadStats()
  await initDragSort()
})
</script>

<style scoped>
.categories-management {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  color: #6b7280;
  font-size: 20px;
}

.stat-icon.active {
  background: #dcfce7;
  color: #16a34a;
}

.stat-icon.tools {
  background: #dbeafe;
  color: #2563eb;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-top: 4px;
}

/* 分类容器 */
.categories-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
  overflow: hidden;
}

.container-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.container-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 分类网格 */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  padding: 24px;
}

.category-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background: #fafbfc;
  transition: all 0.2s;
  cursor: pointer;
}

.category-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.category-card.inactive {
  opacity: 0.6;
  background: #f9fafb;
}

.category-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.category-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.category-info {
  flex: 1;
  min-width: 0;
}

.category-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.category-description {
  font-size: 13px;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.category-actions {
  flex-shrink: 0;
}

.category-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px 0;
  border-top: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-item .stat-label {
  font-size: 12px;
  color: #9ca3af;
}

.stat-item .stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.category-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.drag-handle {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #9ca3af;
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

/* 拖拽状态样式 */
.sortable-ghost {
  opacity: 0.4;
  transform: scale(0.95);
}

.sortable-chosen {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 999;
}

.sortable-drag {
  transform: rotate(5deg);
  opacity: 0.8;
}

.category-time {
  font-size: 12px;
  color: #9ca3af;
}

/* 表单样式 */
.icon-preview {
  margin-left: 12px;
  font-size: 20px;
  padding: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  min-width: 40px;
  text-align: center;
}

.color-preview {
  margin-left: 12px;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.sort-tip {
  margin-left: 12px;
  font-size: 12px;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .categories-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .categories-grid {
    grid-template-columns: 1fr;
    padding: 16px;
  }

  .category-stats {
    flex-direction: column;
    gap: 8px;
  }

  .stat-item {
    flex-direction: row;
    justify-content: space-between;
  }
}
</style>
