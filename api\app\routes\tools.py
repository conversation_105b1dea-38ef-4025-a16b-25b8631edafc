"""
在线工具相关路由
包含工具的增删改查功能
"""

from flask import request
from flask_restx import Namespace, Resource

from app.core.auth_decorators import jwt_required

from app.services.tools_service import ToolService
from app.schemas.tools_schemas import (
    online_tools_info_model,
    create_online_tool_model,
    update_online_tool_model
)
from app.utils.dependencies import get_current_active_user


tools_ns = Namespace('online_tools', description='在线工具')

# 将模型添加到当前命名空间
tools_ns.models[online_tools_info_model.name] = online_tools_info_model
tools_ns.models[create_online_tool_model.name] = create_online_tool_model
tools_ns.models[update_online_tool_model.name] = update_online_tool_model


@tools_ns.route('/list')
class ToolList(Resource):
    @tools_ns.marshal_list_with(online_tools_info_model)
    def get(self):
        """获取所有在线工具列表"""
        try:
            tools = ToolService.get_all_online_tools()
            return tools
        except Exception as e:
            tools_ns.abort(500, f"服务器内部错误: {str(e)}")


@tools_ns.route('/search')
class ToolSearch(Resource):
    @tools_ns.marshal_list_with(online_tools_info_model)
    def get(self):
        """根据名称搜索工具"""
        try:
            name = request.args.get('name', '').strip()
            if not name:
                tools_ns.abort(400, "请提供搜索关键词")
            
            tools = ToolService.get_tools_by_name(name)
            return tools
        except Exception as e:
            tools_ns.abort(500, f"服务器内部错误: {str(e)}")


@tools_ns.route('/')
class ToolManagement(Resource):
    @jwt_required()
    @tools_ns.expect(create_online_tool_model)
    @tools_ns.marshal_with(online_tools_info_model)
    @tools_ns.doc(security=['Login'])
    def post(self):
        """创建新的在线工具（需要登录）"""
        try:
            # 验证用户权限
            user = get_current_active_user()
            if not user.is_admin and not user.is_teacher:
                tools_ns.abort(403, "只有管理员和教师可以创建工具")
            
            data = request.get_json()
            tools_name = data.get('tools_name')
            tools_description = data.get('tools_description')
            tools_url = data.get('tools_url')
            
            if not all([tools_name, tools_description, tools_url]):
                tools_ns.abort(400, "工具名称、描述和URL都不能为空")
            
            # 创建工具
            tool = ToolService.create_online_tool(
                tools_name=tools_name,
                tools_description=tools_description,
                tools_url=tools_url
            )
            
            return tool
            
        except Exception as e:
            tools_ns.abort(500, f"服务器内部错误: {str(e)}")


@tools_ns.route('/<int:tools_id>')
class ToolDetail(Resource):
    @tools_ns.marshal_with(online_tools_info_model)
    def get(self, tools_id):
        """获取指定工具详情"""
        try:
            tool = ToolService.get_online_tool_by_id(tools_id)
            if not tool:
                tools_ns.abort(404, "工具不存在")
            
            return tool
            
        except Exception as e:
            tools_ns.abort(500, f"服务器内部错误: {str(e)}")
    
    @jwt_required()
    @tools_ns.expect(update_online_tool_model)
    @tools_ns.marshal_with(online_tools_info_model)
    @tools_ns.doc(security=['Login'])
    def put(self, tools_id):
        """更新指定工具（需要登录）"""
        try:
            # 验证用户权限
            user = get_current_active_user()
            if not user.is_admin and not user.is_teacher:
                tools_ns.abort(403, "只有管理员和教师可以更新工具")
            
            # 检查工具是否存在
            existing_tool = ToolService.get_online_tool_by_id(tools_id)
            if not existing_tool:
                tools_ns.abort(404, "工具不存在")
            
            data = request.get_json()
            
            # 过滤掉空值
            update_data = {k: v for k, v in data.items() if v is not None and v != ""}
            
            if not update_data:
                tools_ns.abort(400, "没有提供要更新的数据")
            
            # 更新工具
            updated_tool = ToolService.update_online_tool(tools_id, update_data)
            
            return updated_tool
            
        except Exception as e:
            tools_ns.abort(500, f"服务器内部错误: {str(e)}")
    
    @jwt_required()
    @tools_ns.doc(security=['Login'])
    def delete(self, tools_id):
        """删除指定工具（需要登录）"""
        try:
            # 验证用户权限
            user = get_current_active_user()
            if not user.is_admin:
                tools_ns.abort(403, "只有管理员可以删除工具")
            
            # 删除工具
            success = ToolService.delete_online_tool(tools_id)
            if not success:
                tools_ns.abort(404, "工具不存在")
            
            return {"message": "工具删除成功"}
            
        except Exception as e:
            tools_ns.abort(500, f"服务器内部错误: {str(e)}")


# 配置安全认证
authorizations = {
    'Login': {
        'type': 'oauth2',
        'flow': 'password',
        'tokenUrl': '/auth/login',
        'description': '使用用户名和密码登录获取JWT令牌'
    }
}

tools_ns.authorizations = authorizations