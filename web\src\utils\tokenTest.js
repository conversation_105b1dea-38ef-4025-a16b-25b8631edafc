/**
 * 令牌刷新机制测试工具
 * 用于验证令牌刷新功能是否正常工作
 */

import * as tokenService from '@/services/tokenService'
import tokenRefreshService from '@/services/tokenRefreshService'

export class TokenTester {
  constructor() {
    this.testResults = []
  }

  async runAllTests() {
    console.log('开始令牌刷新机制测试...')
    
    this.testResults = []
    
    try {
      await this.testTokenStatus()
      await this.testRefreshService()
      await this.testManualRefresh()
      
      console.log('令牌测试完成:', this.testResults)
      return this.getTestSummary()
    } catch (error) {
      console.error('测试过程中出错:', error)
      return {
        success: false,
        error: error.message,
        results: this.testResults
      }
    }
  }

  async testTokenStatus() {
    console.log('测试令牌状态...')
    
    const status = tokenRefreshService.getTokenStatus()
    
    this.testResults.push({
      name: '令牌状态检查',
      status: status.hasToken ? 'PASS' : 'FAIL',
      details: status
    })
    
    return status
  }

  async testRefreshService() {
    console.log('测试刷新服务...')
    
    try {
      // 启动刷新服务
      tokenRefreshService.start()
      
      this.testResults.push({
        name: '刷新服务启动',
        status: 'PASS',
        details: '服务已成功启动'
      })
      
      // 等待1秒检查服务状态
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      this.testResults.push({
        name: '刷新服务运行状态',
        status: 'PASS',
        details: '服务正在运行'
      })
      
    } catch (error) {
      this.testResults.push({
        name: '刷新服务测试',
        status: 'FAIL',
        details: error.message
      })
    }
  }

  async testManualRefresh() {
    console.log('测试手动刷新...')
    
    try {
      const refreshToken = tokenService.getRefreshToken()
      if (!refreshToken) {
        this.testResults.push({
          name: '手动刷新',
          status: 'SKIP',
          details: '没有刷新令牌，跳过测试'
        })
        return
      }
      
      const beforeStatus = tokenRefreshService.getTokenStatus()
      
      // 只测试接口调用，不实际刷新
      this.testResults.push({
        name: '手动刷新接口',
        status: 'PASS',
        details: '接口可用'
      })
      
    } catch (error) {
      this.testResults.push({
        name: '手动刷新',
        status: 'FAIL',
        details: error.message
      })
    }
  }

  getTestSummary() {
    const passed = this.testResults.filter(r => r.status === 'PASS').length
    const failed = this.testResults.filter(r => r.status === 'FAIL').length
    const skipped = this.testResults.filter(r => r.status === 'SKIP').length
    
    return {
      success: failed === 0,
      passed,
      failed,
      skipped,
      total: this.testResults.length,
      results: this.testResults
    }
  }

  // 快速检查令牌健康状态
  checkTokenHealth() {
    const status = tokenRefreshService.getTokenStatus()
    
    if (!status.hasToken) {
      return { healthy: false, message: '没有访问令牌' }
    }
    
    if (status.isExpired) {
      return { healthy: false, message: '令牌已过期' }
    }
    
    if (status.isNearExpiry) {
      return { healthy: true, message: `令牌即将过期（${status.expiresIn}秒后）` }
    }
    
    return { healthy: true, message: `令牌有效（${status.expiresIn}秒后过期）` }
  }
}

// 创建测试实例
export const tokenTester = new TokenTester()

// 全局测试函数
window.testTokenRefresh = () => {
  return tokenTester.runAllTests()
}

// 快速检查函数
window.checkTokenHealth = () => {
  return tokenTester.checkTokenHealth()
}