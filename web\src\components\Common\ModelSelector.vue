<template>
  <div class="model-selection">
    <button class="model-select-btn" @click="toggleModelDropdown" :disabled="disabled" :class="{ 'disabled': disabled }">
      <span>{{ selectedModelName }}</span>
    </button>
    <div class="model-dropdown" v-show="showModelDropdown && !disabled">
      <div v-if="modelStore.isLoadingModels" class="model-dropdown-item loading-item">
        <div class="loading-icon"></div>
        <span>加载中...</span>
      </div>
      <div v-else-if="modelStore.modelList.length === 0" class="model-dropdown-item no-models">
        <span>暂无可用模型</span>
      </div>
      <div v-else v-for="model in modelStore.modelList" :key="model.model_id" class="model-dropdown-item"
        @click="selectModel(model)">
        <span class="model-name" :title="model.model_name">{{ model.model_name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useModelStore } from '@/stores/modelStore'

// 接收禁用状态prop
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  }
})

// 使用模型存储
const modelStore = useModelStore()

// 仅保留下拉菜单显示状态在组件内部
const showModelDropdown = ref(false)

// 发送更新事件
const emit = defineEmits(['update:model'])

// 计算当前选中的模型名称
const selectedModelName = computed(() => {
  if (!modelStore.selectedModel) return '模型选择'
  return truncateModelName(modelStore.selectedModel.model_name)
})

// 切换模型下拉菜单显示
const toggleModelDropdown = () => {
  if (props.disabled) return; // 如果禁用则不执行切换操作

  showModelDropdown.value = !showModelDropdown.value

  if (showModelDropdown.value) {
    // 如果打开下拉菜单，获取模型列表
    if (modelStore.modelList.length === 0) {
      modelStore.fetchModelList()
    }
  }
}

// 选择模型
const selectModel = (model) => {
  modelStore.setSelectedModel(model)

  // 向父组件发送选中的模型
  emit('update:model', modelStore.selectedModel)

  // 关闭下拉菜单
  showModelDropdown.value = false
}

// 处理模型名称过长的情况
const truncateModelName = (name, maxLength = 15) => {
  if (!name) return '模型选择';
  if (name.length <= maxLength) return name;
  return name.substring(0, maxLength) + '...';
}

// 组件挂载时添加点击事件监听，用于关闭下拉菜单
onMounted(() => {
  document.addEventListener('click', (e) => {
    const modelDropdown = document.querySelector('.model-selection')
    if (modelDropdown && !modelDropdown.contains(e.target)) {
      showModelDropdown.value = false
    }
  })
})
</script>

<style scoped>
/* 模型选择按钮样式 */
.model-selection {
  position: relative;
  margin-right: auto;
  margin-left: 12px;
  max-width: 180px;
}

.model-select-btn {
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 90px;
  width: 150px;
  /* 固定宽度 */
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  outline: none;
  overflow: hidden;
  /* 确保内容不会溢出 */
}

.model-select-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.model-select-btn.disabled:hover {
  background: transparent;
  border-color: #e4e7ed;
}

.model-select-btn span {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  text-align: center;
}

.model-select-btn:hover {
  background: rgba(0, 0, 0, 0.06);
  border-color: #e4e7ed;
}

.model-dropdown {
  position: absolute;
  bottom: 100%; /* 改为向上弹出 */
  left: 0;
  margin-bottom: 8px; /* 改为底部间距 */
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  width: fit-content;
  z-index: 9999;
  padding: 8px;
  transform-origin: bottom left; /* 改为底部起始 */
  animation: dropdownFadeInUp 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.model-dropdown-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  font-size: 15px;
  color: #333;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.model-dropdown-item:hover {
  background: rgba(0, 0, 0, 0.06);
  color: #409eff;
}

/* 添加特殊处理，让no-models类的元素在鼠标悬停时不改变颜色 */
.model-dropdown-item.no-models:hover {
  background: rgba(0, 0, 0, 0.03);
  color: #909399;
  cursor: default;
}

.loading-item {
  justify-content: center;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-icon {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #409eff;
  border-radius: 50%;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

.no-models {
  color: #909399;
  justify-content: center;
  width: 160px;
  margin: 0 auto;
}

@keyframes dropdownFadeInUp {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(8px); /* 向上弹出，从下方开始 */
  }

  50% {
    opacity: 0.5;
    transform: scale(1.05) translateY(4px);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 保留原来的动画以防需要 */
@keyframes dropdownFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-8px);
  }

  50% {
    opacity: 0.5;
    transform: scale(1.05) translateY(-4px);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.model-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>