import os
from urllib.parse import quote_plus
from dotenv import load_dotenv
from pathlib import Path

# 加载.env文件
# 自动查找项目根目录下的.env文件
env_path = Path(__file__).parent.parent.parent / '.env'
load_dotenv(dotenv_path=env_path)


class Config:
    """
    基础配置类
    
    使用方法：
    1. 在项目根目录创建.env文件
    2. 在.env文件中设置环境变量，例如：
       MYSQL_IP=localhost
       MYSQL_DATABASE=your_db_name
       MYSQL_USER=your_username
       MYSQL_PASSWORD=your_password
       SECRET_KEY=your_secret_key
    3. 通过os.environ.get()读取环境变量，支持默认值
    """
    
    # 数据库配置
    MYSQL_IP = os.environ.get('MYSQL_IP', 'localhost')
    MYSQL_DATABASE = os.environ.get('MYSQL_DATABASE')
    MYSQL_PORT = int(os.environ.get('MYSQL_PORT', 3306))
    MYSQL_USER = os.environ.get('MYSQL_USER')
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD')

    # JWT配置
    SECRET_KEY = os.environ.get('SECRET_KEY')
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', os.environ.get('SECRET_KEY'))
    ALGORITHM = os.environ.get('ALGORITHM', 'HS256')

    # JWT令牌过期时间配置
    JWT_ACCESS_TOKEN_EXPIRES = int(os.environ.get('JWT_ACCESS_TOKEN_EXPIRES', 30))  # 分钟
    JWT_REFRESH_TOKEN_EXPIRES = int(os.environ.get('JWT_REFRESH_TOKEN_EXPIRES', 7))  # 天

    # 环境配置
    ENV = os.environ.get('ENV', 'development')
    DEBUG = os.environ.get('DEBUG', 'False').lower() in ('true', '1', 'yes')

    # 文件上下文相关配置
    MAX_FILE_CONTEXT_LENGTH = int(os.environ.get('MAX_FILE_CONTEXT_LENGTH', 200000))
    MIN_CONTENT_LENGTH = int(os.environ.get('MIN_CONTENT_LENGTH', 1000))
    
    # 文件上传配置
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', 'uploads')
    MAX_CONTENT_LENGTH = int(os.environ.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024))  # 16MB
    
    # 外部登录服务配置
    AUTH_SERVICE_BASE_URL = os.environ.get('AUTH_SERVICE_BASE_URL', 'http://10.177.88.10:5000')
    AUTH_SERVICE_LOGIN_ENDPOINT = os.environ.get('AUTH_SERVICE_LOGIN_ENDPOINT', '/auth/login')
    AUTH_SERVICE_TIMEOUT = int(os.environ.get('AUTH_SERVICE_TIMEOUT', 10))  # 超时时间，单位秒

    # Dify API配置
    DIFY_API_URL = os.environ.get('DIFY_API_URL', 'http://***********')
    DIFY_WORKSPACE_ID = os.environ.get('DIFY_WORKSPACE_ID', 'd044326d-3594-4c9c-a795-90841dfdb1ad')
    DIFY_API_KEY = os.environ.get('DIFY_API_KEY', 'admin123')

    # 数据库URI（Flask-SQLAlchemy需要）
    @property
    def SQLALCHEMY_DATABASE_URI(self):
        if not all([self.MYSQL_USER, self.MYSQL_PASSWORD, self.MYSQL_IP, self.MYSQL_DATABASE]):
            raise ValueError("数据库配置不完整，请检查.env文件中的数据库配置项")
        # URL编码用户名和密码，处理特殊字符（如@符号）
        encoded_user = quote_plus(self.MYSQL_USER)
        encoded_password = quote_plus(self.MYSQL_PASSWORD)
        return f"mysql+pymysql://{encoded_user}:{encoded_password}@{self.MYSQL_IP}:{self.MYSQL_PORT}/{self.MYSQL_DATABASE}?charset=utf8mb4"

    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'echo': False
    }
    
    @classmethod
    def validate_config(cls):
        """验证必要的配置是否存在"""
        required_configs = [
            'MYSQL_IP', 'MYSQL_DATABASE', 'MYSQL_USER', 'MYSQL_PASSWORD', 'SECRET_KEY',
            'AUTH_SERVICE_BASE_URL'
        ]
        missing_configs = []
        
        for config_name in required_configs:
            if not getattr(cls, config_name):
                missing_configs.append(config_name)
        
        if missing_configs:
            raise ValueError(f"缺少必要的配置项: {', '.join(missing_configs)}。请在.env文件中设置这些环境变量。")
        
        return True


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'echo': True  # 开发环境显示SQL语句
    }


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    # 生产环境的额外安全配置
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'


class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True
    # 测试环境可以使用内存数据库
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'


# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}


def get_config(env_name=None):
    """
    获取配置对象
    
    Args:
        env_name: 环境名称，如果不指定则从ENV环境变量读取
    
    Returns:
        配置类实例
    """
    if env_name is None:
        env_name = os.environ.get('ENV', 'development')
    
    config_class = config.get(env_name, config['default'])
    config_instance = config_class()
    
    # 在非测试环境下验证配置
    if env_name != 'testing':
        config_instance.validate_config()
    
    return config_instance