# 🤖 普米智能体工作坊 (PUMI Agent Workshop)

> **让AI更智能，让工作更高效** - 企业级AI智能体平台解决方案

一个功能完整、架构清晰的AI智能体平台，采用前后端分离架构，支持多智能体管理、实时对话、文件处理等核心功能。

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![Vue](https://img.shields.io/badge/Vue-3.x-green.svg)](https://vuejs.org/)
[![Flask](https://img.shields.io/badge/Flask-3.0+-red.svg)](https://flask.palletsprojects.com/)

## 🌟 核心特性

### 🎯 智能体管理
- **多智能体支持** - 集成Dify平台，支持多种AI智能体
- **智能体市场** - 浏览、搜索、分类管理智能体应用
- **实时对话** - 流式响应，支持多轮对话和上下文记忆
- **文件处理** - 支持文档上传、解析和智能问答

### 🔐 企业级安全
- **JWT认证系统** - 双令牌机制，支持令牌刷新和撤销
- **外部认证集成** - 灵活的外部认证系统对接
- **权限管理** - 基于角色的访问控制(RBAC)
- **安全日志** - 完整的操作审计和安全事件记录

### 🏗️ 现代化架构
- **前后端分离** - RESTful API + SPA架构
- **微服务设计** - 模块化服务，易于扩展和维护
- **响应式设计** - 适配桌面端和移动端
- **实时通信** - WebSocket支持，实时消息推送

## 📁 项目架构

```
pumi-agent/
├── 📂 api/                    # 🐍 后端API服务 (Flask)
│   ├── 📂 app/               # 应用核心代码
│   │   ├── 📂 core/          # 核心组件 (JWT、配置、安全)
│   │   ├── 📂 models/        # 数据模型 (用户、对话、文件)
│   │   ├── 📂 routes/        # API路由 (认证、聊天、智能体)
│   │   ├── 📂 services/      # 业务逻辑服务
│   │   ├── 📂 schemas/       # 数据验证模式
│   │   └── 📂 utils/         # 工具类和客户端
│   ├── 📂 migrations/        # 数据库迁移脚本
│   ├── 📂 logs/             # 应用日志文件
│   ├── 📄 requirements.txt   # Python依赖
│   ├── 📄 run.py            # 应用启动入口
│   └── 📄 README.md         # 后端文档
├── 📂 web/                   # 🌐 前端Web应用 (Vue 3)
│   ├── 📂 src/              # 源代码
│   │   ├── 📂 components/    # Vue组件
│   │   ├── 📂 views/        # 页面视图
│   │   ├── 📂 stores/       # Pinia状态管理
│   │   ├── 📂 services/     # API服务
│   │   └── 📂 utils/        # 工具函数
│   ├── 📄 package.json      # Node.js依赖
│   ├── 📄 vite.config.js    # Vite构建配置
│   └── 📄 README.md         # 前端文档

├── 📂 docs/                 # 📚 项目文档
│   ├── 📄 API.md            # API 接口文档
│   ├── 📄 DEPLOYMENT.md     # 🚀 详细部署指南
│   └── 📄 DEVELOPMENT.md    # 🛠️ 开发指南
├── 📄 QUICKSTART.md         # ⚡ 快速开始指南
└── 📄 README.md            # 项目总览
```

## 🚀 快速开始



### 📋 本地开发环境要求

如果需要本地开发，需要以下环境：

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| **Python** | 3.8+ | 后端运行环境 |
| **Node.js** | 16+ | 前端构建环境 |
| **MySQL** | 8.0+ | 数据库服务 |
| **Redis** | 6.0+ | 缓存服务(可选) |

### 🗄️ 数据库初始化

```sql
-- 创建数据库
CREATE DATABASE pumi_agent_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER 'pumi_agent'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON pumi_agent_db.* TO 'pumi_agent'@'localhost';
FLUSH PRIVILEGES;
```

### 🔧 后端服务启动

```bash
# 进入后端目录
cd api

# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/Mac
# .venv\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp env.example .env
# 编辑 .env 文件，配置数据库连接等信息

# 初始化数据库
python init_db.py

# 启动开发服务器
python run.py
```

✅ 后端服务启动成功：`http://localhost:5000`
📖 API文档地址：`http://localhost:5000/docs/`

### 🌐 前端服务启动

```bash
# 进入前端目录
cd web

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

✅ 前端服务启动成功：`http://localhost:5173`

## 🛠️ 技术栈

### 🐍 后端技术栈
| 技术 | 版本 | 用途 |
|------|------|------|
| **Flask** | 3.0.0 | Web框架 |
| **Flask-RESTx** | 1.3.0 | API文档和验证 |
| **SQLAlchemy** | 2.0.23 | ORM数据库操作 |
| **PyMySQL** | 1.1.0 | MySQL数据库驱动 |
| **Flask-JWT-Extended** | 4.6.0 | JWT认证管理 |
| **Flask-CORS** | 4.0.0 | 跨域资源共享 |
| **Marshmallow** | 3.20.1 | 数据序列化验证 |
| **Requests** | 2.31.0 | HTTP客户端 |
| **Gunicorn** | 21.2.0 | WSGI服务器 |

### 🌐 前端技术栈
| 技术 | 版本 | 用途 |
|------|------|------|
| **Vue** | 3.5.13 | 前端框架 |
| **Vite** | 6.2.4 | 构建工具 |
| **Element Plus** | 2.9.11 | UI组件库 |
| **Pinia** | 3.0.1 | 状态管理 |
| **Vue Router** | 4.5.0 | 路由管理 |
| **Axios** | 1.9.0 | HTTP客户端 |
| **JWT-Decode** | 4.0.0 | JWT令牌解析 |
| **Marked** | 15.0.12 | Markdown渲染 |

## 🎯 功能模块

### 🔐 认证授权模块
```mermaid
graph LR
    A[用户登录] --> B[外部认证验证]
    B --> C[生成JWT令牌]
    C --> D[令牌刷新机制]
    D --> E[权限验证]
    E --> F[安全日志记录]
```

**核心特性：**
- 🔑 双令牌机制 (Access Token + Refresh Token)
- 🌐 外部认证系统集成
- 🛡️ 基于角色的权限控制 (学生/教师/管理员)
- 📝 完整的安全审计日志
- 🔒 令牌撤销和黑名单机制

### 🤖 智能体管理模块
```mermaid
graph TD
    A[智能体市场] --> B[智能体列表]
    B --> C[智能体详情]
    C --> D[智能体对话]
    D --> E[对话历史]
    E --> F[文件上传处理]
```

**核心特性：**
- 🏪 智能体应用市场
- 🔍 智能体搜索和分类
- 💬 实时流式对话
- 📁 文件上传和智能解析
- 💾 对话历史管理

### 💬 对话系统模块
```mermaid
graph LR
    A[创建对话] --> B[发送消息]
    B --> C[流式响应]
    C --> D[消息存储]
    D --> E[历史查询]
    E --> F[对话管理]
```

**核心特性：**
- 🔄 多轮对话支持
- ⚡ 实时流式响应
- 💾 对话历史持久化
- 🗂️ 对话分类管理
- 📤 消息导出功能

### 📁 文件管理模块
```mermaid
graph TD
    A[文件上传] --> B[格式检测]
    B --> C[内容解析]
    C --> D[文本提取]
    D --> E[智能问答]
    E --> F[文件管理]
```

**核心特性：**
- 📄 多格式文件支持 (PDF, Word, Markdown)
- 🔍 智能内容解析和提取
- 💬 基于文件内容的问答
- 🗃️ 文件版本管理
- 🔒 文件访问权限控制

## ⚙️ 配置说明

### 🔧 后端配置 (api/.env)

```bash
# ===========================================
# 🗄️ 数据库配置
# ===========================================
DATABASE_URL=mysql://pumi_agent:your_password@localhost:3306/pumi_agent_db
DB_POOL_SIZE=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# ===========================================
# 🔐 JWT认证配置
# ===========================================
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production-environment
JWT_ACCESS_TOKEN_EXPIRES=3600    # 访问令牌过期时间(秒)
JWT_REFRESH_TOKEN_EXPIRES=2592000 # 刷新令牌过期时间(秒，30天)

# ===========================================
# 🌐 外部认证服务配置
# ===========================================
EXTERNAL_AUTH_URL=http://your-external-auth-service.com
EXTERNAL_AUTH_TIMEOUT=30
EXTERNAL_AUTH_RETRY_COUNT=3

# ===========================================
# 🤖 Dify智能体平台配置
# ===========================================
DIFY_API_BASE_URL=https://api.dify.ai/v1
DIFY_API_TIMEOUT=60

# ===========================================
# 📝 日志配置
# ===========================================
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_MAX_BYTES=10485760  # 10MB
LOG_BACKUP_COUNT=5

# ===========================================
# 🔒 安全配置
# ===========================================
SECRET_KEY=your-flask-secret-key-change-in-production
CORS_ORIGINS=http://localhost:5173,http://localhost:3000
MAX_CONTENT_LENGTH=16777216  # 16MB文件上传限制

# ===========================================
# 🚀 应用配置
# ===========================================
FLASK_ENV=development
FLASK_DEBUG=True
HOST=0.0.0.0
PORT=5000
```

### 🌐 前端配置 (web/.env)

```bash
# ===========================================
# 🔗 API服务配置
# ===========================================
VITE_API_BASE_URL=http://localhost:5000
VITE_API_TIMEOUT=30000

# ===========================================
# 🎨 应用配置
# ===========================================
VITE_APP_TITLE=普米智能体工作坊
VITE_APP_DESCRIPTION=企业级AI智能体平台
VITE_APP_VERSION=1.0.0

# ===========================================
# 🔐 认证配置
# ===========================================
VITE_TOKEN_STORAGE_KEY=pumi_access_token
VITE_REFRESH_TOKEN_STORAGE_KEY=pumi_refresh_token
VITE_TOKEN_REFRESH_THRESHOLD=300000  # 5分钟

# ===========================================
# 📁 文件上传配置
# ===========================================
VITE_MAX_FILE_SIZE=16777216  # 16MB
VITE_ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.md,.txt

# ===========================================
# 🎯 功能开关
# ===========================================
VITE_ENABLE_DEBUG_MODE=true
VITE_ENABLE_TOKEN_DEBUG=false
VITE_ENABLE_PERFORMANCE_MONITOR=true
```

## 🚀 部署指南

### 🏠 开发环境部署

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端应用 | `http://localhost:5173` | Vue开发服务器 |
| 后端API | `http://localhost:5000` | Flask开发服务器 |
| API文档 | `http://localhost:5000/docs/` | Swagger UI |
| 数据库 | `localhost:3306` | MySQL服务 |

### 🏭 生产环境部署



#### 🔧 手动部署

**后端部署：**
```bash
# 安装依赖
cd api
pip install -r requirements.txt

# 配置生产环境
cp env.example .env
# 编辑.env文件，设置生产环境配置

# 数据库迁移
python init_db.py

# 使用Gunicorn启动
gunicorn -w 4 -b 0.0.0.0:5000 run:app
```

**前端部署：**
```bash
# 构建生产版本
cd web
npm install
npm run build

# 使用Nginx托管静态文件
# 配置Nginx反向代理到后端API
```

**Nginx配置示例：**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /path/to/web/dist;
        try_files $uri $uri/ /index.html;
    }

    # 后端API代理
    location /api/ {
        proxy_pass http://localhost:5000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔧 开发指南

### 📋 常用命令

#### 🐍 后端开发
```bash
# 环境管理
python -m venv .venv                    # 创建虚拟环境
source .venv/bin/activate               # 激活虚拟环境 (Linux/Mac)
.venv\Scripts\activate                  # 激活虚拟环境 (Windows)

# 依赖管理
pip install -r requirements.txt        # 安装依赖
pip freeze > requirements.txt          # 更新依赖列表

# 数据库操作
python init_db.py                      # 初始化数据库
python migrations/add_refresh_token_hash.py  # 执行迁移

# 开发调试
python run.py                          # 启动开发服务器
python -m pytest                       # 运行测试
python -m pytest --cov=app             # 运行测试并生成覆盖率报告

# 代码质量
flake8 app/                            # 代码风格检查
black app/                             # 代码格式化
```

#### 🌐 前端开发
```bash
# 依赖管理
npm install                            # 安装依赖
npm update                             # 更新依赖
npm audit fix                          # 修复安全漏洞

# 开发调试
npm run dev                            # 启动开发服务器
npm run build                          # 构建生产版本
npm run preview                        # 预览构建结果

# 代码质量
npm run lint                           # ESLint检查
npm run lint:fix                       # 自动修复ESLint问题
npx depcheck                           # 检查未使用的依赖
```

### 🔄 开发流程

#### Git工作流
```mermaid
gitgraph
    commit id: "main"
    branch develop
    checkout develop
    commit id: "dev-1"
    branch feature/new-feature
    checkout feature/new-feature
    commit id: "feat-1"
    commit id: "feat-2"
    checkout develop
    merge feature/new-feature
    commit id: "dev-2"
    checkout main
    merge develop
    commit id: "release"
```

#### 分支策略
- **`main`** - 🏭 生产环境分支，稳定版本
- **`develop`** - 🔧 开发环境分支，集成测试
- **`feature/*`** - ✨ 功能开发分支
- **`hotfix/*`** - 🚨 紧急修复分支
- **`release/*`** - 🚀 发布准备分支

#### 提交规范
```bash
# 提交类型
feat: ✨ 新功能
fix: 🐛 修复bug
docs: 📝 文档更新
style: 💄 代码格式调整
refactor: ♻️ 代码重构
test: ✅ 测试相关
chore: 🔧 构建过程或辅助工具的变动
perf: ⚡ 性能优化
ci: 👷 CI/CD配置

# 提交示例
git commit -m "feat: 添加智能体对话流式响应功能"
git commit -m "fix: 修复JWT令牌刷新失败问题"
git commit -m "docs: 更新API接口文档"
```

## 📚 文档资源

### 📖 项目文档
- [⚡ 快速开始](./QUICKSTART.md) - 5分钟快速部署指南
- [📘 API接口文档](./docs/API.md) - 详细的API接口说明
- [🚀 部署指南](./docs/DEPLOYMENT.md) - 生产环境部署指南
- [🛠️ 开发指南](./docs/DEVELOPMENT.md) - 开发环境配置和开发流程
- [🔧 开发规范](./docs/DEVELOPMENT.md) - 代码开发规范

### 🔗 相关链接
- [Flask官方文档](https://flask.palletsprojects.com/)
- [Vue 3官方文档](https://vuejs.org/)
- [Element Plus组件库](https://element-plus.org/)
- [Dify智能体平台](https://dify.ai/)

## 👥 团队协作

### 🎯 角色分工
- **🎯 项目经理** - 项目规划、进度管理、团队协调
- **🏗️ 架构师** - 系统设计、技术选型、架构优化
- **🐍 后端工程师** - API开发、数据库设计、服务集成
- **🌐 前端工程师** - 界面开发、用户体验、组件设计
- **🔧 DevOps工程师** - 部署运维、CI/CD、监控告警
- **🧪 测试工程师** - 质量保证、自动化测试、性能测试

### 📞 沟通协作
- **日常沟通** - 钉钉群组、微信群
- **代码协作** - Git + GitHub/GitLab
- **项目管理** - Jira/Trello/禅道
- **文档协作** - 语雀/Notion/Confluence

## 🐛 问题排查

### 🔍 常见问题

#### 后端问题
```bash
# 数据库连接失败
ERROR: Can't connect to MySQL server
解决方案: 检查MySQL服务状态，确认连接配置

# JWT令牌验证失败
ERROR: Invalid token
解决方案: 检查JWT_SECRET_KEY配置，确认令牌未过期

# 外部认证服务超时
ERROR: External auth timeout
解决方案: 检查网络连接，调整超时配置
```

#### 前端问题
```bash
# API请求跨域错误
ERROR: CORS policy blocked
解决方案: 检查后端CORS配置，确认域名白名单

# 路由404错误
ERROR: Cannot GET /path
解决方案: 检查路由配置，确认history模式设置

# 组件渲染错误
ERROR: Component render failed
解决方案: 检查组件props，确认数据格式正确
```

## 📄 许可证

本项目采用 [MIT License](https://opensource.org/licenses/MIT) 开源许可证。

```
MIT License

Copyright (c) 2024 PUMI Agent Workshop

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！请阅读以下指南：

### 🎯 贡献方式
1. **🐛 报告Bug** - 提交详细的问题描述和复现步骤
2. **✨ 功能建议** - 提出新功能想法和改进建议
3. **📝 文档改进** - 完善文档内容和示例
4. **💻 代码贡献** - 提交代码修复和新功能实现

### 📋 贡献流程
1. Fork 项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'feat: add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### ✅ 代码规范
- 遵循项目现有的代码风格
- 添加必要的测试用例
- 更新相关文档
- 确保所有测试通过

---

<div align="center">

**🤖 普米智能体工作坊 (PUMI Agent Workshop)**

*让AI更智能，让工作更高效*

[![⭐ Star](https://img.shields.io/github/stars/your-org/pumi-agent?style=social)](https://github.com/your-org/pumi-agent)
[![🍴 Fork](https://img.shields.io/github/forks/your-org/pumi-agent?style=social)](https://github.com/your-org/pumi-agent)
[![📝 Issues](https://img.shields.io/github/issues/your-org/pumi-agent)](https://github.com/your-org/pumi-agent/issues)
[![📄 License](https://img.shields.io/github/license/your-org/pumi-agent)](https://github.com/your-org/pumi-agent/blob/main/LICENSE)

</div>
