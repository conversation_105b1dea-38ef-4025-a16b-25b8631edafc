"""
在线工具API模型定义
定义在线工具相关的API输入输出模型
"""

from flask_restx import fields, Namespace

# 创建一个临时命名空间用于定义模型
_temp_ns = Namespace('schemas')

# 在线工具信息模型
online_tools_info_model = _temp_ns.model('OnlineToolsInfo', {
    'tools_id': fields.Integer(description='工具ID'),
    'tools_name': fields.String(description='工具名称'),
    'tools_description': fields.String(description='工具描述'),
    'tools_url': fields.String(description='工具URL')
})

# 创建在线工具请求模型
create_online_tool_model = _temp_ns.model('CreateOnlineToolRequest', {
    'tools_name': fields.String(required=True, description='工具名称'),
    'tools_description': fields.String(required=True, description='工具描述'),
    'tools_url': fields.String(required=True, description='工具URL')
})

# 更新在线工具请求模型
update_online_tool_model = _temp_ns.model('UpdateOnlineToolRequest', {
    'tools_name': fields.String(description='工具名称'),
    'tools_description': fields.String(description='工具描述'),
    'tools_url': fields.String(description='工具URL')
})

