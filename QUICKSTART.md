# ⚡ PUMI Agent 快速开始

## 🚀 新机器 5 分钟部署

### 第一步：环境准备
```bash
# 安装 Docker Desktop (Windows/Mac) 或 Docker Engine (Linux)
# Windows: 下载安装 Docker Desktop
# Mac: brew install --cask docker
# Linux: sudo apt install docker.io docker-compose
```

### 第二步：获取项目
```bash
git clone <your-repo-url>
cd pumi-agent
```

### 第三步：一键启动
```bash
cd docker
docker-compose up -d
```

### 第四步：验证部署
```bash
# 检查服务状态
docker-compose ps

# 访问应用
# 前端: http://localhost:8080
# API: http://localhost:8080/api/health
```

## ✅ 部署验证清单

- [ ] 所有容器状态为 `Up` 和 `healthy`
- [ ] 前端页面正常加载 (http://localhost:8080)
- [ ] API 健康检查通过 (http://localhost:8080/api/health)
- [ ] 数据库连接正常

## 🛠️ 开发环境

### 推荐：Docker 开发
```bash
# 启动开发环境
cd docker
docker-compose up

# 查看日志
docker-compose logs -f api
docker-compose logs -f web

# 进入容器调试
docker exec -it pumi-api bash
docker exec -it pumi-web sh
```

### 本地开发（可选）

#### 后端开发
```bash
cd api
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python run.py
```

#### 前端开发
```bash
cd web
npm install
npm run dev
```

## 🔧 常用命令

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看日志
docker-compose logs -f

# 重新构建
docker-compose up --build

# 清理环境
docker-compose down -v
```

## 🚨 常见问题

### 端口被占用
```bash
# 修改 docker/.env 中的端口配置
WEB_PORT=8081
API_PORT=5001
```

### 容器启动失败
```bash
# 查看详细日志
docker-compose logs <service-name>

# 重新构建
docker-compose up --build
```

### 权限问题 (Linux)
```bash
sudo usermod -aG docker $USER
# 重新登录
```

## 📚 更多文档

- [详细部署指南](./DEPLOYMENT.md)
- [开发指南](./DEVELOPMENT.md)
- [API 文档](./api/README.md)
- [前端文档](./web/README.md)

## 🎯 下一步

1. **配置外部服务**：编辑 `docker/.env` 配置 Dify API 等
2. **自定义功能**：参考开发指南添加新功能
3. **生产部署**：修改密码和安全配置
4. **监控日志**：设置日志收集和监控

## 🤝 获取帮助

- 查看文档：项目根目录下的 Markdown 文件
- 查看日志：`docker-compose logs -f`
- 重置环境：`docker-compose down -v && docker-compose up --build`
