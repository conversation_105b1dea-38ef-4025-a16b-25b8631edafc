import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import unifiedStorage from '@/utils/unifiedStorage'

/**
 * 用户信息管理 Store
 * 负责存储和管理用户的个人信息、偏好设置等
 */
export const useUserStore = defineStore('user', () => {
  // 用户基本信息 - 使用统一存储
  const userProfile = computed({
    get: () => unifiedStorage.get('user_profile', {
      id: '',
      username: '',
      nickname: '',
      email: '',
      avatar: '',
      role: ''
    }),
    set: (profile) => unifiedStorage.set('user_profile', profile)
  })

  // 用户偏好设置 - 使用统一存储
  const userPreferences = computed({
    get: () => unifiedStorage.get('user_preferences', {
      theme: 'light',
      language: 'zh-CN'
    }),
    set: (preferences) => unifiedStorage.set('user_preferences', preferences)
  })

  // 设置用户信息
  const setUserInfo = (userInfo) => {
    if (!userInfo) return
    
    const current = userProfile.value
    unifiedStorage.set('user_profile', {
      ...current,
      ...userInfo
    })
  }

  // 更新用户偏好设置
  const updatePreferences = (preferences) => {
    if (!preferences) return
    
    const current = userPreferences.value
    unifiedStorage.set('user_preferences', {
      ...current,
      ...preferences
    })
  }

  // 清除用户信息
  const clearUserInfo = () => {
    unifiedStorage.remove('user_profile')
    unifiedStorage.remove('user_preferences')
  }

  return {
    userProfile,
    userPreferences,
    setUserInfo,
    updatePreferences,
    clearUserInfo
  }
}, {
  persist: false // 使用统一存储机制
}) 