"""
在线工具服务
处理在线工具的业务逻辑
"""

from typing import List, Optional, Dict, Any
from app.models import OnlineTools
from app import db


class ToolService:
    """在线工具服务类"""
    
    @staticmethod
    def get_all_online_tools() -> List[OnlineTools]:
        """
        获取所有在线工具
        
        Returns:
            所有在线工具列表
        """
        return OnlineTools.query.all()
    
    @staticmethod
    def get_online_tool_by_id(tools_id: int) -> Optional[OnlineTools]:
        """
        根据ID获取在线工具
        
        Args:
            tools_id: 工具ID
            
        Returns:
            在线工具对象，如果不存在返回None
        """
        return OnlineTools.query.filter_by(tools_id=tools_id).first()
    
    @staticmethod
    def create_online_tool(tools_name: str, tools_description: str, tools_url: str) -> OnlineTools:
        """
        创建在线工具
        
        Args:
            tools_name: 工具名称
            tools_description: 工具描述
            tools_url: 工具URL
            
        Returns:
            创建的在线工具对象
        """
        tool = OnlineTools(
            tools_name=tools_name,
            tools_description=tools_description,
            tools_url=tools_url
        )
        db.session.add(tool)
        db.session.commit()
        return tool
    
    @staticmethod
    def update_online_tool(tools_id: int, update_data: Dict[str, Any]) -> Optional[OnlineTools]:
        """
        更新在线工具
        
        Args:
            tools_id: 工具ID
            update_data: 更新数据字典
            
        Returns:
            更新后的工具对象，如果不存在返回None
        """
        tool = OnlineTools.query.filter_by(tools_id=tools_id).first()
        if not tool:
            return None
        
        # 更新字段
        for key, value in update_data.items():
            if hasattr(tool, key) and value is not None:
                setattr(tool, key, value)
        
        db.session.commit()
        return tool
    
    @staticmethod
    def delete_online_tool(tools_id: int) -> bool:
        """
        删除在线工具
        
        Args:
            tools_id: 工具ID
            
        Returns:
            删除成功返回True，工具不存在返回False
        """
        tool = OnlineTools.query.filter_by(tools_id=tools_id).first()
        if not tool:
            return False
        
        db.session.delete(tool)
        db.session.commit()
        return True
    
    @staticmethod
    def get_tools_by_name(tools_name: str) -> List[OnlineTools]:
        """
        根据名称模糊搜索工具
        
        Args:
            tools_name: 工具名称关键词
            
        Returns:
            匹配的工具列表
        """
        return OnlineTools.query.filter(OnlineTools.tools_name.like(f'%{tools_name}%')).all()