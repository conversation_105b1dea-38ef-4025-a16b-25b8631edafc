<template>
  <div class="tools-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">工具管理</h2>
        <p class="page-description">管理AIGC工具的基本信息、分类和状态</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加工具
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Tools /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.total_tools }}</div>
          <div class="stat-label">总工具数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon active">
          <el-icon><Check /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.active_tools }}</div>
          <div class="stat-label">启用工具</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon hot">
          <el-icon><Star /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.hot_tools }}</div>
          <div class="stat-label">热门工具</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon clicks">
          <el-icon><View /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.total_clicks }}</div>
          <div class="stat-label">总点击数</div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filter-bar">
      <div class="search-section">
        <el-input
          v-model="searchQuery"
          placeholder="搜索工具名称或描述..."
          style="width: 300px"
          clearable
          @input="handleSearch"
          @clear="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="filter-section">
        <el-select v-model="filterCategory" placeholder="选择分类" style="width: 150px" clearable @change="handleFilterChange">
          <el-option label="全部分类" value="" />
          <el-option
            v-for="category in categories"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
        <el-select v-model="filterStatus" placeholder="选择状态" style="width: 120px" clearable @change="handleFilterChange">
          <el-option label="全部状态" value="" />
          <el-option label="启用" value="active" />
          <el-option label="禁用" value="inactive" />
        </el-select>
        <el-button @click="loadTools">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 工具列表表格 -->
    <div class="table-container">
      <el-table
        :data="filteredTools"
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="icon" label="图标" width="80">
          <template #default="{ row }">
            <div class="tool-icon-cell">
              <!-- 根据图标类型显示不同内容 -->
              <img
                v-if="row.icon_type === 'image' && row.icon_image_url"
                :src="getIconUrl(row.id)"
                :alt="row.name"
                class="tool-icon-image"
                @error="handleImageError(row)" />
              <span v-else class="tool-emoji">{{ row.icon || '🔧' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="工具名称" min-width="150">
          <template #default="{ row }">
            <div class="tool-name-cell">
              <span class="tool-name">{{ row.name }}</span>
              <el-tag v-if="row.isHot" type="danger" size="small">热门</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="category" label="分类" width="120">
          <template #default="{ row }">
            <el-tag size="small">{{ getCategoryName(row.category) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="clickCount" label="点击数" width="100" sortable />
        <el-table-column prop="isActive" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.isActive"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button size="small" type="primary" @click="editTool(row)">
                编辑
              </el-button>
              <el-button
                size="small"
                :type="row.isHot ? 'warning' : 'default'"
                @click="toggleHot(row)"
              >
                {{ row.isHot ? '取消热门' : '设为热门' }}
              </el-button>
              <el-button size="small" type="danger" @click="deleteToolItem(row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedTools.length > 0">
        <span>已选择 {{ selectedTools.length }} 项</span>
        <el-button size="small" @click="batchEnable">批量启用</el-button>
        <el-button size="small" @click="batchDisable">批量禁用</el-button>
        <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalTools"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 添加/编辑工具对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingTool ? '编辑工具' : '添加工具'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="toolFormRef"
        :model="toolForm"
        :rules="toolFormRules"
        label-width="100px"
      >
        <el-form-item label="工具名称" prop="name">
          <el-input v-model="toolForm.name" placeholder="请输入工具名称" />
        </el-form-item>
        <el-form-item label="工具描述" prop="description">
          <el-input
            v-model="toolForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入工具描述"
          />
        </el-form-item>
        <el-form-item label="工具链接" prop="url">
          <el-input v-model="toolForm.url" placeholder="请输入工具链接" />
        </el-form-item>
        <el-form-item label="图标类型" prop="iconType">
          <el-radio-group v-model="toolForm.iconType" @change="handleIconTypeChange">
            <el-radio value="emoji">Emoji图标</el-radio>
            <el-radio value="image">图片图标</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- Emoji图标输入 -->
        <el-form-item v-if="toolForm.iconType === 'emoji'" label="Emoji图标" prop="icon">
          <el-input
            v-model="toolForm.icon"
            placeholder="请输入emoji图标"
            style="width: 200px"
          />
          <span class="icon-preview">{{ toolForm.icon }}</span>
        </el-form-item>

        <!-- 图片图标上传 -->
        <el-form-item v-if="toolForm.iconType === 'image'" label="图片图标" prop="iconImage">
          <div class="icon-upload-section">
            <el-upload
              ref="iconUploadRef"
              class="icon-uploader"
              :show-file-list="false"
              :before-upload="beforeIconUpload"
              :on-success="handleIconUploadSuccess"
              :on-error="handleIconUploadError"
              :on-progress="handleIconUploadProgress"
              action="#"
              :http-request="customIconUpload"
              accept="image/png,image/jpeg,image/jpg,image/gif,image/webp"
            >
              <div class="upload-area">
                <div v-if="toolForm.iconImageUrl" class="icon-preview-container">
                  <img :src="getIconUrl(editingTool?.id)" :alt="toolForm.name" class="uploaded-icon" />
                  <div class="icon-actions">
                    <el-button size="small" type="danger" @click.stop="removeIcon">删除</el-button>
                  </div>
                </div>
                <div v-else class="upload-placeholder">
                  <el-icon class="upload-icon"><Plus /></el-icon>
                  <div class="upload-text">点击上传图标</div>
                  <div class="upload-hint">支持 PNG、JPG、GIF、WebP 格式，大小不超过 2MB</div>
                </div>
              </div>
            </el-upload>
            <el-progress
              v-if="iconUploadProgress > 0 && iconUploadProgress < 100"
              :percentage="iconUploadProgress"
              style="margin-top: 10px"
            />
          </div>
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="toolForm.category" placeholder="请选择分类">
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签" prop="tags">
          <el-input v-model="toolForm.tagsInput" placeholder="请输入标签，用逗号分隔" />
        </el-form-item>
        <el-form-item label="设置">
          <el-checkbox v-model="toolForm.isHot">设为热门</el-checkbox>
          <el-checkbox v-model="toolForm.isActive">启用状态</el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveTool" :loading="saving">
          {{ editingTool ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Tools,
  Check,
  Star,
  View,
  Search,
  Refresh
} from '@element-plus/icons-vue'
import {
  getCategories,
  getTools,
  createTool,
  updateTool,
  deleteTool,
  getStats,
  uploadIcon,
  deleteIcon,
  getIconUrl,
  validateIconFile
} from '@/services/aigcService'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const searchQuery = ref('')
const filterCategory = ref('')
const filterStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const selectedTools = ref([])
const showAddDialog = ref(false)
const editingTool = ref(null)

// 统计数据
const stats = ref({
  total_tools: 0,
  active_tools: 0,
  hot_tools: 0,
  total_clicks: 0
})

// 分类数据
const categories = ref([
  { id: '', name: '全部分类' }
])
const totalTools = ref(0)

// 工具数据
const tools = ref([])

// 表单数据
const toolForm = ref({
  name: '',
  description: '',
  url: '',
  icon: '',
  iconType: 'emoji',
  iconImageUrl: '',
  category: '',
  tagsInput: '',
  isHot: false,
  isActive: true
})

// 图标上传相关
const iconUploadRef = ref(null)
const iconUploadProgress = ref(0)

// 表单验证规则
const toolFormRules = {
  name: [
    { required: true, message: '请输入工具名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入工具描述', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入工具链接', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ]
}

// 数据加载函数
const loadCategories = async () => {
  try {
    const data = await getCategories(true) // 只获取启用的分类
    categories.value = [
      { id: '', name: '全部分类' },
      ...data.map(cat => ({ id: cat.id, name: cat.name }))
    ]
  } catch (error) {
    console.error('加载分类失败:', error)
    ElMessage.error('加载分类失败')
  }
}

const loadTools = async () => {
  try {
    loading.value = true
    const options = {
      isActive: filterStatus.value === 'active' ? true : filterStatus.value === 'inactive' ? false : null,
      categoryId: filterCategory.value || null,
      search: searchQuery.value || null,
      page: currentPage.value,
      perPage: pageSize.value,
      sortBy: 'sort_order'
    }

    const data = await getTools(options)
    tools.value = data.tools.map(tool => ({
      id: tool.id,
      name: tool.name,
      description: tool.description,
      url: tool.url,
      icon: tool.icon,
      icon_type: tool.icon_type,
      icon_image_url: tool.icon_image_url,
      category: tool.category_id,
      categoryName: tool.category,
      tags: tool.tags || [],
      isHot: tool.is_hot,
      isActive: tool.is_active,
      clickCount: tool.click_count,
      createdAt: tool.created_at
    }))
    totalTools.value = data.pagination.total
  } catch (error) {
    console.error('加载工具失败:', error)
    ElMessage.error('加载工具失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const data = await getStats()
    stats.value = data
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 计算属性（现在筛选在后端进行）
const filteredTools = computed(() => {
  return tools.value
})

// 搜索和筛选处理
const handleSearch = () => {
  currentPage.value = 1
  loadTools()
}

const handleFilterChange = () => {
  currentPage.value = 1
  loadTools()
}

const handlePageChange = (page) => {
  currentPage.value = page
  loadTools()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadTools()
}

// 方法
const getCategoryName = (categoryId) => {
  const category = categories.value.find(c => c.id === categoryId)
  return category ? category.name : '未知分类'
}

const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

const handleSelectionChange = (selection) => {
  selectedTools.value = selection
}

const handleStatusChange = async (tool) => {
  try {
    await updateTool(tool.id, { is_active: tool.isActive })
    ElMessage.success(`${tool.name} 已${tool.isActive ? '启用' : '禁用'}`)
    await loadStats()
  } catch (error) {
    // 如果API调用失败，回滚状态
    tool.isActive = !tool.isActive
    console.error('切换状态失败:', error)
    ElMessage.error('操作失败')
  }
}



const editTool = (tool) => {
  editingTool.value = tool
  toolForm.value = {
    name: tool.name,
    description: tool.description,
    url: tool.url,
    icon: tool.icon,
    iconType: tool.icon_type || 'emoji',
    iconImageUrl: tool.icon_image_url || '',
    category: tool.category,
    tagsInput: tool.tags.join(', '),
    isHot: tool.isHot,
    isActive: tool.isActive
  }
  showAddDialog.value = true
}

const toggleHot = async (tool) => {
  try {
    const newHotStatus = !tool.isHot
    await updateTool(tool.id, { is_hot: newHotStatus })
    tool.isHot = newHotStatus
    ElMessage.success(`${tool.name} 已${newHotStatus ? '设为热门' : '取消热门'}`)
    await loadStats()
  } catch (error) {
    console.error('切换热门状态失败:', error)
    ElMessage.error('操作失败')
  }
}

const deleteToolItem = async (tool) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除工具 "${tool.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteTool(tool.id)
    ElMessage.success('删除成功')
    await loadTools()
    await loadStats()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除工具失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const batchEnable = () => {
  selectedTools.value.forEach(tool => {
    tool.isActive = true
  })
  ElMessage.success(`已启用 ${selectedTools.value.length} 个工具`)
  selectedTools.value = []
}

const batchDisable = () => {
  selectedTools.value.forEach(tool => {
    tool.isActive = false
  })
  ElMessage.success(`已禁用 ${selectedTools.value.length} 个工具`)
  selectedTools.value = []
}

const batchDelete = () => {
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedTools.value.length} 个工具吗？`,
    '确认批量删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const idsToDelete = selectedTools.value.map(tool => tool.id)
    tools.value = tools.value.filter(tool => !idsToDelete.includes(tool.id))
    selectedTools.value = []
    ElMessage.success('批量删除成功')
  })
}

const resetForm = () => {
  toolForm.value = {
    name: '',
    description: '',
    url: '',
    icon: '',
    iconType: 'emoji',
    iconImageUrl: '',
    category: '',
    tagsInput: '',
    isHot: false,
    isActive: true
  }
  editingTool.value = null
  iconUploadProgress.value = 0
}

const saveTool = async () => {
  try {
    saving.value = true

    const formData = {
      name: toolForm.value.name,
      description: toolForm.value.description,
      url: toolForm.value.url,
      icon: toolForm.value.icon,
      icon_type: toolForm.value.iconType,
      icon_image_url: toolForm.value.iconImageUrl,
      category_id: toolForm.value.category,
      tags: toolForm.value.tagsInput ? toolForm.value.tagsInput.split(',').map(tag => tag.trim()) : [],
      is_hot: toolForm.value.isHot,
      is_active: toolForm.value.isActive
    }

    if (editingTool.value) {
      // 更新工具
      await updateTool(editingTool.value.id, formData)
      ElMessage.success('更新成功')
    } else {
      // 创建工具
      await createTool(formData)
      ElMessage.success('添加成功')
    }

    showAddDialog.value = false
    resetForm()
    await loadTools()
    await loadStats()

  } catch (error) {
    console.error('保存工具失败:', error)
    ElMessage.error(editingTool.value ? '更新失败' : '添加失败')
  } finally {
    saving.value = false
  }
}

// 图标相关方法
const handleIconTypeChange = (type) => {
  if (type === 'emoji') {
    toolForm.value.iconImageUrl = ''
  } else {
    toolForm.value.icon = ''
  }
}

const handleImageError = (tool) => {
  console.warn(`工具 ${tool.name} 的图标加载失败`)
  // 可以在这里添加错误处理逻辑
}

const beforeIconUpload = (file) => {
  const validation = validateIconFile(file)
  if (!validation.valid) {
    ElMessage.error(validation.errors.join(', '))
    return false
  }
  return true
}

const customIconUpload = async (options) => {
  if (!editingTool.value) {
    ElMessage.error('请先保存工具后再上传图标')
    return
  }

  try {
    const result = await uploadIcon(editingTool.value.id, options.file, (progress) => {
      iconUploadProgress.value = progress
    })

    if (result.code === 200) {
      toolForm.value.iconImageUrl = result.data.icon_url
      toolForm.value.iconType = 'image'
      ElMessage.success('图标上传成功')
      await loadTools() // 刷新工具列表
    }
  } catch (error) {
    console.error('上传图标失败:', error)
    ElMessage.error('上传失败')
  } finally {
    iconUploadProgress.value = 0
  }
}

const handleIconUploadSuccess = () => {
  // 成功处理已在customIconUpload中完成
}

const handleIconUploadError = (error) => {
  console.error('图标上传失败:', error)
  ElMessage.error('上传失败')
  iconUploadProgress.value = 0
}

const handleIconUploadProgress = () => {
  // 进度处理已在customIconUpload中完成
}

const removeIcon = async () => {
  if (!editingTool.value) return

  try {
    await deleteIcon(editingTool.value.id)
    toolForm.value.iconImageUrl = ''
    toolForm.value.iconType = 'emoji'
    ElMessage.success('图标删除成功')
    await loadTools() // 刷新工具列表
  } catch (error) {
    console.error('删除图标失败:', error)
    ElMessage.error('删除失败')
  }
}

// 生命周期
onMounted(async () => {
  await loadCategories()
  await loadTools()
  await loadStats()
})
</script>

<style scoped>
.tools-management {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  color: #6b7280;
  font-size: 20px;
}

.stat-icon.active {
  background: #dcfce7;
  color: #16a34a;
}

.stat-icon.hot {
  background: #fef3c7;
  color: #d97706;
}

.stat-icon.clicks {
  background: #dbeafe;
  color: #2563eb;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-top: 4px;
}

/* 搜索筛选栏 */
.search-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.filter-section {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 表格容器 */
.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
  overflow: hidden;
}

.tool-icon-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tool-emoji {
  font-size: 20px;
}

.tool-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-name {
  font-weight: 500;
}

/* 批量操作 */
.batch-actions {
  padding: 16px 24px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  min-width: 60px;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}

/* 分页 */
.pagination-container {
  padding: 16px 24px;
  display: flex;
  justify-content: center;
}

/* 表单样式 */
.icon-preview {
  margin-left: 12px;
  font-size: 20px;
  padding: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  min-width: 40px;
  text-align: center;
}

/* 图标相关样式 */
.tool-icon-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tool-icon-image {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 4px;
}

.tool-emoji {
  font-size: 20px;
}

.icon-upload-section {
  width: 100%;
}

.icon-uploader {
  width: 100%;
}

.upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #3b82f6;
}

.icon-preview-container {
  position: relative;
  display: inline-block;
}

.uploaded-icon {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
}

.icon-actions {
  margin-top: 10px;
}

.upload-placeholder {
  color: #6b7280;
}

.upload-icon {
  font-size: 32px;
  color: #9ca3af;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.upload-hint {
  font-size: 12px;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tools-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .search-filter-bar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filter-section {
    justify-content: space-between;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }
}
</style>
