<template>
  <div class="token-debugger" v-if="showDebugger">
    <el-card class="debugger-card">
      <template #header>
        <div class="card-header">
          <span>令牌调试器</span>
          <el-button type="text" @click="closeDebugger">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </template>
      
      <div class="debugger-content">
        <div class="token-status">
          <h4>令牌状态</h4>
          <div class="status-item">
            <span class="label">访问令牌:</span>
            <span :class="['value', tokenStatus.hasToken ? 'success' : 'error']">
              {{ tokenStatus.hasToken ? '存在' : '不存在' }}
            </span>
          </div>
          <div class="status-item">
            <span class="label">是否过期:</span>
            <span :class="['value', tokenStatus.isExpired ? 'error' : 'success']">
              {{ tokenStatus.isExpired ? '已过期' : '有效' }}
            </span>
          </div>
          <div class="status-item">
            <span class="label">剩余时间:</span>
            <span class="value">{{ tokenStatus.expiresIn }} 秒</span>
          </div>
          <div class="status-item">
            <span class="label">即将过期:</span>
            <span :class="['value', tokenStatus.isNearExpiry ? 'warning' : 'success']">
              {{ tokenStatus.isNearExpiry ? '是' : '否' }}
            </span>
          </div>
        </div>
        
        <div class="debugger-actions">
          <el-button size="small" type="primary" @click="refreshTokenManually">
            手动刷新令牌
          </el-button>
          <el-button size="small" type="danger" @click="clearTokens">
            清除令牌
          </el-button>
        </div>
        
        <div class="debug-log">
          <h4>调试日志</h4>
          <div class="log-container">
            <div v-for="(log, index) in debugLogs" :key="index" class="log-item">
              <span class="timestamp">{{ log.timestamp }}</span>
              <span :class="['message', log.type]">{{ log.message }}</span>
            </div>
          </div>
          <el-button size="small" @click="clearLogs">清除日志</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as tokenService from '@/services/tokenService'
import tokenRefreshService from '@/services/tokenRefreshService'
import { Close } from '@element-plus/icons-vue'

const showDebugger = ref(false)
const debugLogs = ref([])

const tokenStatus = computed(() => {
  return tokenRefreshService.getTokenStatus()
})

// 添加调试日志
const addDebugLog = (message, type = 'info') => {
  const timestamp = new Date().toLocaleTimeString()
  debugLogs.value.unshift({
    timestamp,
    message,
    type
  })
  
  // 限制日志数量
  if (debugLogs.value.length > 50) {
    debugLogs.value = debugLogs.value.slice(0, 50)
  }
}

// 手动刷新令牌
const refreshTokenManually = async () => {
  try {
    addDebugLog('开始手动刷新令牌...')
    await tokenRefreshService.refreshToken()
    addDebugLog('手动刷新令牌成功', 'success')
    ElMessage.success('令牌刷新成功')
  } catch (error) {
    addDebugLog(`手动刷新令牌失败: ${error.message}`, 'error')
    ElMessage.error('令牌刷新失败')
  }
}

// 清除令牌
const clearTokens = () => {
  try {
    tokenService.clearTokens()
    addDebugLog('令牌已清除', 'warning')
    ElMessage.warning('令牌已清除')
  } catch (error) {
    addDebugLog(`清除令牌失败: ${error.message}`, 'error')
    ElMessage.error('清除令牌失败')
  }
}

// 清除日志
const clearLogs = () => {
  debugLogs.value = []
}

// 显示调试器
const openDebugger = () => {
  showDebugger.value = true
  addDebugLog('令牌调试器已打开')
}

// 关闭调试器
const closeDebugger = () => {
  showDebugger.value = false
}

// 监听令牌状态变化
let checkInterval
onMounted(() => {
  // 定期更新状态
  checkInterval = setInterval(() => {
    // 状态会自动通过 computed 更新
  }, 5000)
  
  // 添加初始日志
  addDebugLog('令牌调试器已初始化')
})

onUnmounted(() => {
  if (checkInterval) {
    clearInterval(checkInterval)
  }
})

// 暴露方法给全局使用
window.openTokenDebugger = openDebugger

// 添加键盘快捷键 Ctrl+Shift+T
document.addEventListener('keydown', (e) => {
  if (e.ctrlKey && e.shiftKey && e.key === 'T') {
    e.preventDefault()
    openDebugger()
  }
})
</script>

<style scoped>
.token-debugger {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  width: 400px;
  max-height: 600px;
  overflow-y: auto;
}

.debugger-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.debugger-content {
  font-size: 14px;
}

.token-status {
  margin-bottom: 20px;
}

.token-status h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.label {
  font-weight: 500;
  color: #666;
}

.value {
  font-weight: bold;
}

.value.success {
  color: #67c23a;
}

.value.error {
  color: #f56c6c;
}

.value.warning {
  color: #e6a23c;
}

.debugger-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.debug-log h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 5px;
}

.timestamp {
  color: #999;
  margin-right: 10px;
  white-space: nowrap;
}

.message {
  color: #333;
}

.message.success {
  color: #67c23a;
}

.message.error {
  color: #f56c6c;
}

.message.warning {
  color: #e6a23c;
}
</style>