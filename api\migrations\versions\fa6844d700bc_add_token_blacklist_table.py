"""Add token blacklist table

Revision ID: fa6844d700bc
Revises: 0c52da0764be
Create Date: 2025-07-26 21:28:34.692332

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fa6844d700bc'
down_revision = '0c52da0764be'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('token_blacklist',
    sa.Column('jti', sa.String(length=36), nullable=False, comment='JWT唯一标识符(JTI)'),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('revoked_at', sa.DateTime(), nullable=False, comment='令牌撤销时间'),
    sa.Column('expires_at', sa.DateTime(), nullable=False, comment='令牌原始过期时间'),
    sa.Column('token_type', sa.String(length=20), nullable=False, comment='令牌类型'),
    sa.ForeignKeyConstraint(['user_id'], ['User.user_id'], ),
    sa.PrimaryKeyConstraint('jti')
    )
    with op.batch_alter_table('token_blacklist', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_token_blacklist_expires_at'), ['expires_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_token_blacklist_user_id'), ['user_id'], unique=False)

    with op.batch_alter_table('aigc_categories', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='智能体分类'
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('aigc_categories', schema=None) as batch_op:
        batch_op.create_table_comment(
        '智能体分类',
        existing_comment=None
    )

    with op.batch_alter_table('token_blacklist', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_token_blacklist_user_id'))
        batch_op.drop_index(batch_op.f('ix_token_blacklist_expires_at'))

    op.drop_table('token_blacklist')
    # ### end Alembic commands ###
