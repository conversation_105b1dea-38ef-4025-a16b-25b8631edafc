"""
智能体API路由和入口点
包含智能体列表获取、详情查看、对话功能等API
"""

from flask import request, jsonify, Response
from flask_restx import Namespace, Resource
from werkzeug.datastructures import FileStorage
import requests
import json
import uuid
import datetime
from typing import Dict, Any, List

from app.core.auth_decorators import jwt_required, get_jwt_identity

from app.services.agent_service import AgentService
from app.utils.dependencies import get_current_active_user
from app.utils.errors import ResourceNotFound
from app.core.config import get_config

# 导入模型定义
from app.schemas import (
    agent_chat_request_model,
    agent_list_response_model,
    agent_detail_response_model,
    agent_tags_response_model,
    agent_chat_response_model,
    agent_conversation_list_params_model,
    agent_conversation_list_response_model,
    agent_list_data_model,
    agent_item_model,
    agent_conversation_item_model,
    chat_file_item_model,
    file_upload_response_model,
    file_upload_data_model,
    agent_item_in_list_model,
    agent_chat_data_model,
    agent_parameters_response_model,
    agent_parameters_data_model
)

# 导入参数信息相关的子模型
from app.schemas.agent.response import (
    text_input_control_model,
    paragraph_control_model,
    select_control_model,
    image_upload_config_model,
    file_upload_config_model,
    system_parameters_model,
    text_to_speech_model,
    more_like_this_model,
    sensitive_word_avoidance_model,
    file_upload_detail_config_model
)

config = get_config()

# 创建命名空间
agent_ns = Namespace('agent', description='智能体API')

# 将模型添加到当前命名空间
agent_ns.models[agent_chat_request_model.name] = agent_chat_request_model
agent_ns.models[agent_list_response_model.name] = agent_list_response_model
agent_ns.models[agent_detail_response_model.name] = agent_detail_response_model
agent_ns.models[agent_tags_response_model.name] = agent_tags_response_model
agent_ns.models[agent_chat_response_model.name] = agent_chat_response_model
agent_ns.models[agent_conversation_list_params_model.name] = agent_conversation_list_params_model
agent_ns.models[agent_conversation_list_response_model.name] = agent_conversation_list_response_model
agent_ns.models[agent_list_data_model.name] = agent_list_data_model
agent_ns.models[agent_item_model.name] = agent_item_model
agent_ns.models[agent_conversation_item_model.name] = agent_conversation_item_model
agent_ns.models[chat_file_item_model.name] = chat_file_item_model
agent_ns.models[file_upload_response_model.name] = file_upload_response_model
agent_ns.models[file_upload_data_model.name] = file_upload_data_model
agent_ns.models[agent_item_in_list_model.name] = agent_item_in_list_model
agent_ns.models[agent_chat_data_model.name] = agent_chat_data_model

# 注册参数信息模型及其子模型
agent_ns.models[agent_parameters_response_model.name] = agent_parameters_response_model
agent_ns.models[agent_parameters_data_model.name] = agent_parameters_data_model
agent_ns.models[text_input_control_model.name] = text_input_control_model
agent_ns.models[paragraph_control_model.name] = paragraph_control_model
agent_ns.models[select_control_model.name] = select_control_model
agent_ns.models[image_upload_config_model.name] = image_upload_config_model
agent_ns.models[file_upload_config_model.name] = file_upload_config_model
agent_ns.models[system_parameters_model.name] = system_parameters_model
agent_ns.models[text_to_speech_model.name] = text_to_speech_model
agent_ns.models[more_like_this_model.name] = more_like_this_model
agent_ns.models[sensitive_word_avoidance_model.name] = sensitive_word_avoidance_model
agent_ns.models[file_upload_detail_config_model.name] = file_upload_detail_config_model

@agent_ns.route('/list')
class AgentList(Resource):
    """智能体列表API"""
    
    @jwt_required()
    @agent_ns.doc(
        description='获取智能体列表',
        params={
            'page': {'description': '页码', 'type': 'integer', 'default': 1},
            'limit': {'description': '每页记录数', 'type': 'integer', 'default': 20},
            'mode': {'description': '智能体模式', 'type': 'string', 'enum': ['chat', 'advanced-chat', 'agent-chat', 'workflow']}
        }
    )
    @agent_ns.marshal_with(agent_list_response_model)
    def get(self):
        """获取智能体列表"""
        try:
            # 获取查询参数
            page = request.args.get('page', default=1, type=int)
            limit = request.args.get('limit', default=20, type=int)
            mode = request.args.get('mode', default=None, type=str)
            
            # 调用服务获取智能体列表
            agent_service = AgentService()
            result = agent_service.get_agent_list(page, limit, mode)
            
            return result
            
        except Exception as e:
            return {
                'code': 500,
                'message': f"获取智能体列表失败: {str(e)}",
                'data': None
            }, 500

@agent_ns.route('/<string:agent_id>')
class AgentDetail(Resource):
    """智能体详情API"""
    
    @jwt_required()
    @agent_ns.doc(description='获取智能体详情')
    @agent_ns.marshal_with(agent_detail_response_model)
    def get(self, agent_id):
        """获取智能体详情"""
        try:
            # 调用服务获取智能体详情
            agent_service = AgentService()
            result = agent_service.get_agent_detail(agent_id)
            
            if result.get('code') != 200:
                return result, result.get('code', 500)
            
            return result
            
        except Exception as e:
            return {
                'code': 500,
                'message': f"获取智能体详情失败: {str(e)}",
                'data': None
            }, 500

@agent_ns.route('/tags')
class AgentTags(Resource):
    """智能体标签分类API"""
    
    @jwt_required()
    @agent_ns.doc(description='获取智能体标签分类')
    @agent_ns.marshal_with(agent_tags_response_model)
    def get(self):
        """获取智能体标签分类"""
        try:
            # 调用服务获取智能体标签
            agent_service = AgentService()
            result = agent_service.get_agent_tags()
            
            return result
            
        except Exception as e:
            return {
                'code': 500,
                'message': f"获取智能体标签失败: {str(e)}",
                'data': None
            }, 500

@agent_ns.route('/<string:agent_id>/conversations')
class AgentConversationList(Resource):
    """智能体对话历史列表API"""
    
    @jwt_required()
    @agent_ns.doc(
        description='获取智能体对话历史列表',
        params={
            'last_id': {'description': '（选填）当前页最后面一条记录的ID', 'type': 'string'},
            'limit': {'description': '（选填）一次请求返回多少条记录，默认20条，最大100条', 'type': 'integer', 'default': 20, 'min': 1, 'max': 100},
            'sort_by': {'description': '（选填）排序字段，默认-updated_at', 'type': 'string', 'enum': ['created_at', '-created_at', 'updated_at', '-updated_at'], 'default': '-updated_at'}
        }
    )
    @agent_ns.marshal_with(agent_conversation_list_response_model)
    def get(self, agent_id):
        """获取智能体对话历史列表"""
        try:
            # 获取查询参数
            last_id = request.args.get('last_id', default=None, type=str)
            limit = request.args.get('limit', default=20, type=int)
            sort_by = request.args.get('sort_by', default='-updated_at', type=str)
            
            # 验证参数
            if limit < 1 or limit > 100:
                limit = 20  # 规范化参数
                
            # 获取当前用户
            current_user = get_current_active_user()
            
            # 获取用户标识
            if hasattr(current_user, 'external_user_id'):
                # 优先使用external_user_id
                user_id = str(current_user.external_user_id)
            else:
                # 回退到user_id
                user_id = str(current_user.user_id)
                
            # 调用服务获取对话历史列表
            agent_service = AgentService()
            result = agent_service.get_agent_conversation_list(
                agent_id=agent_id,
                user_id=user_id,
                last_id=last_id,
                limit=limit,
                sort_by=sort_by
            )
            
            # 直接返回结果
            return result
            
        except Exception as e:
            return {
                'code': 500,
                'message': f"获取智能体对话历史列表失败: {str(e)}",
                'data': []
            }, 500

@agent_ns.route('/chat')
class AgentChat(Resource):
    """智能体对话API"""
    
    @jwt_required()
    @agent_ns.doc(description='创建智能体对话消息并获取响应')
    @agent_ns.expect(agent_chat_request_model)
    @agent_ns.response(200, 'Success', agent_chat_response_model)
    @agent_ns.response(400, 'Bad Request')
    @agent_ns.response(401, 'Unauthorized')
    @agent_ns.response(500, 'Internal Server Error')
    def post(self):
        """创建智能体对话消息并获取响应"""
        try:
            # 获取请求数据
            data = request.get_json()
            
            # 提取参数
            agent_id = data.get('agent_id')
            query = data.get('query')
            response_mode = data.get('response_mode', 'streaming')
            conversation_id = data.get('conversation_id', '')
            inputs = data.get('inputs', {})
            files = data.get('files', [])
            
            # 参数验证
            if not agent_id:
                return {
                    'code': 400,
                    'message': '缺少必要参数: agent_id',
                    'data': None
                }, 400
                
            if not query:
                return {
                    'code': 400,
                    'message': '缺少必要参数: query',
                    'data': None
                }, 400
            
            # 获取当前用户
            current_user = get_current_active_user()
            
            # 获取用户标识
            if hasattr(current_user, 'external_user_id'):
                # 优先使用external_user_id
                user_id = str(current_user.external_user_id)
            else:
                # 回退到user_id
                user_id = str(current_user.user_id)
            
            # 调用智能体服务进行对话
            agent_service = AgentService()
            
            # 流式响应处理
            if response_mode == "streaming":
                stream_response = agent_service.chat_with_agent(
                    agent_id=agent_id,
                    query=query,
                    conversation_id=conversation_id,
                    user=user_id,
                    inputs=inputs,
                    files=files,
                    response_mode=response_mode,
                    stream=True
                )
                
                # 检查是否为字典类型，表示出错
                if isinstance(stream_response, dict):
                    return stream_response, stream_response.get('code', 500)
                
                # 处理流式响应
                def generate():
                    try:
                        # 初始化变量用于跟踪状态
                        accumulated_content = ""
                        conversation_id = None
                        message_id = None
                        
                        # 转发服务端事件流
                        for chunk in stream_response.iter_lines():
                            if not chunk:
                                continue
                                
                            # 解码数据
                            raw_data = chunk.decode('utf-8')
                            
                            # 提取JSON数据
                            json_data = None
                            if raw_data.startswith('data: '):
                                if raw_data.strip() == 'data: [DONE]':
                                    # 发送结束信号
                                    yield "data: [DONE]\n\n"
                                    continue
                                
                                try:
                                    json_data = json.loads(raw_data[6:])  # 去掉"data: "前缀
                                except json.JSONDecodeError:
                                    continue
                            else:
                                continue
                            
                            # 根据事件类型处理数据
                            event_type = json_data.get('event')
                            
                            # 处理消息事件
                            if event_type == 'message':
                                # 获取消息内容
                                answer_text = json_data.get('answer', '')
                                
                                # 如果是第一个消息块，获取会话ID和消息ID
                                if not conversation_id and 'conversation_id' in json_data:
                                    conversation_id = json_data['conversation_id']
                                    
                                if not message_id and 'message_id' in json_data:
                                    message_id = json_data['message_id']
                                
                                # 累积内容
                                accumulated_content += answer_text
                                
                                # 检查并处理图片路径
                                # Dify生成的图片通常是这种格式:
                                # ![alt](/files/tools/file-id.png?timestamp=xxx&nonce=xxx&sign=xxx)
                                # 需要将它转换为我们的代理路径:
                                # ![alt](/agent/files/tools/file-id.png?timestamp=xxx&nonce=xxx&sign=xxx)
                                if "![" in answer_text and "](/files/" in answer_text:
                                    # 使用正则表达式处理图片URL
                                    import re
                                    # 匹配Markdown图片格式: ![alt](/files/tools/file-id.png?timestamp=xxx&nonce=xxx&sign=xxx)
                                    pattern = r'!\[(.*?)\]\(/files/(tools/[^?)]+)(\?[^)]*)\)'
                                    
                                    # 替换为我们的代理URL
                                    answer_text = re.sub(pattern, r'![\1](/api/agent/files/\2\3)', answer_text)
                                
                                # 转换为chat service格式
                                response_chunk = {
                                    'content': answer_text,
                                    'conversation_id': conversation_id
                                }
                                
                                # 发送转换后的数据
                                yield f"data: {json.dumps(response_chunk)}\n\n"
                                
                            # 处理消息结束事件
                            elif event_type == 'message_end':
                                # 获取元数据
                                metadata = json_data.get('metadata', {})
                                
                                # 发送完整信息，包含元数据
                                final_chunk = {
                                    'content': '',
                                    'conversation_id': json_data.get('conversation_id', conversation_id),
                                    'message_id': json_data.get('id', message_id),
                                    'metadata': metadata,
                                    'status': 'finished'
                                }
                                
                                # 发送最终块
                                yield f"data: {json.dumps(final_chunk)}\n\n"
                                
                            # 处理工作流开始事件
                            elif event_type == 'workflow_started':
                                # 获取会话ID
                                if not conversation_id and 'conversation_id' in json_data:
                                    conversation_id = json_data['conversation_id']
                                    
                                # 获取消息ID
                                if not message_id and 'message_id' in json_data:
                                    message_id = json_data['message_id']
                                    
                                # 发送开始事件
                                start_chunk = {
                                    'content': '',
                                    'conversation_id': conversation_id,
                                    'message_id': message_id,
                                    'status': 'started'
                                }
                                
                                yield f"data: {json.dumps(start_chunk)}\n\n"
                                
                            # 错误处理
                            elif event_type and 'error' in json_data:
                                error_chunk = {
                                    'error': json_data.get('error'),
                                    'status': 'error',
                                    'conversation_id': json_data.get('conversation_id', conversation_id)
                                }
                                yield f"data: {json.dumps(error_chunk)}\n\n"
                                yield "data: [DONE]\n\n"
                                
                    except Exception as e:
                        # 发送错误消息
                        error_data = json.dumps({
                            'error': str(e),
                            'status': 'error'
                        })
                        yield f"data: {error_data}\n\n"
                        yield "data: [DONE]\n\n"
                
                # 返回SSE响应
                return Response(generate(), mimetype='text/event-stream')
            
            # 非流式响应处理
            else:
                response = agent_service.chat_with_agent(
                    agent_id=agent_id,
                    query=query,
                    conversation_id=conversation_id,
                    user=user_id,
                    inputs=inputs,
                    files=files,
                    response_mode="blocking",
                    stream=False
                )
                
                # 如果返回的是标准响应格式
                if isinstance(response, dict) and 'code' in response:
                    return response, response.get('code', 200)
                
                # 处理Dify API的非流式响应
                try:
                    # 提取相关信息
                    answer = response.get('answer', '')
                    conversation_id = response.get('conversation_id', '')
                    
                    # 转换为统一格式
                    return {
                        'code': 200,
                        'message': 'success',
                        'data': {
                            'content': answer,
                            'conversation_id': conversation_id
                        }
                    }
                except Exception as e:
                    return {
                        'code': 500,
                        'message': f"处理智能体响应失败: {str(e)}",
                        'data': None
                    }, 500
                
        except Exception as e:
            return {
                'code': 500,
                'message': f"智能体对话失败: {str(e)}",
                'data': None
            }, 500

@agent_ns.route('/icons/<string:icon_id>')
class AgentIcon(Resource):
    """智能体图标代理API"""
    
    @agent_ns.doc(description='代理获取智能体图标')
    def get(self, icon_id):
        """代理获取智能体图标"""
        try:
            # 从查询参数中获取签名信息
            timestamp = request.args.get('timestamp')
            nonce = request.args.get('nonce')
            sign = request.args.get('sign')
            
            # 构建图标URL
            icon_url = f"{config.DIFY_API_URL}/files/{icon_id}/file-preview"
            
            # 如果有签名参数，添加到URL
            if all([timestamp, nonce, sign]):
                icon_url += f"?timestamp={timestamp}&nonce={nonce}&sign={sign}"
            
            # 获取Dify API请求头
            headers = {
                "Authorization": f"Bearer {config.DIFY_API_KEY}",
                "X-WORKSPACE-ID": config.DIFY_WORKSPACE_ID
            }
            
            # 转发请求到Dify
            response = requests.get(
                icon_url,
                headers=headers,
                stream=True,  # 流式传输
                timeout=5
            )
            
            # 检查响应状态
            if response.status_code != 200:
                return {
                    'code': response.status_code,
                    'message': f"获取图标失败: {response.reason}",
                    'data': None
                }, response.status_code
            
            # 创建Flask响应对象并转发内容类型
            return Response(
                response.iter_content(chunk_size=1024),
                content_type=response.headers.get('Content-Type', 'image/png'),
                status=response.status_code
            )
            
        except Exception as e:
            return {
                'code': 500,
                'message': f"获取智能体图标失败: {str(e)}",
                'data': None
            }, 500

@agent_ns.route('/files/<string:file_id>')
class AgentFile(Resource):
    """智能体文件代理API"""
    
    @agent_ns.doc(description='代理获取智能体生成的文件')
    def get(self, file_id):
        """代理获取智能体生成的文件（如图片等）"""
        try:
            # 从查询参数中获取签名信息
            timestamp = request.args.get('timestamp')
            nonce = request.args.get('nonce')
            sign = request.args.get('sign')
            
            if not all([timestamp, nonce, sign]):
                return {
                    'code': 400,
                    'message': "缺少必要的签名参数",
                    'data': None
                }, 400
            
            # 构建文件URL，保持查询参数
            file_url = f"{config.DIFY_API_URL}/files/{file_id}/file-preview?timestamp={timestamp}&nonce={nonce}&sign={sign}"
            
            # 获取Dify API请求头
            headers = {
                "Authorization": f"Bearer {config.DIFY_API_KEY}",
                "X-WORKSPACE-ID": config.DIFY_WORKSPACE_ID
            }
            
            # 转发请求到Dify
            response = requests.get(
                file_url,
                headers=headers,
                stream=True,  # 流式传输
                timeout=5
            )
            
            # 检查响应状态
            if response.status_code != 200:
                return {
                    'code': response.status_code,
                    'message': f"获取文件失败: {response.reason}",
                    'data': None
                }, response.status_code
            
            # 创建Flask响应对象并转发内容类型
            return Response(
                response.iter_content(chunk_size=1024),
                content_type=response.headers.get('Content-Type', 'application/octet-stream'),
                status=response.status_code
            )
            
        except Exception as e:
            return {
                'code': 500,
                'message': f"获取智能体文件失败: {str(e)}",
                'data': None
            }, 500

@agent_ns.route('/files/tools/<string:file_id>')
class AgentToolFile(Resource):
    """智能体工具文件代理API"""
    
    @agent_ns.doc(description='代理获取智能体工具生成的文件')
    def get(self, file_id):
        """代理获取智能体工具生成的文件（如图片等）"""
        try:
            # 从查询参数中获取签名信息
            timestamp = request.args.get('timestamp')
            nonce = request.args.get('nonce')
            sign = request.args.get('sign')
            
            # 构建文件URL
            file_url = f"{config.DIFY_API_URL}/files/tools/{file_id}"
            
            # 如果有签名参数，添加到URL
            if all([timestamp, nonce, sign]):
                file_url += f"?timestamp={timestamp}&nonce={nonce}&sign={sign}"
            
            # 获取Dify API请求头
            headers = {
                "Authorization": f"Bearer {config.DIFY_API_KEY}",
                "X-WORKSPACE-ID": config.DIFY_WORKSPACE_ID
            }
            
            # 转发请求到Dify
            response = requests.get(
                file_url,
                headers=headers,
                stream=True,  # 流式传输
                timeout=10
            )
            
            # 检查响应状态
            if response.status_code != 200:
                return {
                    'code': response.status_code,
                    'message': f"获取工具文件失败: {response.reason}",
                    'data': None
                }, response.status_code
            
            # 创建Flask响应对象并转发内容类型
            return Response(
                response.iter_content(chunk_size=1024),
                content_type=response.headers.get('Content-Type', 'image/png'),
                status=response.status_code
            )
            
        except Exception as e:
            return {
                'code': 500,
                'message': f"获取智能体工具文件失败: {str(e)}",
                'data': None
            }, 500

@agent_ns.route('/upload')
class FileUpload(Resource):
    """文件上传API"""
    
    # 定义文件上传解析器
    upload_parser = agent_ns.parser()
    upload_parser.add_argument('file', location='files', type=FileStorage, required=True, help='要上传的文件')
    upload_parser.add_argument('agent_id', type=str, required=True, help='智能体ID', location='form')
    
    @jwt_required()
    @agent_ns.doc(description='上传文件到智能体')
    @agent_ns.expect(upload_parser)
    @agent_ns.response(200, 'Success', file_upload_response_model)
    @agent_ns.response(400, 'Bad Request')
    @agent_ns.response(401, 'Unauthorized')
    @agent_ns.response(500, 'Internal Server Error')
    def post(self):
        """上传文件到智能体"""
        try:
            # 解析请求参数
            args = self.upload_parser.parse_args()
            file = args['file']
            agent_id = args['agent_id']
            
            # 检查文件名是否为空
            if file.filename == '':
                return {
                    'code': 400,
                    'message': '文件名不能为空',
                    'data': None
                }, 400
                
            # 获取当前用户
            current_user = get_current_active_user()
            
            # 获取用户标识
            if hasattr(current_user, 'external_user_id'):
                # 优先使用external_user_id
                user_id = str(current_user.external_user_id)
            else:
                # 回退到user_id
                user_id = str(current_user.user_id)
                
            # 保存文件到临时目录
            import os
            import tempfile
            
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            temp_file_path = os.path.join(temp_dir, file.filename)
            
            # 保存上传的文件到临时文件
            file.save(temp_file_path)
            
            try:
                # 调用服务上传文件
                agent_service = AgentService()
                result = agent_service.upload_file(
                    agent_id=agent_id,
                    file_path=temp_file_path,
                    user_id=user_id,
                    mime_type=file.content_type
                )
                
                # 打印用户ID信息
                print(f"当前用户ID: {user_id}")
                if result.get('data'):
                    print(f"Dify返回的created_by: {result.get('data', {}).get('created_by')}")
                
                # 检查上传结果
                if result.get('code') == 200 and result.get('data'):
                    # 提取文件信息
                    file_data = result.get('data', {})
                    
                    # 构建响应数据
                    response_data = {
                        'code': 200,
                        'message': '文件上传成功',
                        'data': {
                            'id': file_data.get('id', ''),
                            'name': file_data.get('name', ''),
                            'size': file_data.get('size', 0),
                            'extension': file_data.get('extension', ''),
                            'mime_type': file_data.get('mime_type', ''),
                            'created_by': file_data.get('created_by', ''),
                            'created_at': file_data.get('created_at', 0)
                        }
                    }
                    return response_data
                else:
                    # 上传失败
                    return {
                        'code': result.get('code', 500),
                        'message': result.get('message', '文件上传失败'),
                        'data': None
                    }, result.get('code', 500)
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
                    
        except Exception as e:
            return {
                'code': 500,
                'message': f"文件上传失败: {str(e)}",
                'data': None
            }, 500

@agent_ns.route('/<string:agent_id>/parameters')
class AgentParameters(Resource):
    """智能体参数信息API"""
    
    @jwt_required()
    @agent_ns.doc(description='获取智能体参数信息')
    @agent_ns.marshal_with(agent_parameters_response_model)
    def get(self, agent_id):
        """获取智能体参数信息"""
        try:
            # 调用服务获取智能体参数信息
            agent_service = AgentService()
            result = agent_service.get_agent_parameters(agent_id)
            
            if result.get('code') != 200:
                return result, result.get('code', 500)
            
            return result
            
        except Exception as e:
            return {
                'code': 500,
                'message': f"获取智能体参数信息失败: {str(e)}",
                'data': None
            }, 500 