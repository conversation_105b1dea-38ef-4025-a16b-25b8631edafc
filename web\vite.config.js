import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import { visualizer } from 'rollup-plugin-visualizer'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue()
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    proxy: {
      // 将所有 /api 开头的请求代理到后端服务器
      '/api': {
        target: 'http://127.0.0.1:5000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  // 生产环境构建配置
  build: {
    minify: 'terser', // 使用terser进行压缩
    terserOptions: {
      compress: {
        // 不使用drop_console选项，因为它会移除所有console语句
        // drop_console: true,
        // 仅移除指定的console方法
        pure_funcs: ['console.log', 'console.info', 'console.debug']
      }
    }
  }
})
